import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { App } from '../App';
import { AppProvider } from '../providers/AppProvider';

// Mock Tauri API
const mockTauri = {
  invoke: vi.fn(),
  event: {
    listen: vi.fn(),
    emit: vi.fn(),
  },
};

// @ts-ignore
global.__TAURI__ = mockTauri;

const renderApp = () => {
  return render(
    <AppProvider>
      <App />
    </AppProvider>
  );
};

describe('UI Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Layout and Structure', () => {
    it('should render main layout without sidebar', async () => {
      renderApp();
      
      // 应该有顶部栏
      expect(screen.getByRole('banner')).toBeInTheDocument();
      
      // 不应该有侧边栏
      expect(screen.queryByRole('navigation')).not.toBeInTheDocument();
      
      // 应该有主内容区域
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('should have terminal-style dark theme', async () => {
      renderApp();
      
      const body = document.body;
      expect(body).toHaveClass('dark');
    });
  });

  describe('Top Bar Functionality', () => {
    it('should render Agent configuration button', async () => {
      renderApp();
      
      // 查找Agent配置按钮（标识1）
      const agentButton = screen.getByRole('button', { name: /agent/i });
      expect(agentButton).toBeInTheDocument();
    });

    it('should toggle Agent configuration panel', async () => {
      renderApp();
      
      const agentButton = screen.getByRole('button', { name: /agent/i });
      
      // 点击打开面板
      fireEvent.click(agentButton);
      
      await waitFor(() => {
        expect(screen.getByText(/AI模型选择/i)).toBeInTheDocument();
      });
      
      // 再次点击关闭面板
      fireEvent.click(agentButton);
      
      await waitFor(() => {
        expect(screen.queryByText(/AI模型选择/i)).not.toBeInTheDocument();
      });
    });

    it('should not have application info button', async () => {
      renderApp();
      
      // 不应该有应用信息按钮（标识3已移除）
      expect(screen.queryByRole('button', { name: /info/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /about/i })).not.toBeInTheDocument();
    });
  });

  describe('Tab Management', () => {
    it('should render tab bar with new tab button', async () => {
      renderApp();
      
      // 应该有Tab栏
      const tabBar = screen.getByRole('tablist');
      expect(tabBar).toBeInTheDocument();
      
      // 应该有新建Tab按钮（标识2）
      const newTabButton = screen.getByRole('button', { name: /新建标签页/i });
      expect(newTabButton).toBeInTheDocument();
    });

    it('should create new tab when clicking new tab button', async () => {
      renderApp();
      
      const newTabButton = screen.getByRole('button', { name: /新建标签页/i });
      const initialTabs = screen.getAllByRole('tab');
      
      fireEvent.click(newTabButton);
      
      await waitFor(() => {
        const newTabs = screen.getAllByRole('tab');
        expect(newTabs.length).toBe(initialTabs.length + 1);
      });
    });

    it('should switch between tabs', async () => {
      renderApp();
      
      // 创建第二个Tab
      const newTabButton = screen.getByRole('button', { name: /新建标签页/i });
      fireEvent.click(newTabButton);
      
      await waitFor(() => {
        const tabs = screen.getAllByRole('tab');
        expect(tabs.length).toBe(2);
        
        // 点击第一个Tab
        fireEvent.click(tabs[0]);
        
        // 验证Tab切换
        expect(tabs[0]).toHaveAttribute('aria-selected', 'true');
      });
    });

    it('should maintain tab state when switching', async () => {
      renderApp();
      
      // 这个测试验证Tab状态保持功能
      // 由于我们修改了TabManager和TerminalDisplay的逻辑
      // Tab应该在切换时保持状态
      
      const newTabButton = screen.getByRole('button', { name: /新建标签页/i });
      fireEvent.click(newTabButton);
      
      await waitFor(() => {
        const tabs = screen.getAllByRole('tab');
        expect(tabs.length).toBe(2);
        
        // 切换到第一个Tab
        fireEvent.click(tabs[0]);
        
        // 切换回第二个Tab
        fireEvent.click(tabs[1]);
        
        // 验证状态保持（终端内容应该还在）
        expect(tabs[1]).toHaveAttribute('aria-selected', 'true');
      });
    });
  });

  describe('Terminal Display', () => {
    it('should render terminal display area', async () => {
      renderApp();
      
      // 应该有终端显示区域
      const terminalDisplay = screen.getByRole('textbox') || screen.getByTestId('terminal-display');
      expect(terminalDisplay).toBeInTheDocument();
    });

    it('should have terminal-style fonts and colors', async () => {
      renderApp();
      
      const terminalArea = screen.getByRole('main');
      const computedStyle = window.getComputedStyle(terminalArea);
      
      // 验证使用了等宽字体
      expect(computedStyle.fontFamily).toMatch(/mono|consolas|courier/i);
    });
  });

  describe('Agent Configuration Panel', () => {
    it('should render configuration options', async () => {
      renderApp();
      
      const agentButton = screen.getByRole('button', { name: /agent/i });
      fireEvent.click(agentButton);
      
      await waitFor(() => {
        // 验证配置选项
        expect(screen.getByText(/AI模型选择/i)).toBeInTheDocument();
        expect(screen.getByText(/快捷键设置/i)).toBeInTheDocument();
        expect(screen.getByText(/终端偏好/i)).toBeInTheDocument();
      });
    });

    it('should handle configuration changes', async () => {
      renderApp();
      
      const agentButton = screen.getByRole('button', { name: /agent/i });
      fireEvent.click(agentButton);
      
      await waitFor(() => {
        const modelSelect = screen.getByRole('combobox', { name: /模型/i });
        expect(modelSelect).toBeInTheDocument();
        
        // 测试模型选择
        fireEvent.change(modelSelect, { target: { value: 'gpt-4' } });
        expect(modelSelect).toHaveValue('gpt-4');
      });
    });
  });

  describe('Responsive Design', () => {
    it('should adapt to different screen sizes', async () => {
      renderApp();
      
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });
      
      window.dispatchEvent(new Event('resize'));
      
      // 验证响应式布局
      const tabBar = screen.getByRole('tablist');
      expect(tabBar).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      renderApp();
      
      // 验证Tab的ARIA属性
      const tabs = screen.getAllByRole('tab');
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected');
      });
      
      // 验证按钮的ARIA标签
      const newTabButton = screen.getByRole('button', { name: /新建标签页/i });
      expect(newTabButton).toHaveAttribute('title');
    });

    it('should support keyboard navigation', async () => {
      renderApp();
      
      const tabs = screen.getAllByRole('tab');
      
      // 测试键盘导航
      tabs[0].focus();
      fireEvent.keyDown(tabs[0], { key: 'ArrowRight' });
      
      if (tabs[1]) {
        expect(tabs[1]).toHaveFocus();
      }
    });
  });
});
