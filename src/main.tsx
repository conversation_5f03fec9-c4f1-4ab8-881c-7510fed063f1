import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './styles/globals.css';
import './styles/terminal.css';
import './styles/themes.css';

// 初始化主题系统
import { initializeTheme } from './stores/themeStore';

// 初始化主题
initializeTheme();

console.log('main.tsx is loading...');

const rootElement = document.getElementById('root');
console.log('Root element:', rootElement);

if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  console.log('React root created, rendering App...');

  root.render(
    // <React.StrictMode>
    <App />
    // </React.StrictMode>
  );

  console.log('App rendered successfully!');
} else {
  console.error('Root element not found!');
}
