import { create } from 'zustand';

export interface ContextMenuState {
  visible: boolean;
  position: { x: number; y: number };
  menuType: 'terminal' | 'tab' | null;
  contextData: any;

  // 菜单项状态
  hasSelection: boolean;
  canPaste: boolean;
  tabCount: number;
  currentTabIndex: number;

  // 操作方法
  showMenu: (
    type: 'terminal' | 'tab',
    position: { x: number; y: number },
    data?: any
  ) => void;
  hideMenu: () => void;

  // 状态更新方法
  updateMenuState: (state: Partial<ContextMenuState>) => void;
}

export const useContextMenuStore = create<ContextMenuState>(set => ({
  visible: false,
  position: { x: 0, y: 0 },
  menuType: null,
  contextData: null,

  // 菜单项状态
  hasSelection: false,
  canPaste: false,
  tabCount: 1,
  currentTabIndex: 0,

  showMenu: (type, position, data) => {
    // 防止菜单位置超出屏幕
    const adjustedPosition = {
      x: Math.max(0, Math.min(position.x, window.innerWidth - 200)),
      y: Math.max(0, Math.min(position.y, window.innerHeight - 300)),
    };

    set({
      visible: true,
      position: adjustedPosition,
      menuType: type,
      contextData: data,
    });
  },

  hideMenu: () =>
    set({
      visible: false,
      menuType: null,
      contextData: null,
    }),

  updateMenuState: newState =>
    set(state => ({
      ...state,
      ...newState,
    })),
}));
