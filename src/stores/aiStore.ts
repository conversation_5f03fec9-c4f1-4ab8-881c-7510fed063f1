import { invoke } from '@tauri-apps/api/core';
import { create } from 'zustand';

// AI 相关类型定义
export interface AIModel {
  id: string;
  name: string;
  provider: string;
  version: string;
  capabilities: string[];
  max_tokens?: number;
  context_window?: number;
}

export interface AIConfig {
  defaultProvider: string;
  fallbackEnabled: boolean;
  cacheEnabled: boolean;
  timeoutMs: number;
  maxRetries: number;
}

export interface AIContext {
  sessionId: string;
  currentDirectory: string;
  shellType: string;
  environment: Record<string, string>;
  commandHistory: string[];
  userPreferences: UserPreferences;
}

export interface UserPreferences {
  preferredCommands: Record<string, string>;
  riskTolerance: 'Low' | 'Medium' | 'High' | 'Critical';
  confirmationRequired: boolean;
  language: string;
}

export interface AIResponse {
  responseType: 'Command' | 'Explanation' | 'Chat' | 'Error';
  content: string;
  confidence: number;
  suggestedCommands: SuggestedCommand[];
  requiresConfirmation: boolean;
  metadata: ResponseMetadata;
}

export interface SuggestedCommand {
  command: string;
  description: string;
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
  estimatedDuration?: string;
}

export interface ResponseMetadata {
  provider: string;
  model: string;
  timestamp: string;
  processingTimeMs: number;
  cached: boolean;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

export interface ChatContext {
  sessionId: string;
  history: ChatMessage[];
  maxHistory: number;
}

// AI Store 状态接口
interface AIState {
  // 状态
  currentModel: AIModel | null;
  availableModels: AIModel[];
  isProcessing: boolean;
  lastResponse: AIResponse | null;
  chatHistory: ChatMessage[];
  chatContext: ChatContext;
  error: string | null;

  // 配置
  config: AIConfig;

  // 操作方法
  processCommand: (input: string) => Promise<AIResponse>;
  explainCommand: (command: string) => Promise<string>;
  sendChatMessage: (message: string) => Promise<string>;
  switchModel: (modelId: string) => Promise<void>;
  loadAvailableModels: () => Promise<void>;
  updateConfig: (config: Partial<AIConfig>) => Promise<void>;
  healthCheck: () => Promise<boolean>;
  clearChatHistory: () => void;
  clearError: () => void;
}

// 辅助函数：生成AI上下文
const generateAIContext = async (): Promise<AIContext> => {
  try {
    const [currentDirectory, shellType, environment] = await Promise.all([
      invoke<string>('get_current_directory'),
      invoke<string>('get_shell_type'),
      invoke<Record<string, string>>('get_environment_variables'),
    ]);

    return {
      sessionId: crypto.randomUUID(),
      currentDirectory,
      shellType,
      environment,
      commandHistory: [], // TODO: 从终端 store 获取
      userPreferences: {
        preferredCommands: {},
        riskTolerance: 'Medium',
        confirmationRequired: true,
        language: 'zh-CN',
      },
    };
  } catch (error) {
    console.error('Failed to generate AI context:', error);
    // 返回默认上下文
    return {
      sessionId: crypto.randomUUID(),
      currentDirectory: '/',
      shellType: 'bash',
      environment: {},
      commandHistory: [],
      userPreferences: {
        preferredCommands: {},
        riskTolerance: 'Medium',
        confirmationRequired: true,
        language: 'zh-CN',
      },
    };
  }
};

// 创建 AI Store
export const useAIStore = create<AIState>((set, get) => ({
  // 初始状态
  currentModel: null,
  availableModels: [],
  isProcessing: false,
  lastResponse: null,
  chatHistory: [],
  chatContext: {
    sessionId: crypto.randomUUID(),
    history: [],
    maxHistory: 50,
  },
  error: null,

  config: {
    defaultProvider: 'mock',
    fallbackEnabled: true,
    cacheEnabled: true,
    timeoutMs: 30000,
    maxRetries: 3,
  },

  // 处理自然语言命令
  processCommand: async (input: string) => {
    set({ isProcessing: true, error: null });

    try {
      const context = await generateAIContext();

      const response = await invoke<AIResponse>('ai_process_natural_language', {
        input,
        context,
      });

      set({
        lastResponse: response,
        isProcessing: false,
      });

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      set({
        isProcessing: false,
        error: `处理命令失败: ${errorMessage}`,
      });
      throw error;
    }
  },

  // 解释命令
  explainCommand: async (command: string) => {
    set({ error: null });

    try {
      const explanation = await invoke<string>('ai_explain_command', {
        command,
      });
      return explanation;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      set({ error: `解释命令失败: ${errorMessage}` });
      throw error;
    }
  },

  // AI 对话
  sendChatMessage: async (message: string) => {
    const state = get();
    const context = state.chatContext;

    set({ isProcessing: true, error: null });

    try {
      // 添加用户消息到历史
      const userMessage: ChatMessage = {
        role: 'user',
        content: message,
        timestamp: new Date().toISOString(),
      };

      const updatedHistory = [...context.history, userMessage].slice(
        -context.maxHistory
      );
      const updatedContext = { ...context, history: updatedHistory };

      const response = await invoke<string>('ai_chat', {
        message,
        context: updatedContext,
      });

      // 添加助手响应到历史
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response,
        timestamp: new Date().toISOString(),
      };

      const finalHistory = [...updatedHistory, assistantMessage].slice(
        -context.maxHistory
      );

      set(state => ({
        chatHistory: finalHistory,
        chatContext: { ...state.chatContext, history: finalHistory },
        isProcessing: false,
      }));

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      set({
        isProcessing: false,
        error: `聊天失败: ${errorMessage}`,
      });
      throw error;
    }
  },

  // 切换模型
  switchModel: async (modelId: string) => {
    set({ error: null });

    try {
      await invoke('ai_switch_model', { modelId });

      const models = get().availableModels;
      const newModel = models.find(m => m.id === modelId);

      set({ currentModel: newModel || null });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      set({ error: `切换模型失败: ${errorMessage}` });
      throw error;
    }
  },

  // 加载可用模型
  loadAvailableModels: async () => {
    set({ error: null });

    try {
      const models = await invoke<AIModel[]>('ai_get_available_models');

      set({
        availableModels: models,
        currentModel: models.length > 0 ? models[0] : null,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      set({ error: `加载模型列表失败: ${errorMessage}` });
      throw error;
    }
  },

  // 更新配置
  updateConfig: async (newConfig: Partial<AIConfig>) => {
    const state = get();
    const updatedConfig = { ...state.config, ...newConfig };

    set({ error: null });

    try {
      // 转换配置格式以匹配 Rust 结构
      const rustConfig = {
        default_provider: updatedConfig.defaultProvider,
        providers: {
          mock: {
            provider_type: 'Mock',
            model_name: 'mock-model',
            api_endpoint: null,
            api_key: null,
            max_tokens: 1024,
            temperature: 0.7,
            custom_params: {},
          },
        },
        fallback_enabled: updatedConfig.fallbackEnabled,
        cache_enabled: updatedConfig.cacheEnabled,
        timeout_ms: updatedConfig.timeoutMs,
        max_retries: updatedConfig.maxRetries,
      };

      await invoke('ai_update_config', { config: rustConfig });
      set({ config: updatedConfig });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      set({ error: `更新配置失败: ${errorMessage}` });
      throw error;
    }
  },

  // 健康检查
  healthCheck: async () => {
    set({ error: null });

    try {
      await invoke('ai_health_check');
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      set({ error: `健康检查失败: ${errorMessage}` });
      return false;
    }
  },

  // 清除聊天历史
  clearChatHistory: () => {
    set(state => ({
      chatHistory: [],
      chatContext: {
        ...state.chatContext,
        history: [],
        sessionId: crypto.randomUUID(),
      },
    }));
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },
}));

// 导出类型
export type { AIState };
