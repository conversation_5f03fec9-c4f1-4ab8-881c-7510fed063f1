import { create } from 'zustand';

// 简化的应用状态 - 专注于终端功能
interface SimpleAppState {
  theme: 'light' | 'dark';
  isReady: boolean;
  sidebarVisible: boolean;
}

interface SimpleAppActions {
  setTheme: (_theme: 'light' | 'dark') => void;
  setReady: (_ready: boolean) => void;
  setSidebarVisible: (_visible: boolean) => void;
  toggleSidebar: () => void;
}

type SimpleAppStore = SimpleAppState & SimpleAppActions;

export const useAppStore = create<SimpleAppStore>(set => ({
  // Initial State
  theme: 'dark',
  isReady: true,
  sidebarVisible: false,

  // Actions
  setTheme: theme => set({ theme }),
  setReady: isReady => set({ isReady }),
  setSidebarVisible: sidebarVisible => set({ sidebarVisible }),
  toggleSidebar: () => set(state => ({ sidebarVisible: !state.sidebarVisible })),
}));
