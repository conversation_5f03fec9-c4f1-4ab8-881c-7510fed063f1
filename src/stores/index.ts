// Store Configuration and Exports
// This file configures Zustand stores and provides typed access

export { useAIStore } from './aiStore';
export { useAppStore } from './appStore';
export { useHistoryStore } from './historyStore';
export { useSettingsStore } from './settingsStore';
export { SHORTCUT_ACTIONS, useShortcutStore } from './shortcutStore';
export { useTerminalStore } from './terminalStore';

// Store types re-export
export type {
  AIState,
  AppSettings,
  AppState,
  ShortcutState,
  TerminalState,
} from '../types';
