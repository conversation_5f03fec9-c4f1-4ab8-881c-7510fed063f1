import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  Theme,
  ThemeState,
  applyThemeToDOM,
  defaultDarkTheme,
  defaultLightTheme,
  getSystemTheme,
  presetThemes,
} from '../themes';

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentTheme: defaultDarkTheme,
      availableThemes: presetThemes,
      systemTheme: getSystemTheme(),
      autoSwitchEnabled: false,

      // 主题切换
      setTheme: (themeId: string) => {
        const state = get();
        const theme = state.availableThemes.find(t => t.id === themeId);

        if (theme) {
          set({ currentTheme: theme });
          applyThemeToDOM(theme);

          // 触发自定义事件，通知其他组件主题已变更
          window.dispatchEvent(
            new CustomEvent('theme-changed', {
              detail: theme,
            })
          );
        }
      },

      // 添加自定义主题
      addCustomTheme: (theme: Theme) => {
        const state = get();
        const exists = state.availableThemes.some(t => t.id === theme.id);

        if (!exists) {
          set(prevState => ({
            availableThemes: [...prevState.availableThemes, theme],
          }));
        }
      },

      // 删除自定义主题
      removeCustomTheme: (themeId: string) => {
        const state = get();
        // 不允许删除预设主题
        const isPresetTheme = presetThemes.some(t => t.id === themeId);

        if (!isPresetTheme) {
          const newThemes = state.availableThemes.filter(t => t.id !== themeId);
          set({ availableThemes: newThemes });

          // 如果删除的是当前主题，切换到默认主题
          if (state.currentTheme.id === themeId) {
            get().setTheme(defaultDarkTheme.id);
          }
        }
      },

      // 更新自定义主题
      updateCustomTheme: (theme: Theme) => {
        const state = get();
        const themeIndex = state.availableThemes.findIndex(
          t => t.id === theme.id
        );

        if (themeIndex !== -1) {
          const newThemes = [...state.availableThemes];
          newThemes[themeIndex] = theme;
          set({ availableThemes: newThemes });

          // 如果更新的是当前主题，重新应用
          if (state.currentTheme.id === theme.id) {
            set({ currentTheme: theme });
            applyThemeToDOM(theme);
          }
        }
      },

      // 检测系统主题
      detectSystemTheme: () => {
        const systemTheme = getSystemTheme();
        set({ systemTheme });

        // 如果启用自动切换，根据系统主题选择对应主题
        const state = get();
        if (state.autoSwitchEnabled) {
          const targetTheme =
            systemTheme === 'dark' ? defaultDarkTheme : defaultLightTheme;
          get().setTheme(targetTheme.id);
        }
      },

      // 设置自动切换
      setAutoSwitch: (enabled: boolean) => {
        set({ autoSwitchEnabled: enabled });

        if (enabled) {
          // 立即检测并应用系统主题
          get().detectSystemTheme();
        }
      },
    }),
    {
      name: 'theme-storage',
      // 只持久化必要的状态
      partialize: state => ({
        currentTheme: state.currentTheme,
        availableThemes: state.availableThemes.filter(
          theme =>
            // 只保存自定义主题，预设主题每次重新加载
            !presetThemes.some(preset => preset.id === theme.id)
        ),
        autoSwitchEnabled: state.autoSwitchEnabled,
      }),

      // 数据恢复时的处理
      onRehydrateStorage: () => state => {
        if (state) {
          // 合并预设主题和自定义主题
          const customThemes = state.availableThemes || [];
          state.availableThemes = [...presetThemes, ...customThemes];

          // 应用当前主题
          if (state.currentTheme) {
            applyThemeToDOM(state.currentTheme);
          }

          // 如果启用自动切换，检测系统主题
          if (state.autoSwitchEnabled) {
            state.detectSystemTheme();
          }
        }
      },
    }
  )
);

// 主题相关的工具 hooks
export const useTheme = () => {
  const store = useThemeStore();
  return {
    currentTheme: store.currentTheme,
    setTheme: store.setTheme,
  };
};

export const useThemeSelector = () => {
  const store = useThemeStore();
  return {
    availableThemes: store.availableThemes,
    currentTheme: store.currentTheme,
    setTheme: store.setTheme,
    autoSwitchEnabled: store.autoSwitchEnabled,
    setAutoSwitch: store.setAutoSwitch,
  };
};

// 初始化主题系统
export const initializeTheme = () => {
  const store = useThemeStore.getState();

  // 应用当前主题
  applyThemeToDOM(store.currentTheme);

  // 监听系统主题变化
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = () => {
      store.detectSystemTheme();
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    // 返回清理函数
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }
};
