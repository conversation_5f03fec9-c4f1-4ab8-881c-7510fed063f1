import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { AppSettings, DeepPartial } from '../types';

interface SettingsActions {
  updateSettings: (updates: DeepPartial<AppSettings>) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (settingsJson: string) => boolean;
}

type SettingsStore = AppSettings & SettingsActions;

// Default application settings
const defaultSettings: AppSettings = {
  terminal: {
    defaultShell: 'zsh', // Will be detected at runtime by Tauri
    fontSize: 14,
    fontFamily: 'Monaco, "Cascadia Code", "Fira Code", monospace',
    cursorStyle: 'block',
    scrollback: 1000,
    enableBell: false,
  },
  appearance: {
    theme: 'dark',
    colorScheme: 'default',
    opacity: 0.95,
    blur: true,
  },
  ai: {
    defaultProvider: 'local-model',
    autoSuggest: false,
    confirmDangerousCommands: true,
    maxHistoryLength: 100,
  },
  security: {
    enableSafeMode: true,
    dangerousCommands: [
      'rm -rf',
      'sudo rm',
      'del /f',
      'format',
      'fdisk',
      'dd if=',
      'chmod -R 777',
      'chown -R',
    ],
    requireConfirmation: true,
    auditLog: true,
  },
  shortcuts: {
    newTab: 'CmdOrCtrl+T',
    closeTab: 'CmdOrCtrl+W',
    nextTab: 'CmdOrCtrl+Tab',
    previousTab: 'CmdOrCtrl+Shift+Tab',
    toggleSettings: 'CmdOrCtrl+,',
  },
};

// Deep merge utility function
const deepMerge = <T extends Record<string, any>>(
  target: T,
  source: DeepPartial<T>
): T => {
  const result = { ...target };

  for (const key in source) {
    if (source[key] !== undefined) {
      if (
        typeof source[key] === 'object' &&
        source[key] !== null &&
        !Array.isArray(source[key])
      ) {
        result[key] = deepMerge(result[key] as any, source[key] as any);
      } else {
        result[key] = source[key] as any;
      }
    }
  }

  return result;
};

export const useSettingsStore = create<SettingsStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State (default settings)
        ...defaultSettings,

        // Settings Management Actions
        updateSettings: (updates: DeepPartial<AppSettings>) => {
          set(state => {
            const newSettings = deepMerge(
              {
                terminal: state.terminal,
                appearance: state.appearance,
                ai: state.ai,
                security: state.security,
                shortcuts: state.shortcuts,
              },
              updates
            );
            return newSettings;
          });
        },

        resetSettings: () => {
          set(defaultSettings);
        },

        exportSettings: () => {
          const state = get();
          const settingsData = {
            terminal: state.terminal,
            appearance: state.appearance,
            ai: state.ai,
            security: state.security,
            shortcuts: state.shortcuts,
          };
          return JSON.stringify(settingsData, null, 2);
        },

        importSettings: (settingsJson: string) => {
          try {
            const importedSettings = JSON.parse(
              settingsJson
            ) as DeepPartial<AppSettings>;

            // Validate imported settings structure
            if (
              typeof importedSettings !== 'object' ||
              importedSettings === null
            ) {
              return false;
            }

            // Apply imported settings
            get().updateSettings(importedSettings);
            return true;
          } catch (error) {
            console.error('Failed to import settings:', error);
            return false;
          }
        },
      }),
      {
        name: 'tagent-settings',
        version: 1,
        // Only persist the settings data, not the actions
        partialize: state => ({
          terminal: state.terminal,
          appearance: state.appearance,
          ai: state.ai,
          security: state.security,
          shortcuts: state.shortcuts,
        }),
      }
    ),
    {
      name: 'settings-store',
    }
  )
);

// Helper functions for specific setting updates
export const updateTerminalSettings = (
  updates: DeepPartial<AppSettings['terminal']>
) => {
  useSettingsStore.getState().updateSettings({ terminal: updates });
};

export const updateAppearanceSettings = (
  updates: DeepPartial<AppSettings['appearance']>
) => {
  useSettingsStore.getState().updateSettings({ appearance: updates });
};

export const updateAISettings = (updates: DeepPartial<AppSettings['ai']>) => {
  useSettingsStore.getState().updateSettings({ ai: updates });
};

export const updateSecuritySettings = (
  updates: DeepPartial<AppSettings['security']>
) => {
  useSettingsStore.getState().updateSettings({ security: updates });
};

export const updateShortcutSettings = (
  updates: DeepPartial<AppSettings['shortcuts']>
) => {
  useSettingsStore.getState().updateSettings({ shortcuts: updates });
};
