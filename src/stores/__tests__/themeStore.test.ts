import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  defaultDarkTheme,
  defaultLightTheme,
  presetThemes,
} from '../../themes';
import { useThemeStore } from '../themeStore';

// Mock DOM manipulation functions
vi.mock('../../themes', async () => {
  const actual = (await vi.importActual('../../themes')) as any;
  return {
    ...actual,
    applyThemeToDOM: vi.fn(),
    getSystemTheme: vi.fn(() => 'dark'),
  };
});

describe('themeStore', () => {
  beforeEach(() => {
    // 清理 localStorage
    localStorage.clear();

    // 重置 store 到初始状态
    useThemeStore.setState({
      currentTheme: defaultDarkTheme,
      availableThemes: presetThemes,
      systemTheme: 'dark',
      autoSwitchEnabled: false,
    });
  });

  describe('初始状态', () => {
    it('应该有正确的默认值', () => {
      const { result } = renderHook(() => useThemeStore());

      expect(result.current.currentTheme).toEqual(defaultDarkTheme);
      expect(result.current.systemTheme).toBe('dark');
      expect(result.current.autoSwitchEnabled).toBe(false);
      expect(Array.isArray(result.current.availableThemes)).toBe(true);
    });
  });

  describe('setTheme', () => {
    it('应该能够设置存在的主题', () => {
      const { result } = renderHook(() => useThemeStore());

      act(() => {
        result.current.setTheme(defaultLightTheme.id);
      });

      expect(result.current.currentTheme).toEqual(defaultLightTheme);
    });

    it('设置不存在的主题应该不改变当前主题', () => {
      const { result } = renderHook(() => useThemeStore());
      const initialTheme = result.current.currentTheme;

      act(() => {
        result.current.setTheme('non-existent-theme');
      });

      expect(result.current.currentTheme).toEqual(initialTheme);
    });
  });

  describe('addCustomTheme', () => {
    const mockTheme = {
      id: 'custom-theme-1',
      name: 'Custom Theme',
      type: 'dark' as const,
      colors: {
        primary: '#ff0000',
        secondary: '#00ff00',
        background: '#000000',
        surface: '#111111',
        text: '#ffffff',
        textSecondary: '#cccccc',
        border: '#333333',
        terminal: {
          background: '#000000',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selection: '#444444',
          ansi: Array(16).fill('#ffffff'),
        },
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000',
        info: '#0000ff',
        hover: '#222222',
        active: '#333333',
        focus: '#444444',
        disabled: '#666666',
      },
      typography: {
        fontFamily: 'monospace',
        fontSize: 14,
        lineHeight: 1.5,
        fontWeight: 400,
      },
      effects: {
        borderRadius: 4,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        opacity: {
          disabled: 0.5,
          hover: 0.8,
        },
        transition: {
          duration: '0.2s',
          easing: 'ease-in-out',
        },
      },
    };

    it('应该能够添加自定义主题', () => {
      const { result } = renderHook(() => useThemeStore());
      const initialCount = result.current.availableThemes.length;

      act(() => {
        result.current.addCustomTheme(mockTheme);
      });

      expect(result.current.availableThemes).toHaveLength(initialCount + 1);
      expect(
        result.current.availableThemes.find(t => t.id === mockTheme.id)
      ).toEqual(mockTheme);
    });

    it('重复添加相同ID的主题应该不会重复', () => {
      const { result } = renderHook(() => useThemeStore());

      act(() => {
        result.current.addCustomTheme(mockTheme);
        result.current.addCustomTheme(mockTheme);
      });

      const matchingThemes = result.current.availableThemes.filter(
        t => t.id === mockTheme.id
      );
      expect(matchingThemes).toHaveLength(1);
    });
  });

  describe('removeCustomTheme', () => {
    const mockTheme = {
      id: 'custom-theme-2',
      name: 'Custom Theme 2',
      type: 'light' as const,
      colors: {
        primary: '#000000',
        secondary: '#333333',
        background: '#ffffff',
        surface: '#f5f5f5',
        text: '#000000',
        textSecondary: '#666666',
        border: '#cccccc',
        terminal: {
          background: '#ffffff',
          foreground: '#000000',
          cursor: '#000000',
          selection: '#cccccc',
          ansi: Array(16).fill('#000000'),
        },
        success: '#008000',
        warning: '#ffa500',
        error: '#ff0000',
        info: '#0000ff',
        hover: '#eeeeee',
        active: '#dddddd',
        focus: '#bbbbbb',
        disabled: '#999999',
      },
      typography: {
        fontFamily: 'monospace',
        fontSize: 14,
        lineHeight: 1.5,
        fontWeight: 400,
      },
      effects: {
        borderRadius: 4,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        opacity: {
          disabled: 0.5,
          hover: 0.8,
        },
        transition: {
          duration: '0.2s',
          easing: 'ease-in-out',
        },
      },
    };

    it('应该能够删除自定义主题', () => {
      const { result } = renderHook(() => useThemeStore());

      act(() => {
        result.current.addCustomTheme(mockTheme);
      });

      expect(
        result.current.availableThemes.find(t => t.id === mockTheme.id)
      ).toBeDefined();

      act(() => {
        result.current.removeCustomTheme(mockTheme.id);
      });

      expect(
        result.current.availableThemes.find(t => t.id === mockTheme.id)
      ).toBeUndefined();
    });

    it('删除当前使用的主题应该切换到默认主题', () => {
      const { result } = renderHook(() => useThemeStore());

      act(() => {
        result.current.addCustomTheme(mockTheme);
        result.current.setTheme(mockTheme.id);
      });

      expect(result.current.currentTheme.id).toBe(mockTheme.id);

      act(() => {
        result.current.removeCustomTheme(mockTheme.id);
      });

      expect(result.current.currentTheme.id).toBe(defaultDarkTheme.id);
    });

    it('删除不存在的主题应该不报错', () => {
      const { result } = renderHook(() => useThemeStore());

      expect(() => {
        act(() => {
          result.current.removeCustomTheme('non-existent-theme');
        });
      }).not.toThrow();
    });
  });

  describe('setAutoSwitch', () => {
    it('应该能够启用自动切换', () => {
      const { result } = renderHook(() => useThemeStore());

      act(() => {
        result.current.setAutoSwitch(true);
      });

      expect(result.current.autoSwitchEnabled).toBe(true);
    });

    it('应该能够禁用自动切换', () => {
      const { result } = renderHook(() => useThemeStore());

      act(() => {
        result.current.setAutoSwitch(true);
        result.current.setAutoSwitch(false);
      });

      expect(result.current.autoSwitchEnabled).toBe(false);
    });
  });

  describe('detectSystemTheme', () => {
    it('应该能够检测系统主题', () => {
      const { result } = renderHook(() => useThemeStore());

      act(() => {
        result.current.detectSystemTheme();
      });

      // 由于我们 mock 了 getSystemTheme 返回 'dark'
      expect(result.current.systemTheme).toBe('dark');
    });
  });

  describe('状态持久化', () => {
    it('应该维护状态的响应式', () => {
      const { result } = renderHook(() => useThemeStore());

      const initialTheme = result.current.currentTheme;

      act(() => {
        result.current.setTheme(defaultLightTheme.id);
      });

      expect(result.current.currentTheme).not.toEqual(initialTheme);
      expect(result.current.currentTheme.id).toBe(defaultLightTheme.id);
    });
  });
});
