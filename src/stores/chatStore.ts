import { invoke } from '@tauri-apps/api/core';
import { create } from 'zustand';
import type { ChatMessage } from '../components/chat/types';

interface ChatState {
  // 对话状态
  isActive: boolean;
  messages: ChatMessage[];
  currentConversationId: string;
  isLoading: boolean;
  contextHistory: string[];

  // 操作方法
  startChat: () => void;
  endChat: () => void;
  sendMessage: (content: string) => Promise<void>;
  clearHistory: () => void;
  addContext: (context: string) => void;

  // 内部方法
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
}

export const useChatStore = create<ChatState>((set, get) => ({
  // 初始状态
  isActive: false,
  messages: [],
  currentConversationId: '',
  isLoading: false,
  contextHistory: [],

  // 开始对话
  startChat: () => {
    set({
      isActive: true,
      currentConversationId: crypto.randomUUID(),
      messages: [],
    });
  },

  // 结束对话
  endChat: () => {
    set({
      isActive: false,
      currentConversationId: '',
    });
  },

  // 发送消息
  sendMessage: async (content: string) => {
    const state = get();

    // 检查退出命令
    if (['exit', 'quit', '@end'].includes(content.toLowerCase().trim())) {
      state.endChat();
      state.addMessage({
        type: 'assistant',
        content: '对话模式已结束。输入 @model 可重新开始对话。',
      });
      return;
    }

    // 添加用户消息
    state.addMessage({
      type: 'user',
      content,
    });

    set({ isLoading: true });

    try {
      // 构建上下文
      const context = {
        sessionId: state.currentConversationId,
        history: state.messages.slice(-10), // 保留最近10条消息作为上下文
        contextHistory: state.contextHistory,
      };

      // 调用AI服务
      const response = await invoke<string>('ai_chat_with_context', {
        message: content,
        context,
      });

      // 添加AI回复
      state.addMessage({
        type: 'assistant',
        content: response,
      });
    } catch (error) {
      console.error('AI对话失败:', error);

      // 添加错误消息
      state.addMessage({
        type: 'assistant',
        content: `抱歉，AI服务暂时不可用。错误信息：${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          riskLevel: 'Low',
        },
      });
    } finally {
      set({ isLoading: false });
    }
  },

  // 清空历史
  clearHistory: () => {
    set({
      messages: [],
      contextHistory: [],
    });
  },

  // 添加上下文
  addContext: (context: string) => {
    set(state => ({
      contextHistory: [...state.contextHistory, context].slice(-20), // 保留最近20条上下文
    }));
  },

  // 内部方法：添加消息
  addMessage: (messageData: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const message: ChatMessage = {
      ...messageData,
      id: crypto.randomUUID(),
      timestamp: Date.now(),
    };

    set(state => ({
      messages: [...state.messages, message],
    }));
  },
}));
