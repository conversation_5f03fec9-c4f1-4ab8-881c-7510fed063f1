import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import type { CommandHistory, TerminalState, TerminalTab } from '../types';

interface TerminalActions {
  // Tab Management
  createTab: (shell?: string, workingDirectory?: string) => string;
  closeTab: (tabId: string) => void;
  setActiveTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<TerminalTab>) => void;

  // Command History
  addCommand: (command: CommandHistory) => void;
  clearHistory: (terminalId?: string) => void;

  // State Management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Utility
  getActiveTab: () => TerminalTab | null;
  getTabById: (tabId: string) => TerminalTab | null;
}

type TerminalStore = TerminalState & TerminalActions;

const generateId = () =>
  `terminal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export const useTerminalStore = create<TerminalStore>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // Initial State
      tabs: [],
      activeTabId: null,
      history: [],
      isLoading: false,
      error: null,

      // Tab Management Actions
      createTab: (shell = 'zsh', workingDirectory = '~') => {
        const id = generateId();
        const newTab: TerminalTab = {
          id,
          title: `Terminal ${get().tabs.length + 1}`,
          shell,
          currentDirectory: workingDirectory,
          isActive: false,
          createdAt: new Date(),
          lastActivity: new Date(),
        };

        set(state => ({
          tabs: [...state.tabs, newTab],
          activeTabId: state.activeTabId || id, // Set as active if it's the first tab
        }));

        return id;
      },

      closeTab: (tabId: string) => {
        set(state => {
          const newTabs = state.tabs.filter(tab => tab.id !== tabId);
          let newActiveTabId = state.activeTabId;

          // If closing the active tab, switch to another tab
          if (state.activeTabId === tabId) {
            newActiveTabId = newTabs.length > 0 ? newTabs[0].id : null;
          }

          return {
            tabs: newTabs,
            activeTabId: newActiveTabId,
            history: state.history.filter(cmd => cmd.terminalId !== tabId),
          };
        });
      },

      setActiveTab: (tabId: string) => {
        set(state => {
          // Update active status for all tabs
          const updatedTabs = state.tabs.map(tab => ({
            ...tab,
            isActive: tab.id === tabId,
          }));

          return {
            tabs: updatedTabs,
            activeTabId: tabId,
          };
        });
      },

      updateTab: (tabId: string, updates: Partial<TerminalTab>) => {
        set(state => ({
          tabs: state.tabs.map(tab =>
            tab.id === tabId
              ? { ...tab, ...updates, lastActivity: new Date() }
              : tab
          ),
        }));
      },

      // Command History Actions
      addCommand: (command: CommandHistory) => {
        set(state => ({
          history: [...state.history, command].slice(-1000), // Keep last 1000 commands
        }));
      },

      clearHistory: (terminalId?: string) => {
        set(state => ({
          history: terminalId
            ? state.history.filter(cmd => cmd.terminalId !== terminalId)
            : [],
        }));
      },

      // State Management Actions
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // Utility Actions
      getActiveTab: () => {
        const state = get();
        return state.tabs.find(tab => tab.id === state.activeTabId) || null;
      },

      getTabById: (tabId: string) => {
        const state = get();
        return state.tabs.find(tab => tab.id === tabId) || null;
      },
    })),
    {
      name: 'terminal-store',
    }
  )
);
