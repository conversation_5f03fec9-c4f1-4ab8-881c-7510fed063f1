// Command History Store - Manages command history state and actions
// Provides history loading, searching, navigation, and execution functionality

import { invoke } from '@tauri-apps/api/core';
import { create } from 'zustand';
import { HistoryEntry, HistoryStore } from '../types/history';

// Initial state
const initialState = {
  entries: [],
  filteredEntries: [],
  searchQuery: '',
  isLoading: false,
  error: null,
  stats: null,
  selectedEntry: null,
  currentIndex: -1,
};

export const useHistoryStore = create<HistoryStore>((set, get) => ({
  ...initialState,

  // Load command history for a terminal
  loadHistory: async (terminalId: string, limit = 100) => {
    set({ isLoading: true, error: null });

    try {
      const entries: HistoryEntry[] = await invoke(
        'get_advanced_command_history',
        {
          terminalId,
          limit,
        }
      );

      set({
        entries,
        filteredEntries: entries,
        isLoading: false,
        currentIndex: entries.length > 0 ? entries.length - 1 : -1,
      });
    } catch (error) {
      console.error('Failed to load command history:', error);
      set({
        error:
          error instanceof Error ? error.message : 'Failed to load history',
        isLoading: false,
      });
    }
  },

  // Search command history
  searchHistory: async (terminalId: string, query: string, limit = 50) => {
    if (!query.trim()) {
      get().clearSearch();
      return;
    }

    set({ isLoading: true, error: null, searchQuery: query });

    try {
      const filteredEntries: HistoryEntry[] = await invoke(
        'search_advanced_command_history',
        {
          terminalId: terminalId,
          query: query.trim(),
          limit,
        }
      );

      set({
        filteredEntries,
        isLoading: false,
        currentIndex:
          filteredEntries.length > 0 ? filteredEntries.length - 1 : -1,
      });
    } catch (error) {
      console.error('Failed to search command history:', error);
      set({
        error:
          error instanceof Error ? error.message : 'Failed to search history',
        isLoading: false,
      });
    }
  },

  // Set search query without triggering search
  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  // Clear search and show all entries
  clearSearch: () => {
    const { entries } = get();
    set({
      searchQuery: '',
      filteredEntries: entries,
      currentIndex: entries.length > 0 ? entries.length - 1 : -1,
    });
  },

  // Select a history entry
  selectEntry: (entry: HistoryEntry | null) => {
    set({ selectedEntry: entry });
  },

  // Navigate through history (for arrow key navigation)
  navigateHistory: (direction: 'up' | 'down'): HistoryEntry | null => {
    const { filteredEntries, currentIndex } = get();

    if (filteredEntries.length === 0) {
      return null;
    }

    let newIndex = currentIndex;

    if (direction === 'up') {
      // Move to previous command (older)
      newIndex = currentIndex > 0 ? currentIndex - 1 : 0;
    } else {
      // Move to next command (newer)
      newIndex =
        currentIndex < filteredEntries.length - 1
          ? currentIndex + 1
          : filteredEntries.length - 1;
    }

    set({ currentIndex: newIndex });

    const selectedEntry = filteredEntries[newIndex];
    set({ selectedEntry });

    return selectedEntry;
  },

  // Execute a history command (emit to terminal)
  executeHistoryCommand: (entry: HistoryEntry) => {
    // This will be handled by the parent component that uses the store
    // We just dispatch a custom event that the terminal can listen for
    const event = new CustomEvent('executeHistoryCommand', {
      detail: { command: entry.command.raw_input, entry },
    });
    window.dispatchEvent(event);

    // Update selected entry
    set({ selectedEntry: entry });
  },

  // Clear all history data
  clearHistory: () => {
    set({
      entries: [],
      filteredEntries: [],
      searchQuery: '',
      selectedEntry: null,
      currentIndex: -1,
      stats: null,
    });
  },

  // Reset store to initial state
  resetState: () => {
    set({
      ...initialState,
    });
  },
}));

// Utility functions for history management
export const historyUtils = {
  // Format command for display
  formatCommand: (entry: HistoryEntry): string => {
    return entry.command.raw_input;
  },

  // Format timestamp for display
  formatTimestamp: (timestamp: string): string => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  },

  // Format duration for display
  formatDuration: (duration: number | null): string => {
    if (duration === null) return 'N/A';

    if (duration < 1000) {
      return `${duration}ms`;
    } else if (duration < 60000) {
      return `${(duration / 1000).toFixed(1)}s`;
    } else {
      const minutes = Math.floor(duration / 60000);
      const seconds = Math.floor((duration % 60000) / 1000);
      return `${minutes}m ${seconds}s`;
    }
  },

  // Get exit code status
  getExitCodeStatus: (
    exitCode: number | null
  ): 'success' | 'error' | 'unknown' => {
    if (exitCode === null) return 'unknown';
    return exitCode === 0 ? 'success' : 'error';
  },

  // Filter sensitive commands (basic implementation)
  isSensitiveCommand: (command: string): boolean => {
    const sensitivePatterns = [
      /password/i,
      /token/i,
      /key/i,
      /secret/i,
      /auth/i,
      /login/i,
    ];

    return sensitivePatterns.some(pattern => pattern.test(command));
  },

  // Group history entries by date
  groupByDate: (entries: HistoryEntry[]): Record<string, HistoryEntry[]> => {
    return entries.reduce(
      (groups, entry) => {
        const date = new Date(entry.timestamp).toDateString();
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(entry);
        return groups;
      },
      {} as Record<string, HistoryEntry[]>
    );
  },
};
