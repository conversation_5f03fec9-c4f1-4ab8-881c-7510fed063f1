import { getDefaultShortcuts } from '@/constants/shortcuts';
import { Platform, ShortcutConfig, ShortcutState } from '@/types';
import {
  checkShortcutConflict,
  detectPlatform,
  validateShortcutKeys,
} from '@/utils/shortcutUtils';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ShortcutStore extends ShortcutState {
  // 状态
  shortcuts: Record<string, ShortcutConfig>;
  isEnabled: boolean;
  platform: Platform;

  // 快捷键管理方法
  setShortcut: (action: string, config: ShortcutConfig) => void;
  removeShortcut: (action: string) => void;
  resetShortcuts: () => void;
  enableShortcuts: (enabled: boolean) => void;

  // 工具方法
  checkConflict: (keys: string[], excludeAction?: string) => string[];
  getCurrentPlatform: () => Platform;
  getShortcutByAction: (action: string) => ShortcutConfig | undefined;
  getShortcutsByCategory: (category: string) => Record<string, ShortcutConfig>;

  // 初始化方法
  initializeShortcuts: () => void;

  // 配置导入导出
  exportConfig: () => string;
  importConfig: (configJson: string) => { success: boolean; error?: string };
}

export const useShortcutStore = create<ShortcutStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      shortcuts: {},
      isEnabled: true,
      platform: detectPlatform(),

      // 设置快捷键
      setShortcut: (action: string, config: ShortcutConfig) => {
        const state = get();

        // 验证快捷键有效性
        const validation = validateShortcutKeys(config.keys);
        if (!validation.valid) {
          console.warn(
            `Invalid shortcut keys for action ${action}:`,
            validation.error
          );
          return;
        }

        // 检查冲突
        const conflicts = state.checkConflict(config.keys, action);
        if (conflicts.length > 0) {
          console.warn(
            `Shortcut conflict detected for action ${action}:`,
            conflicts
          );
          return;
        }

        set(state => ({
          shortcuts: {
            ...state.shortcuts,
            [action]: {
              ...config,
              action, // 确保action字段正确
            },
          },
        }));
      },

      // 移除快捷键
      removeShortcut: (action: string) => {
        set(state => {
          const { [action]: removed, ...rest } = state.shortcuts;
          return { shortcuts: rest };
        });
      },

      // 重置为默认快捷键
      resetShortcuts: () => {
        const platform = get().platform;
        const defaultShortcuts = getDefaultShortcuts(platform);

        set({ shortcuts: defaultShortcuts });
      },

      // 启用/禁用快捷键
      enableShortcuts: (enabled: boolean) => {
        set({ isEnabled: enabled });
      },

      // 检查快捷键冲突
      checkConflict: (keys: string[], excludeAction?: string) => {
        const state = get();
        return checkShortcutConflict(keys, state.shortcuts, excludeAction);
      },

      // 获取当前平台
      getCurrentPlatform: () => {
        return get().platform;
      },

      // 根据动作获取快捷键配置
      getShortcutByAction: (action: string) => {
        const state = get();
        return state.shortcuts[action];
      },

      // 根据类别获取快捷键
      getShortcutsByCategory: (category: string) => {
        const state = get();
        return Object.fromEntries(
          Object.entries(state.shortcuts).filter(
            ([, config]) => config.category === category
          )
        );
      },

      // 初始化快捷键配置
      initializeShortcuts: () => {
        const state = get();

        // 如果没有快捷键配置，使用默认配置
        if (Object.keys(state.shortcuts).length === 0) {
          const defaultShortcuts = getDefaultShortcuts(state.platform);
          set({ shortcuts: defaultShortcuts });
        }
      },

      // 导出配置
      exportConfig: () => {
        const state = get();
        const config = {
          shortcuts: state.shortcuts,
          platform: state.platform,
          exportTime: new Date().toISOString(),
          version: '1.0.0',
        };
        return JSON.stringify(config, null, 2);
      },

      // 导入配置
      importConfig: (configJson: string) => {
        try {
          const config = JSON.parse(configJson);

          // 验证配置格式
          if (!config.shortcuts || typeof config.shortcuts !== 'object') {
            return { success: false, error: '无效的配置格式' };
          }

          // 验证每个快捷键配置
          const validShortcuts: Record<string, ShortcutConfig> = {};
          let invalidCount = 0;

          Object.entries(config.shortcuts).forEach(([action, shortcut]) => {
            const sc = shortcut as ShortcutConfig;
            const validation = validateShortcutKeys(sc.keys);

            if (validation.valid) {
              validShortcuts[action] = sc;
            } else {
              invalidCount++;
              console.warn(
                `Invalid shortcut for action ${action}:`,
                validation.error
              );
            }
          });

          set({ shortcuts: validShortcuts });

          if (invalidCount > 0) {
            return {
              success: true,
              error: `已导入配置，但跳过了 ${invalidCount} 个无效快捷键`,
            };
          }

          return { success: true };
        } catch (error) {
          return {
            success: false,
            error: `解析配置文件失败: ${error instanceof Error ? error.message : '未知错误'}`,
          };
        }
      },
    }),
    {
      name: 'shortcut-storage',
      version: 1,
      // 只持久化必要的状态
      partialize: state => ({
        shortcuts: state.shortcuts,
        isEnabled: state.isEnabled,
        platform: state.platform,
      }),
      // 从存储恢复时的迁移逻辑
      migrate: (persistedState: any, version: number) => {
        if (version === 0) {
          // 从版本0迁移：添加平台信息
          return {
            ...persistedState,
            platform: detectPlatform(),
          };
        }
        return persistedState;
      },
    }
  )
);

// 导出快捷键动作常量
export const SHORTCUT_ACTIONS = {
  // Tab管理
  CREATE_TAB: 'createTab',
  CLOSE_TAB: 'closeTab',
  NEXT_TAB: 'nextTab',
  PREV_TAB: 'prevTab',
  SWITCH_TO_TAB: 'switchToTab',

  // 终端操作
  COPY_SELECTION: 'copySelection',
  PASTE_TEXT: 'pasteText',
  SELECT_ALL: 'selectAll',
  CLEAR_TERMINAL: 'clearTerminal',
  INTERRUPT_PROCESS: 'interruptProcess',
  OPEN_SEARCH: 'openSearch',

  // AI功能
  OPEN_AI_CHAT: 'openAIChat',
  EXECUTE_AI_COMMAND: 'executeAICommand',
  TOGGLE_AI_CHAT: 'toggleAIChat',

  // 应用功能
  OPEN_SETTINGS: 'openSettings',
  TOGGLE_THEME: 'toggleTheme',
  SHOW_HELP: 'showHelp',
  TOGGLE_APP: 'toggleApp',
  NEW_WINDOW: 'newWindow',
} as const;

export type ShortcutAction =
  (typeof SHORTCUT_ACTIONS)[keyof typeof SHORTCUT_ACTIONS];
