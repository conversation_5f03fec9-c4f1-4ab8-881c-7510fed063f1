import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ThemeProvider } from '../../components/ui/ThemeProvider';

// Mock AI 服务
const mockAIService = {
  convertCommand: vi.fn(),
  chatWithModel: vi.fn(),
  isConnected: vi.fn(),
  getAvailableModels: vi.fn(),
};

vi.mock('../../services/ai/AIService', () => ({
  AIService: {
    getInstance: () => mockAIService,
  },
}));

// Mock 设置存储
const mockSettingsStore = {
  aiSettings: {
    provider: 'deepseek',
    apiKey: 'test-api-key',
    model: 'deepseek-coder',
    enabled: true,
  },
  updateAISettings: vi.fn(),
};

vi.mock('../../stores/settingsStore', () => ({
  useSettingsStore: () => mockSettingsStore,
}));

// 简单的测试组件
const AITestContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider>
      <div data-testid="ai-test-container">{children}</div>
    </ThemeProvider>
  );
};

// 模拟AI命令输入组件
const MockAICommandInput = () => {
  const handleSubmit = async (command: string) => {
    const result = await mockAIService.convertCommand(command);
    console.log('Command result:', result);
  };

  return (
    <div data-testid="ai-command-input">
      <input
        data-testid="command-input"
        placeholder="输入自然语言命令..."
        onKeyDown={e => {
          if (e.key === 'Enter') {
            handleSubmit((e.target as HTMLInputElement).value);
          }
        }}
      />
    </div>
  );
};

describe('AI服务系统集成测试', () => {
  beforeEach(() => {
    // 清理 mock 状态
    vi.clearAllMocks();

    // 设置默认的 mock 返回值
    mockAIService.isConnected.mockReturnValue(true);
    mockAIService.getAvailableModels.mockResolvedValue([
      { id: 'deepseek-coder', name: 'DeepSeek Coder', provider: 'deepseek' },
      { id: 'deepseek-chat', name: 'DeepSeek Chat', provider: 'deepseek' },
    ]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('AI服务初始化', () => {
    it('应该正确初始化AI服务', () => {
      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      expect(screen.getByTestId('ai-test-container')).toBeInTheDocument();
      expect(screen.getByTestId('ai-command-input')).toBeInTheDocument();
    });

    it('应该检查AI服务连接状态', () => {
      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      expect(mockAIService.isConnected()).toBe(true);
    });

    it('应该获取可用的AI模型列表', async () => {
      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      const models = await mockAIService.getAvailableModels();

      expect(models).toHaveLength(2);
      expect(models[0]).toHaveProperty('id', 'deepseek-coder');
      expect(models[1]).toHaveProperty('id', 'deepseek-chat');
    });
  });

  describe('@command 自然语言转命令', () => {
    it('应该将简单的自然语言转换为命令', async () => {
      const user = userEvent.setup();
      mockAIService.convertCommand.mockResolvedValue({
        success: true,
        command: 'ls -la',
        explanation: '列出当前目录下的所有文件和文件夹，包括隐藏文件',
        confidence: 0.95,
      });

      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      const input = screen.getByTestId('command-input');
      await user.type(input, '显示当前目录的所有文件');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockAIService.convertCommand).toHaveBeenCalledWith(
          '显示当前目录的所有文件'
        );
      });
    });

    it('应该处理复杂的文件操作命令', async () => {
      const user = userEvent.setup();
      mockAIService.convertCommand.mockResolvedValue({
        success: true,
        command: 'find . -name "*.txt" -type f',
        explanation: '在当前目录及子目录中查找所有.txt文件',
        confidence: 0.88,
      });

      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      const input = screen.getByTestId('command-input');
      await user.type(input, '找到所有的txt文件');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockAIService.convertCommand).toHaveBeenCalledWith(
          '找到所有的txt文件'
        );
      });
    });

    it('应该处理Git相关命令', async () => {
      const user = userEvent.setup();
      mockAIService.convertCommand.mockResolvedValue({
        success: true,
        command: 'git status',
        explanation: '查看Git仓库的当前状态',
        confidence: 0.92,
      });

      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      const input = screen.getByTestId('command-input');
      await user.type(input, '查看git状态');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockAIService.convertCommand).toHaveBeenCalledWith(
          '查看git状态'
        );
      });
    });

    it('应该处理系统信息查询命令', async () => {
      const user = userEvent.setup();
      mockAIService.convertCommand.mockResolvedValue({
        success: true,
        command: 'ps aux | grep node',
        explanation: '查找所有正在运行的Node.js进程',
        confidence: 0.85,
      });

      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      const input = screen.getByTestId('command-input');
      await user.type(input, '找到运行中的node进程');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockAIService.convertCommand).toHaveBeenCalledWith(
          '找到运行中的node进程'
        );
      });
    });
  });

  describe('@model AI对话功能', () => {
    it('应该处理简单的对话', async () => {
      mockAIService.chatWithModel.mockResolvedValue({
        success: true,
        response: '你好！我是DeepSeek AI助手，很高兴为你服务。',
        model: 'deepseek-chat',
        usage: { tokens: 25 },
      });

      const response = await mockAIService.chatWithModel(
        '你好',
        'deepseek-chat'
      );

      expect(response.success).toBe(true);
      expect(response.response).toContain('你好');
      expect(response.model).toBe('deepseek-chat');
    });

    it('应该处理编程相关的问题', async () => {
      mockAIService.chatWithModel.mockResolvedValue({
        success: true,
        response:
          '这是一个React函数组件的示例：\n\n```jsx\nfunction MyComponent() {\n  return <div>Hello World</div>;\n}\n```',
        model: 'deepseek-coder',
        usage: { tokens: 48 },
      });

      const response = await mockAIService.chatWithModel(
        '如何创建一个React组件？',
        'deepseek-coder'
      );

      expect(response.success).toBe(true);
      expect(response.response).toContain('React');
      expect(response.response).toContain('```jsx');
    });

    it('应该处理技术问题解答', async () => {
      mockAIService.chatWithModel.mockResolvedValue({
        success: true,
        response:
          'TypeScript是JavaScript的超集，它添加了静态类型检查。主要优势包括：\n1. 更好的IDE支持\n2. 编译时错误检测\n3. 更好的代码可维护性',
        model: 'deepseek-chat',
        usage: { tokens: 65 },
      });

      const response = await mockAIService.chatWithModel(
        '解释一下TypeScript的优势',
        'deepseek-chat'
      );

      expect(response.success).toBe(true);
      expect(response.response).toContain('TypeScript');
      expect(response.response).toContain('优势');
    });
  });

  describe('错误处理', () => {
    it('应该处理网络连接错误', async () => {
      mockAIService.convertCommand.mockRejectedValue(
        new Error('Network error')
      );

      try {
        await mockAIService.convertCommand('测试命令');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });

    it('应该处理API密钥错误', async () => {
      mockAIService.chatWithModel.mockRejectedValue(
        new Error('Invalid API key')
      );

      try {
        await mockAIService.chatWithModel('测试问题', 'deepseek-chat');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Invalid API key');
      }
    });

    it('应该处理模型不可用错误', async () => {
      mockAIService.convertCommand.mockResolvedValue({
        success: false,
        error: 'Model temporarily unavailable',
        command: null,
      });

      const result = await mockAIService.convertCommand('测试命令');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Model temporarily unavailable');
    });

    it('应该处理无法理解的命令', async () => {
      mockAIService.convertCommand.mockResolvedValue({
        success: false,
        error: 'Unable to understand the command',
        command: null,
        suggestion: '请尝试使用更具体的描述',
      });

      const result = await mockAIService.convertCommand('随机文字内容');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Unable to understand');
      expect(result.suggestion).toBeTruthy();
    });
  });

  describe('设置集成', () => {
    it('应该使用正确的AI设置', () => {
      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      expect(mockSettingsStore.aiSettings.provider).toBe('deepseek');
      expect(mockSettingsStore.aiSettings.enabled).toBe(true);
    });

    it('应该能够更新AI设置', () => {
      const newSettings = {
        provider: 'openai',
        apiKey: 'new-api-key',
        model: 'gpt-4',
        enabled: true,
      };

      mockSettingsStore.updateAISettings(newSettings);

      expect(mockSettingsStore.updateAISettings).toHaveBeenCalledWith(
        newSettings
      );
    });

    it('AI禁用时应该显示提示信息', async () => {
      // 模拟AI被禁用
      mockSettingsStore.aiSettings.enabled = false;

      render(
        <AITestContainer>
          <MockAICommandInput />
        </AITestContainer>
      );

      // 验证AI设置已被禁用
      expect(mockSettingsStore.aiSettings.enabled).toBe(false);
    });
  });
});
