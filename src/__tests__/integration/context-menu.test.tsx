import { ContextMenu } from '@/components/ui/ContextMenu';
import { ContextMenuManager } from '@/components/ui/ContextMenuManager';
import { TabContextMenu } from '@/components/ui/TabContextMenu';
import { TerminalContextMenu } from '@/components/ui/TerminalContextMenu';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

// Mock stores
vi.mock('@/stores/terminalStore', () => ({
  useTerminalStore: () => ({
    getActiveTab: vi.fn(() => ({ id: 'test-tab', title: 'Test Tab' })),
    createTab: vi.fn(),
    closeTab: vi.fn(),
    updateTab: vi.fn(),
    getTabById: vi.fn(() => ({
      id: 'test-tab',
      title: 'Test Tab',
      shell: 'zsh',
      currentDirectory: '/test',
    })),
  }),
}));

vi.mock('@/stores/contextMenuStore', () => ({
  useContextMenuStore: () => ({
    visible: false,
    position: { x: 100, y: 100 },
    menuType: null,
    contextData: null,
    hideMenu: vi.fn(),
    hasSelection: false,
    canPaste: true,
  }),
}));

describe('Context Menu Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  describe('基础上下文菜单', () => {
    it('应该正确渲染菜单项', () => {
      const mockOnClose = vi.fn();
      const menuItems = [
        {
          id: 'copy',
          label: '复制',
          action: vi.fn(),
        },
        {
          id: 'paste',
          label: '粘贴',
          action: vi.fn(),
        },
      ];

      render(
        <ContextMenu
          items={menuItems}
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
        />
      );

      expect(screen.getByText('复制')).toBeInTheDocument();
      expect(screen.getByText('粘贴')).toBeInTheDocument();
    });

    it('应该在点击外部时关闭菜单', async () => {
      const mockOnClose = vi.fn();
      const menuItems = [
        {
          id: 'copy',
          label: '复制',
          action: vi.fn(),
        },
      ];

      render(
        <div>
          <div data-testid="outside">外部区域</div>
          <ContextMenu
            items={menuItems}
            position={{ x: 100, y: 100 }}
            visible={true}
            onClose={mockOnClose}
          />
        </div>
      );

      fireEvent.mouseDown(screen.getByTestId('outside'));
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    it('应该在按ESC键时关闭菜单', async () => {
      const mockOnClose = vi.fn();
      const menuItems = [
        {
          id: 'copy',
          label: '复制',
          action: vi.fn(),
        },
      ];

      render(
        <ContextMenu
          items={menuItems}
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
        />
      );

      fireEvent.keyDown(document, { key: 'Escape' });
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    it('应该在点击菜单项时执行对应的动作', async () => {
      const mockAction = vi.fn();
      const mockOnClose = vi.fn();
      const menuItems = [
        {
          id: 'copy',
          label: '复制',
          action: mockAction,
        },
      ];

      render(
        <ContextMenu
          items={menuItems}
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
        />
      );

      fireEvent.click(screen.getByText('复制'));
      await waitFor(() => {
        expect(mockAction).toHaveBeenCalled();
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    it('应该正确处理禁用的菜单项', () => {
      const mockAction = vi.fn();
      const mockOnClose = vi.fn();
      const menuItems = [
        {
          id: 'disabled',
          label: '禁用项',
          action: mockAction,
          disabled: true,
        },
      ];

      render(
        <ContextMenu
          items={menuItems}
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
        />
      );

      const disabledButton = screen.getByText('禁用项').closest('button');
      expect(disabledButton).toHaveClass('opacity-50');
      expect(disabledButton).toBeDisabled();

      fireEvent.click(disabledButton!);
      expect(mockAction).not.toHaveBeenCalled();
    });
  });

  describe('终端右键菜单', () => {
    it('应该渲染所有终端相关的菜单项', () => {
      const mockOnClose = vi.fn();

      render(
        <TerminalContextMenu
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
          hasSelection={true}
        />
      );

      expect(screen.getByText('复制')).toBeInTheDocument();
      expect(screen.getByText('粘贴')).toBeInTheDocument();
      expect(screen.getByText('全选')).toBeInTheDocument();
      expect(screen.getByText('清空终端')).toBeInTheDocument();
    });

    it('应该根据选择状态禁用复制菜单项', () => {
      const mockOnClose = vi.fn();

      const { rerender } = render(
        <TerminalContextMenu
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
          hasSelection={false}
        />
      );

      // 没有选择时，复制应该被禁用
      const copyButton = screen.getByText('复制').closest('button');
      expect(copyButton).toBeDisabled();

      // 有选择时，复制应该可用
      rerender(
        <TerminalContextMenu
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
          hasSelection={true}
        />
      );

      const copyButtonEnabled = screen.getByText('复制').closest('button');
      expect(copyButtonEnabled).not.toBeDisabled();
    });
  });

  describe('Tab右键菜单', () => {
    it('应该渲染所有Tab相关的菜单项', () => {
      const mockOnClose = vi.fn();

      render(
        <TabContextMenu
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
          tabId="test-tab"
          tabIndex={0}
          tabCount={3}
        />
      );

      expect(screen.getByText('重命名Tab')).toBeInTheDocument();
      expect(screen.getByText('复制Tab')).toBeInTheDocument();
      expect(screen.getByText('关闭Tab')).toBeInTheDocument();
      expect(screen.getByText('关闭其他Tab')).toBeInTheDocument();
      expect(screen.getByText('关闭右侧Tab')).toBeInTheDocument();
    });

    it('应该在只有一个Tab时禁用关闭菜单项', () => {
      const mockOnClose = vi.fn();

      render(
        <TabContextMenu
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
          tabId="test-tab"
          tabIndex={0}
          tabCount={1}
        />
      );

      const closeButton = screen.getByText('关闭Tab').closest('button');
      const closeOthersButton = screen
        .getByText('关闭其他Tab')
        .closest('button');

      expect(closeButton).toBeDisabled();
      expect(closeOthersButton).toBeDisabled();
    });

    it('应该在最后一个Tab时禁用关闭右侧Tab菜单项', () => {
      const mockOnClose = vi.fn();

      render(
        <TabContextMenu
          position={{ x: 100, y: 100 }}
          visible={true}
          onClose={mockOnClose}
          tabId="test-tab"
          tabIndex={2}
          tabCount={3}
        />
      );

      const closeToRightButton = screen
        .getByText('关闭右侧Tab')
        .closest('button');
      expect(closeToRightButton).toBeDisabled();
    });
  });

  describe('右键菜单管理器', () => {
    it('应该在不可见时不渲染任何内容', () => {
      const { container } = render(<ContextMenuManager />);
      expect(container.firstChild).toBeNull();
    });
  });
});
