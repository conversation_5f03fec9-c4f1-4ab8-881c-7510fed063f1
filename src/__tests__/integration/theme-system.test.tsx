import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { Button } from '../../components/ui/Button';
import { ThemeProvider } from '../../components/ui/ThemeProvider';
import { useThemeStore } from '../../stores/themeStore';
import { defaultDarkTheme, defaultLightTheme } from '../../themes';

// Mock DOM manipulation
vi.mock('../../themes', async () => {
  const actual = (await vi.importActual('../../themes')) as any;
  return {
    ...actual,
    applyThemeToDOM: vi.fn(),
    getSystemTheme: vi.fn(() => 'dark'),
  };
});

// 获取 mock 函数引用 - 在组件之前导入
import * as themesModule from '../../themes';
const mockApplyThemeToDOM = vi.mocked(themesModule.applyThemeToDOM);

// 测试组件 - 简单的主题切换器
const ThemeToggleComponent = () => {
  const { currentTheme, setTheme, autoSwitchEnabled, setAutoSwitch } =
    useThemeStore();

  return (
    <ThemeProvider>
      <div data-testid="theme-container">
        <div data-testid="current-theme">{currentTheme.name}</div>
        <Button
          data-testid="switch-to-light"
          onClick={() => setTheme(defaultLightTheme.id)}
        >
          切换到亮色主题
        </Button>
        <Button
          data-testid="switch-to-dark"
          onClick={() => setTheme(defaultDarkTheme.id)}
        >
          切换到暗色主题
        </Button>
        <Button
          data-testid="toggle-auto-switch"
          onClick={() => setAutoSwitch(!autoSwitchEnabled)}
        >
          {autoSwitchEnabled ? '禁用' : '启用'}自动切换
        </Button>
        <div data-testid="auto-switch-status">
          自动切换: {autoSwitchEnabled ? '已启用' : '已禁用'}
        </div>
      </div>
    </ThemeProvider>
  );
};

describe('主题系统集成测试', () => {
  beforeEach(() => {
    // 清理状态
    localStorage.clear();
    mockApplyThemeToDOM.mockClear();

    // 重置store
    useThemeStore.setState({
      currentTheme: defaultDarkTheme,
      availableThemes: [defaultDarkTheme, defaultLightTheme],
      systemTheme: 'dark',
      autoSwitchEnabled: false,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('主题切换功能', () => {
    it('应该能够在亮色和暗色主题之间切换', async () => {
      const user = userEvent.setup();
      render(<ThemeToggleComponent />);

      // 初始状态应该是暗色主题
      expect(screen.getByTestId('current-theme')).toHaveTextContent(
        defaultDarkTheme.name
      );

      // 切换到亮色主题
      await user.click(screen.getByTestId('switch-to-light'));

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent(
          defaultLightTheme.name
        );
      });

      // 验证 DOM 更新函数被调用
      expect(mockApplyThemeToDOM).toHaveBeenCalledWith(defaultLightTheme);

      // 切换回暗色主题
      await user.click(screen.getByTestId('switch-to-dark'));

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent(
          defaultDarkTheme.name
        );
      });

      expect(mockApplyThemeToDOM).toHaveBeenCalledWith(defaultDarkTheme);
    });

    it('主题切换应该触发自定义事件', async () => {
      const user = userEvent.setup();
      const eventListener = vi.fn();

      window.addEventListener('theme-changed', eventListener);

      render(<ThemeToggleComponent />);

      await user.click(screen.getByTestId('switch-to-light'));

      await waitFor(() => {
        expect(eventListener).toHaveBeenCalledWith(
          expect.objectContaining({
            detail: defaultLightTheme,
          })
        );
      });

      window.removeEventListener('theme-changed', eventListener);
    });
  });

  describe('自动切换功能', () => {
    it('应该能够启用和禁用自动切换', async () => {
      const user = userEvent.setup();
      render(<ThemeToggleComponent />);

      // 初始状态
      expect(screen.getByTestId('auto-switch-status')).toHaveTextContent(
        '已禁用'
      );

      // 启用自动切换
      await user.click(screen.getByTestId('toggle-auto-switch'));

      await waitFor(() => {
        expect(screen.getByTestId('auto-switch-status')).toHaveTextContent(
          '已启用'
        );
      });

      // 禁用自动切换
      await user.click(screen.getByTestId('toggle-auto-switch'));

      await waitFor(() => {
        expect(screen.getByTestId('auto-switch-status')).toHaveTextContent(
          '已禁用'
        );
      });
    });

    it('启用自动切换时应该立即检测系统主题', async () => {
      const user = userEvent.setup();
      const store = useThemeStore.getState();
      const detectSystemThemeSpy = vi.spyOn(store, 'detectSystemTheme');

      render(<ThemeToggleComponent />);

      await user.click(screen.getByTestId('toggle-auto-switch'));

      await waitFor(() => {
        expect(detectSystemThemeSpy).toHaveBeenCalled();
      });
    });
  });

  describe('组件与主题的集成', () => {
    it('Button组件应该响应主题变化', async () => {
      const user = userEvent.setup();
      render(<ThemeToggleComponent />);

      const lightButton = screen.getByTestId('switch-to-light');

      // 切换主题
      await user.click(lightButton);

      await waitFor(() => {
        // 验证按钮的样式类是否正确应用
        expect(lightButton).toHaveClass(
          'bg-primary',
          'text-primary-foreground'
        );
      });
    });

    it('ThemeProvider应该为子组件提供主题上下文', () => {
      render(<ThemeToggleComponent />);

      // 验证ThemeProvider包装的组件能正确渲染
      expect(screen.getByTestId('theme-container')).toBeInTheDocument();
      expect(screen.getByTestId('current-theme')).toBeInTheDocument();
    });
  });

  describe('状态持久化', () => {
    it('主题变化应该被持久化到localStorage', async () => {
      const user = userEvent.setup();
      render(<ThemeToggleComponent />);

      await user.click(screen.getByTestId('switch-to-light'));

      await waitFor(() => {
        // 检查localStorage是否包含主题数据
        const storedData = localStorage.getItem('theme-storage');
        expect(storedData).toBeTruthy();

        if (storedData) {
          const parsedData = JSON.parse(storedData);
          expect(parsedData.state.currentTheme.id).toBe(defaultLightTheme.id);
        }
      });
    });

    it('自动切换状态应该被持久化', async () => {
      const user = userEvent.setup();
      render(<ThemeToggleComponent />);

      await user.click(screen.getByTestId('toggle-auto-switch'));

      await waitFor(() => {
        const storedData = localStorage.getItem('theme-storage');
        expect(storedData).toBeTruthy();

        if (storedData) {
          const parsedData = JSON.parse(storedData);
          expect(parsedData.state.autoSwitchEnabled).toBe(true);
        }
      });
    });
  });

  describe('错误处理', () => {
    it('设置不存在的主题ID应该不改变当前主题', async () => {
      // const user = userEvent.setup();
      render(<ThemeToggleComponent />);

      const initialTheme = screen.getByTestId('current-theme').textContent;

      // 尝试设置不存在的主题
      const store = useThemeStore.getState();
      store.setTheme('non-existent-theme-id');

      // 主题应该保持不变
      expect(screen.getByTestId('current-theme')).toHaveTextContent(
        initialTheme || ''
      );
    });

    it('localStorage错误不应该影响主题功能', async () => {
      const _user = userEvent.setup();

      // Mock localStorage 抛出错误
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = vi.fn(() => {
        throw new Error('Storage quota exceeded');
      });

      render(<ThemeToggleComponent />);

      // 主题切换应该仍然工作
      await _user.click(screen.getByTestId('switch-to-light'));

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent(
          defaultLightTheme.name
        );
        expect(mockApplyThemeToDOM).toHaveBeenCalledWith(defaultLightTheme);
      });

      // 恢复原始方法
      localStorage.setItem = originalSetItem;
    });
  });

  describe('自定义主题管理', () => {
    const customTheme = {
      id: 'custom-test-theme',
      name: 'Test Custom Theme',
      type: 'dark' as const,
      colors: {
        primary: '#ff0000',
        secondary: '#00ff00',
        background: '#000000',
        surface: '#111111',
        text: '#ffffff',
        textSecondary: '#cccccc',
        border: '#333333',
        terminal: {
          background: '#000000',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selection: '#444444',
          ansi: Array(16).fill('#ffffff'),
        },
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000',
        info: '#0000ff',
        hover: '#222222',
        active: '#333333',
        focus: '#444444',
        disabled: '#666666',
      },
      typography: {
        fontFamily: 'monospace',
        fontSize: 14,
        lineHeight: 1.5,
        fontWeight: 400,
      },
      effects: {
        borderRadius: 4,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        opacity: {
          disabled: 0.5,
          hover: 0.8,
        },
        transition: {
          duration: '0.2s',
          easing: 'ease-in-out',
        },
      },
    };

    it('应该能够添加和使用自定义主题', async () => {
      render(<ThemeToggleComponent />);

      const store = useThemeStore.getState();

      // 添加自定义主题
      store.addCustomTheme(customTheme);

      // 切换到自定义主题
      store.setTheme(customTheme.id);

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent(
          customTheme.name
        );
        expect(mockApplyThemeToDOM).toHaveBeenCalledWith(customTheme);
      });
    });

    it('删除当前使用的自定义主题应该回退到默认主题', async () => {
      render(<ThemeToggleComponent />);

      const store = useThemeStore.getState();

      // 添加并切换到自定义主题
      store.addCustomTheme(customTheme);
      store.setTheme(customTheme.id);

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent(
          customTheme.name
        );
      });

      // 删除自定义主题
      store.removeCustomTheme(customTheme.id);

      await waitFor(() => {
        // 应该回退到默认主题
        expect(screen.getByTestId('current-theme')).toHaveTextContent(
          defaultDarkTheme.name
        );
      });
    });
  });
});
