import { SHORTCUT_ACTIONS } from '@/stores/shortcutStore';
import { useFeatureShortcuts } from './useShortcuts';

interface UseTerminalShortcutsProps {
  onCopy?: () => void;
  onPaste?: () => void;
  onSelectAll?: () => void;
  onClear?: () => void;
  onInterrupt?: () => void;
  onOpenSearch?: () => void;
}

export const useTerminalShortcuts = ({
  onCopy,
  onPaste,
  onSelectAll,
  onClear,
  onInterrupt,
  onOpenSearch,
}: UseTerminalShortcutsProps = {}) => {
  // 定义终端操作的动作映射
  const terminalActions = {
    [SHORTCUT_ACTIONS.COPY_SELECTION]: () => {
      if (onCopy) {
        onCopy();
      } else {
        // 默认复制行为 - 复制选中的文本
        handleDefaultCopy();
      }
    },

    [SHORTCUT_ACTIONS.PASTE_TEXT]: () => {
      if (onPaste) {
        onPaste();
      } else {
        // 默认粘贴行为
        handleDefaultPaste();
      }
    },

    [SHORTCUT_ACTIONS.SELECT_ALL]: () => {
      if (onSelectAll) {
        onSelectAll();
      } else {
        // 默认全选行为
        handleDefaultSelectAll();
      }
    },

    [SHORTCUT_ACTIONS.CLEAR_TERMINAL]: () => {
      if (onClear) {
        onClear();
      } else {
        console.log('Clear terminal action triggered');
      }
    },

    [SHORTCUT_ACTIONS.INTERRUPT_PROCESS]: () => {
      if (onInterrupt) {
        onInterrupt();
      } else {
        console.log('Interrupt process action triggered');
      }
    },

    [SHORTCUT_ACTIONS.OPEN_SEARCH]: () => {
      if (onOpenSearch) {
        onOpenSearch();
      } else {
        console.log('Open search action triggered');
      }
    },
  };

  // 使用新的快捷键系统
  const { isEnabled } = useFeatureShortcuts(terminalActions);

  // 默认复制处理
  const handleDefaultCopy = () => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      navigator.clipboard.writeText(selection.toString()).catch(console.error);
    }
  };

  // 默认粘贴处理
  const handleDefaultPaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      // 这里应该将文本发送到终端
      console.log('Pasting text:', text);
    } catch (error) {
      console.error('Failed to paste text:', error);
    }
  };

  // 默认全选处理
  const handleDefaultSelectAll = () => {
    // 选择终端内容区域
    const terminalElement = document.querySelector('[data-terminal-content]');
    if (terminalElement) {
      const range = document.createRange();
      range.selectNodeContents(terminalElement);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  };

  return {
    isEnabled,
    actions: {
      copy: onCopy || handleDefaultCopy,
      paste: onPaste || handleDefaultPaste,
      selectAll: onSelectAll || handleDefaultSelectAll,
      clear: onClear,
      interrupt: onInterrupt,
      openSearch: onOpenSearch,
    },
  };
};
