import {
  CreateTerminalRequest,
  terminalService,
} from '@/services/terminalService';
import { parseAnsiLine, ParsedSegment } from '@/utils/ansiParser';
import { useCallback, useEffect, useRef, useState } from 'react';


// 终端行类型，支持ANSI格式化
export interface TerminalLine {
  segments: ParsedSegment[];
  timestamp: number;
  type?: 'input' | 'output' | 'error' | 'system';
  rawText?: string; // 保留原始文本用于调试
}

export interface UseRealTerminalOptions {
  rows?: number;
  cols?: number;
  shell?: string;
  cwd?: string;
  env?: Record<string, string>;
}

export interface UseRealTerminalReturn {
  terminalId: string | null;
  lines: TerminalLine[];
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  currentInput: string;

  // 操作方法
  createTerminal: (options?: CreateTerminalRequest) => Promise<void>;
  destroyTerminal: () => Promise<void>;
  sendInput: (input: string) => Promise<void>;
  sendCommand: (command: string) => Promise<void>;
  resize: (rows: number, cols: number) => Promise<void>;
  clear: () => void;

  // 输入处理
  setCurrentInput: (input: string) => void;
  handleKeyInput: (
    key: string,
    ctrlKey?: boolean,
    metaKey?: boolean
  ) => Promise<void>;
}

export const useRealTerminal = (
  options: UseRealTerminalOptions = {}
): UseRealTerminalReturn => {
  // 基础状态
  const [terminalId, setTerminalId] = useState<string | null>(null);
  const [lines, setLines] = useState<TerminalLine[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentInput, setCurrentInput] = useState('');

  // 添加防重复创建的 ref
  const hookInitialized = useRef(false);
  const hookId = useRef(
    `hook-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  );

  // 调试用的唯一标识
  const debugId = hookId.current;

  console.log(`[useRealTerminal:${debugId}] Hook初始化, StrictMode检查:`, {
    initialized: hookInitialized.current,
    terminalId,
    isLoading,
  });

  const currentInputRef = useRef('');
  currentInputRef.current = currentInput;

  // 添加系统消息行
  const addSystemLine = useCallback(
    (text: string, type: TerminalLine['type'] = 'system') => {
      setLines(prev => [
        ...prev,
        {
          segments: [{ text, style: {} }],
          timestamp: Date.now(),
          type,
          rawText: text,
        },
      ]);
    },
    []
  );

  // 添加用户输入行
  const addInputLine = useCallback((text: string) => {
    setLines(prev => [
      ...prev,
      {
        segments: [
          { text: '$ ', style: { color: '#22c55e', bold: true } },
          { text, style: { color: '#22c55e' } },
        ],
        timestamp: Date.now(),
        type: 'input',
        rawText: `$ ${text}`,
      },
    ]);
  }, []);

  // 处理终端输出 - 使用ANSI解析器
  const handleTerminalOutput = useCallback((data: string) => {
    console.log('[useRealTerminal] 收到原始输出:', JSON.stringify(data));

    if (!data) return;

    // 检测是否是终端大小调整产生的输出
    const isResizeOutput = (input: string): boolean => {
      // 常见的终端大小调整输出模式：
      // 1. 只包含换行符和空白字符
      // 2. 包含光标定位序列后跟换行符
      // 3. 包含清屏序列后跟换行符
      const ESC = '\u001b';
      const patterns = [
        /^\s*\n+\s*$/,                                                    // 只有换行符和空白
        new RegExp(`^${ESC}\\[[0-9;]*[Hf]\\s*\\n*\\s*$`),              // 光标定位序列
        new RegExp(`^${ESC}\\[[0-9]*[JK]\\s*\\n*\\s*$`),               // 清屏/清行序列
        new RegExp(`^${ESC}\\[[0-9]*[ABCD]\\s*\\n*\\s*$`),             // 光标移动序列
      ];

      return patterns.some(pattern => pattern.test(input));
    };

    // 如果检测到是终端大小调整的输出，直接跳过
    if (isResizeOutput(data)) {
      console.log('[useRealTerminal] 检测到终端大小调整输出，跳过处理');
      return;
    }

    // 辅助函数：添加新行
    const addNewLine = (segments: ParsedSegment[], rawText: string) => {
      setLines(prev => [
        ...prev,
        {
          segments,
          timestamp: Date.now(),
          type: 'output',
          rawText,
        },
      ]);

      console.log('[useRealTerminal] 解析行:', {
        raw: JSON.stringify(rawText),
        segments: segments.map(s => ({
          text: s.text,
          hasStyle: Object.keys(s.style).length > 0,
        })),
      });
    };

    // 辅助函数：处理包含退格符的行
    const handleBackspaceInLine = (line: string) => {
      setLines(prev => {
        const newLines = [...prev];
        let currentLine = line;

        // 处理退格符
        let i = 0;
        while (i < currentLine.length) {
          if (currentLine[i] === '\b') {
            // 找到退格符，需要删除前面的字符
            if (newLines.length > 0) {
              const lastLine = newLines[newLines.length - 1];
              const lastSegment = lastLine.segments[lastLine.segments.length - 1];

              if (lastSegment && lastSegment.text.length > 0) {
                // 删除最后一个字符
                const newText = lastSegment.text.slice(0, -1);
                if (newText.length === 0) {
                  // 如果段落变空，删除整个段落
                  lastLine.segments.pop();
                  if (lastLine.segments.length === 0) {
                    // 如果行变空，删除整行
                    newLines.pop();
                  }
                } else {
                  // 更新段落文本
                  lastSegment.text = newText;
                }
              }
            }

            // 移除退格符，继续处理后面的字符
            currentLine = currentLine.slice(0, i) + currentLine.slice(i + 1);
          } else {
            i++;
          }
        }

        // 如果还有剩余字符，解析并添加到最后一行或创建新行
        if (currentLine) {
          const segments = parseAnsiLine(currentLine);
          if (segments.length > 0 && segments[0].text) {
            if (newLines.length > 0) {
              // 尝试添加到最后一行
              const lastLine = newLines[newLines.length - 1];
              lastLine.segments.push(...segments);
              lastLine.rawText = (lastLine.rawText || '') + currentLine;
            } else {
              // 创建新行
              newLines.push({
                segments,
                timestamp: Date.now(),
                type: 'output',
                rawText: currentLine,
              });
            }
          }
        }

        return newLines;
      });
    };

    // 预处理数据，处理常见的控制字符问题
    let processedData = data;

    // 处理回车符和换行符的组合
    processedData = processedData.replace(/\r\n/g, '\n');
    processedData = processedData.replace(/\r(?!\n)/g, '\n'); // 只替换不跟随\n的\r

    // 处理光标移动序列（如 \u001b[A\u001b[A）- 这些通常用于覆盖之前的输出
    // 简单处理：移除连续的光标上移序列
    const ESC = '\u001b';
    processedData = processedData.replace(new RegExp(`(${ESC}\\[[0-9]*A)+`, 'g'), '');

    // 处理清除行序列
    processedData = processedData.replace(new RegExp(`${ESC}\\[[0-9]*J`, 'g'), '');

    // 处理清除到行末序列
    processedData = processedData.replace(new RegExp(`${ESC}\\[[0-9]*K`, 'g'), '');

    // 处理光标定位序列
    processedData = processedData.replace(new RegExp(`${ESC}\\[[0-9;]*H`, 'g'), '');

    // 处理保存/恢复光标位置
    processedData = processedData.replace(new RegExp(`${ESC}\\[s`, 'g'), '');
    processedData = processedData.replace(new RegExp(`${ESC}\\[u`, 'g'), '');

    // 处理终端大小调整时可能产生的多余换行符
    // 移除连续的多个换行符，保留最多1个连续换行符
    processedData = processedData.replace(/\n{2,}/g, '\n');

    // 特别处理：如果数据只包含换行符和空白字符，可能是终端大小调整产生的
    if (/^\s*\n*\s*$/.test(processedData)) {
      console.log('[useRealTerminal] 检测到可能的终端大小调整产生的空输出，跳过处理');
      return;
    }

    // 处理其他常见的光标控制序列
    processedData = processedData.replace(new RegExp(`${ESC}\\[[0-9]*[BCDEF]`, 'g'), '');

    // 按行分割输出
    const outputLines = processedData.split('\n');

    outputLines.forEach((line, index) => {
      // 跳过空行，但保留有意义的空行（比如用户按回车产生的）
      if (!line.trim() && index < outputLines.length - 1) {
        // 检查是否是终端大小调整产生的空行
        // 如果前后都是空行或者只包含控制字符，则跳过
        const prevLine = index > 0 ? outputLines[index - 1] : '';
        const nextLine = index < outputLines.length - 1 ? outputLines[index + 1] : '';

        if (!prevLine.trim() && !nextLine.trim()) {
          console.log('[useRealTerminal] 跳过可能由终端大小调整产生的空行');
          return;
        }
      }

      try {
        // 只需特殊处理第一行即可
        if (index === 0) {
          handleBackspaceInLine(line);
        } else {
          // 正常处理不包含退格符的行
          const segments = parseAnsiLine(line);
          addNewLine(segments, line);
        }
      } catch (err) {
        console.error(
          '[useRealTerminal] ANSI解析失败:',
          err,
          'line:',
          JSON.stringify(line)
        );

        // 解析失败时显示原始文本
        addNewLine([{ text: line || '', style: {} }], line);
      }
    });
  }, []);

  // 创建终端
  const createTerminal = useCallback(
    async (createOptions?: CreateTerminalRequest) => {
      // 防止重复创建
      if (isLoading || terminalId || hookInitialized.current) {
        console.log(`[useRealTerminal:${debugId}] 跳过终端创建:`, {
          isLoading,
          hasTerminalId: !!terminalId,
          initialized: hookInitialized.current,
        });
        return;
      }

      console.log(`[useRealTerminal:${debugId}] 开始创建终端`);
      hookInitialized.current = true;
      setIsLoading(true);
      setError(null);

      try {
        const config: CreateTerminalRequest = {
          rows: options.rows || 24,
          cols: options.cols || 80,
          shell: options.shell,
          cwd: options.cwd,
          env: options.env,
          ...createOptions,
        };

        const response = await terminalService.createTerminal(config);

        if (response.success) {
          setTerminalId(response.id);
          setIsConnected(true);

          // 注册输出监听器
          terminalService.addOutputListener(response.id, handleTerminalOutput);

          // 添加欢迎信息
          // addSystemLine('TAgent 终端已连接到后端', 'system');
          // addSystemLine(`终端 ID: ${response.id}`, 'system');

          console.log(
            `[useRealTerminal:${debugId}] 终端创建成功:`,
            response.id
          );
        } else {
          const errorMessage = response.message || '创建终端失败';
          console.error(
            `[useRealTerminal:${debugId}] 终端创建失败详情:`,
            response
          );
          throw new Error(errorMessage);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : (err as string);
        setError(errorMessage);
        addSystemLine(`错误: ${errorMessage}`, 'error');
        console.error(`[useRealTerminal:${debugId}] 创建终端失败:`, err);

        // 重置初始化标志，允许重试
        hookInitialized.current = false;
      } finally {
        setIsLoading(false);
      }
    },
    [
      isLoading,
      terminalId,
      handleTerminalOutput,
      addSystemLine,
      debugId,
      options.rows,
      options.cols,
      options.shell,
      options.cwd,
      options.env,
    ]
  );

  // 销毁终端
  const destroyTerminal = useCallback(async () => {
    if (!terminalId || isLoading) return;

    console.log(`[useRealTerminal:${debugId}] 开始销毁终端:`, terminalId);
    setIsLoading(true);

    try {
      await terminalService.killTerminal(terminalId);

      // 清理状态
      terminalService.removeOutputListener(terminalId);
      setTerminalId(null);
      setIsConnected(false);
      setCurrentInput('');

      addSystemLine('终端连接已断开', 'system');
      console.log(`[useRealTerminal:${debugId}] 终端已销毁:`, terminalId);
    } catch (err) {
      console.error(`[useRealTerminal:${debugId}] 销毁终端失败:`, err);
    } finally {
      setIsLoading(false);
      // 重置初始化标志
      hookInitialized.current = false;
    }
  }, [terminalId, isLoading, addSystemLine, debugId]);

  // 组件卸载时的清理函数
  useEffect(() => {
    return () => {
      console.log(`[useRealTerminal:${debugId}] Hook卸载，清理资源`);

      // 异步清理资源
      if (terminalId) {
        console.log(`[useRealTerminal:${debugId}] 清理监听器:`, terminalId);
        terminalService.removeOutputListener(terminalId);

        // 尝试关闭终端（不等待结果）
        terminalService.killTerminal(terminalId).catch(err => {
          console.warn(`[useRealTerminal:${debugId}] 清理时关闭终端失败:`, err);
        });
      }

      // 重置初始化标志
      hookInitialized.current = false;
    };
  }, [terminalId, debugId]);

  // 发送输入到终端
  const sendInput = useCallback(
    async (input: string) => {
      if (!terminalId || !isConnected) {
        console.warn('[useRealTerminal] 终端未连接，无法发送输入');
        const errorMsg = '终端未连接，请等待连接完成';
        addSystemLine(`输入错误: ${errorMsg}`, 'error');
        return;
      }

      try {
        console.log(
          '[useRealTerminal] 发送输入:',
          JSON.stringify(input),
          '到终端:',
          terminalId
        );
        const result = await terminalService.writeToTerminal(terminalId, input);

        if (result) {
          console.log('[useRealTerminal] 输入发送成功');
        } else {
          console.warn('[useRealTerminal] 输入发送返回false');
          addSystemLine('输入发送失败：返回false', 'error');
        }
      } catch (err) {
        console.error('[useRealTerminal] 发送输入失败:', err);

        // 提供更详细的错误信息
        let errorMessage = '未知错误';
        if (err instanceof Error) {
          errorMessage = err.message;

          // 检查是否是特定的错误类型
          if (
            errorMessage.includes('PTY') &&
            errorMessage.includes('not found')
          ) {
            errorMessage = '终端进程已断开，请重新创建终端';
          } else if (errorMessage.includes('write')) {
            errorMessage = '写入终端失败，请检查终端状态';
          } else if (errorMessage.includes('channel')) {
            errorMessage = '通信通道错误，请重新连接';
          }
        }

        addSystemLine(`输入错误: ${errorMessage}`, 'error');
      }
    },
    [terminalId, isConnected, addSystemLine]
  );

  // 发送完整命令
  const sendCommand = useCallback(
    async (command: string) => {
      if (!command.trim()) return;

      // 显示用户输入
      addInputLine(command);

      // 检查是否是特殊AI命令
      if (command.startsWith('@')) {
        try {
          addSystemLine('正在处理AI命令...', 'system');
          const response = await terminalService.executeAdvancedCommand(
            terminalId!,
            command
          );

          if (response.success && response.result) {
            if (response.result.response) {
              addSystemLine(response.result.response, 'output');
            }
            if (response.result.processed_command) {
              addSystemLine(
                `建议命令: ${response.result.processed_command}`,
                'output'
              );
            }
          } else {
            addSystemLine(response.error || 'AI命令处理失败', 'error');
          }
        } catch (err) {
          console.error('[useRealTerminal] AI命令执行失败:', err);
          const errorMessage = err instanceof Error ? err.message : '未知错误';
          addSystemLine(`AI命令错误: ${errorMessage}`, 'error');
        }
      } else {
        // 普通命令，发送到真实终端
        await sendInput(command + '\n');
      }
    },
    [terminalId, addInputLine, addSystemLine, sendInput]
  );

  // 调整终端大小
  const resize = useCallback(
    async (rows: number, cols: number) => {
      if (!terminalId || !isConnected) return;

      try {
        await terminalService.resizeTerminal(terminalId, { rows, cols });
        console.log('[useRealTerminal] 终端大小已调整:', { rows, cols });
      } catch (err) {
        console.error('[useRealTerminal] 调整终端大小失败:', err);
      }
    },
    [terminalId, isConnected]
  );

  // 清空显示
  const clear = useCallback(() => {
    setLines([]);
  }, []);

  // 处理键盘输入
  const handleKeyInput = useCallback(
    async (key: string, ctrlKey = false, metaKey = false) => {
      if (!isConnected) return;

      if (ctrlKey || metaKey) {
        // 处理组合键
        switch (key.toLowerCase()) {
          case 'c':
            // Ctrl+C
            await sendInput('\x03'); // ASCII ETX (End of Text)
            break;
          case 'd':
            // Ctrl+D
            await sendInput('\x04'); // ASCII EOT (End of Transmission)
            break;
          case 'l':
            // Ctrl+L - 清屏，但不清除我们的显示，让PTY处理
            await sendInput('\x0c'); // ASCII FF (Form Feed)
            break;
        }
        return;
      }

      // 处理普通字符和特殊键
      if (key.length === 1) {
        // 普通字符，直接发送到PTY
        await sendInput(key);
      } else {
        // 特殊键
        switch (key) {
          case 'Enter':
            await sendInput('\n'); // 发送回车换行
            break;

          case 'Backspace':
            await sendInput('\x08'); // ASCII BS (Backspace)
            // 移除本地状态更新，让PTY处理回显
            break;

          case 'Tab':
            await sendInput('\t'); // Tab 补全
            break;

          case 'ArrowUp':
            await sendInput('\x1b[A'); // ANSI escape sequence for up arrow
            break;

          case 'ArrowDown':
            await sendInput('\x1b[B'); // ANSI escape sequence for down arrow
            break;

          case 'ArrowLeft':
            await sendInput('\x1b[D'); // ANSI escape sequence for left arrow
            break;

          case 'ArrowRight':
            await sendInput('\x1b[C'); // ANSI escape sequence for right arrow
            break;

          case 'Home':
            await sendInput('\x1b[H'); // ANSI escape sequence for home
            break;

          case 'End':
            await sendInput('\x1b[F'); // ANSI escape sequence for end
            break;

          case 'Delete':
            await sendInput('\x1b[3~'); // ANSI escape sequence for delete
            break;

          case 'PageUp':
            await sendInput('\x1b[5~'); // ANSI escape sequence for page up
            break;

          case 'PageDown':
            await sendInput('\x1b[6~'); // ANSI escape sequence for page down
            break;
        }
      }
    },
    [isConnected, sendInput]
  );

  return {
    terminalId,
    lines,
    isConnected,
    isLoading,
    error,
    currentInput,

    createTerminal,
    destroyTerminal,
    sendInput,
    sendCommand,
    resize,
    clear,

    setCurrentInput,
    handleKeyInput,
  };
};
