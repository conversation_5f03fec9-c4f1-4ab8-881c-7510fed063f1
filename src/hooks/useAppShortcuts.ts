import { SHORTCUT_ACTIONS } from '@/stores/shortcutStore';
import { useThemeStore } from '@/stores/themeStore';
import { defaultDarkTheme, defaultLightTheme } from '@/themes';
import { useFeatureShortcuts } from './useShortcuts';

interface UseAppShortcutsProps {
  onOpenSettings?: () => void;
  onToggleTheme?: () => void;
  onShowHelp?: () => void;
  onToggleApp?: () => void;
  onNewWindow?: () => void;
}

export const useAppShortcuts = ({
  onOpenSettings,
  onToggleTheme,
  onShowHelp,
  onToggleApp,
  onNewWindow,
}: UseAppShortcutsProps = {}) => {
  const { currentTheme, setTheme } = useThemeStore();

  // 主题切换函数
  const handleToggleTheme = () => {
    const isDark = currentTheme.id === defaultDarkTheme.id;
    const newThemeId = isDark ? defaultLightTheme.id : defaultDarkTheme.id;
    setTheme(newThemeId);
  };

  // 定义应用功能的动作映射
  const appActions = {
    [SHORTCUT_ACTIONS.OPEN_SETTINGS]: () => {
      if (onOpenSettings) {
        onOpenSettings();
      } else {
        console.log('Open settings action triggered');
      }
    },

    [SHORTCUT_ACTIONS.TOGGLE_THEME]: () => {
      if (onToggleTheme) {
        onToggleTheme();
      } else {
        // 默认主题切换行为
        handleToggleTheme();
      }
    },

    [SHORTCUT_ACTIONS.SHOW_HELP]: () => {
      if (onShowHelp) {
        onShowHelp();
      } else {
        // 默认帮助行为
        handleDefaultShowHelp();
      }
    },

    [SHORTCUT_ACTIONS.TOGGLE_APP]: () => {
      if (onToggleApp) {
        onToggleApp();
      } else {
        console.log('Toggle app visibility action triggered');
      }
    },

    [SHORTCUT_ACTIONS.NEW_WINDOW]: () => {
      if (onNewWindow) {
        onNewWindow();
      } else {
        console.log('New window action triggered');
      }
    },
  };

  // 使用新的快捷键系统
  const { isEnabled } = useFeatureShortcuts(appActions);

  // 默认帮助处理
  const handleDefaultShowHelp = () => {
    // 可以打开帮助对话框或者跳转到帮助页面
    console.log('Showing help...');
    // 这里可以集成具体的帮助功能
  };

  return {
    isEnabled,
    actions: {
      openSettings: onOpenSettings,
      toggleTheme: onToggleTheme || handleToggleTheme,
      showHelp: onShowHelp || handleDefaultShowHelp,
      toggleApp: onToggleApp,
      newWindow: onNewWindow,
    },
  };
};
