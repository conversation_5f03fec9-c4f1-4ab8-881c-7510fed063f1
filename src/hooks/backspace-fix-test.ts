/**
 * 测试新的退格符处理逻辑
 * 这个文件用于验证跨行退格符处理是否正确工作
 */

import { parseAnsiLine, ParsedSegment } from '../utils/ansiParser';

interface TerminalLine {
  segments: ParsedSegment[];
  timestamp: number;
  type?: 'input' | 'output' | 'error' | 'system';
  rawText?: string;
}

/**
 * 模拟新的退格符处理逻辑
 */
function simulateBackspaceHandling(lines: TerminalLine[], newInput: string): TerminalLine[] {
  const newLines = [...lines];
  let currentLine = newInput;

  // 处理退格符
  let i = 0;
  while (i < currentLine.length) {
    if (currentLine[i] === '\b') {
      // 找到退格符，需要删除前面的字符
      if (newLines.length > 0) {
        const lastLine = newLines[newLines.length - 1];
        const lastSegment = lastLine.segments[lastLine.segments.length - 1];

        if (lastSegment && lastSegment.text.length > 0) {
          // 删除最后一个字符
          const newText = lastSegment.text.slice(0, -1);
          if (newText.length === 0) {
            // 如果段落变空，删除整个段落
            lastLine.segments.pop();
            if (lastLine.segments.length === 0) {
              // 如果行变空，删除整行
              newLines.pop();
            }
          } else {
            // 更新段落文本
            lastSegment.text = newText;
          }
        }
      }

      // 移除退格符，继续处理后面的字符
      currentLine = currentLine.slice(0, i) + currentLine.slice(i + 1);
    } else {
      i++;
    }
  }

  // 如果还有剩余字符，解析并添加到最后一行或创建新行
  if (currentLine) {
    const segments = parseAnsiLine(currentLine);
    if (segments.length > 0 && segments[0].text) {
      if (newLines.length > 0) {
        // 尝试添加到最后一行
        const lastLine = newLines[newLines.length - 1];
        lastLine.segments.push(...segments);
        lastLine.rawText = (lastLine.rawText || '') + currentLine;
      } else {
        // 创建新行
        newLines.push({
          segments,
          timestamp: Date.now(),
          type: 'output',
          rawText: currentLine,
        });
      }
    }
  }

  return newLines;
}

/**
 * 测试用例
 */
function runTests() {
  console.log('=== 退格符处理测试 ===\n');

  // 测试1：模拟您的实际问题
  console.log('测试1: 模拟 \\bls 问题');
  let lines: TerminalLine[] = [
    {
      segments: [{ text: 'l', style: {} }],
      timestamp: Date.now(),
      type: 'output',
      rawText: 'l',
    }
  ];

  console.log('初始状态:', lines.map(l => l.segments.map(s => s.text).join('')).join('\n'));

  lines = simulateBackspaceHandling(lines, '\bls');
  console.log('处理 \\bls 后:', lines.map(l => l.segments.map(s => s.text).join('')).join('\n'));
  console.log('预期结果: ls');
  console.log('测试结果:', lines.map(l => l.segments.map(s => s.text).join('')).join('') === 'ls' ? '✅ 通过' : '❌ 失败');
  console.log();

  // 测试2：多个字符的退格
  console.log('测试2: 多个字符的退格');
  lines = [
    {
      segments: [{ text: 'hello', style: {} }],
      timestamp: Date.now(),
      type: 'output',
      rawText: 'hello',
    }
  ];

  console.log('初始状态:', lines.map(l => l.segments.map(s => s.text).join('')).join('\n'));

  lines = simulateBackspaceHandling(lines, '\b\b\bworld');
  console.log('处理 \\b\\b\\bworld 后:', lines.map(l => l.segments.map(s => s.text).join('')).join('\n'));
  console.log('预期结果: heworld');
  const result2 = lines.map(l => l.segments.map(s => s.text).join('')).join('');
  console.log('测试结果:', result2 === 'heworld' ? '✅ 通过' : '❌ 失败');
  console.log();

  // 测试3：完全删除一行
  console.log('测试3: 完全删除一行');
  lines = [
    {
      segments: [{ text: 'abc', style: {} }],
      timestamp: Date.now(),
      type: 'output',
      rawText: 'abc',
    }
  ];

  console.log('初始状态:', lines.map(l => l.segments.map(s => s.text).join('')).join('\n'));

  lines = simulateBackspaceHandling(lines, '\b\b\bxyz');
  console.log('处理 \\b\\b\\bxyz 后:', lines.map(l => l.segments.map(s => s.text).join('')).join('\n'));
  console.log('预期结果: xyz');
  const result3 = lines.map(l => l.segments.map(s => s.text).join('')).join('');
  console.log('测试结果:', result3 === 'xyz' ? '✅ 通过' : '❌ 失败');
  console.log();

  // 测试4：跨多行的退格
  console.log('测试4: 跨多行的退格');
  lines = [
    {
      segments: [{ text: 'line1', style: {} }],
      timestamp: Date.now(),
      type: 'output',
      rawText: 'line1',
    },
    {
      segments: [{ text: 'line2', style: {} }],
      timestamp: Date.now(),
      type: 'output',
      rawText: 'line2',
    }
  ];

  console.log('初始状态:');
  lines.forEach((line, i) => {
    console.log(`  行${i + 1}: ${line.segments.map(s => s.text).join('')}`);
  });

  lines = simulateBackspaceHandling(lines, '\b\b\b\b\bnew');
  console.log('处理 \\b\\b\\b\\b\\bnew 后:');
  lines.forEach((line, i) => {
    console.log(`  行${i + 1}: ${line.segments.map(s => s.text).join('')}`);
  });
  console.log('预期结果: line1 + new');
  const result4 = lines.map(l => l.segments.map(s => s.text).join('')).join('');
  console.log('测试结果:', result4 === 'line1new' ? '✅ 通过' : '❌ 失败');
  console.log();

  console.log('=== 测试完成 ===');
}

// 运行测试
if (typeof window === 'undefined' && typeof process !== 'undefined') {
  runTests();
}

export { runTests, simulateBackspaceHandling };

