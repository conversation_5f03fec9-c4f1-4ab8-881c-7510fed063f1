import { useShortcutStore } from '@/stores/shortcutStore';
import { ShortcutActionMap, ShortcutConfig, UseShortcutsReturn } from '@/types';
import { isShortcutMatch } from '@/utils/shortcutUtils';
import { useCallback, useEffect, useRef } from 'react';

/**
 * 核心快捷键Hook
 * 提供快捷键注册、注销、执行等功能
 */
export const useShortcuts = (): UseShortcutsReturn => {
  const shortcutStore = useShortcutStore();
  const actionMapRef = useRef<ShortcutActionMap>({});
  // const isProcessingRef = useRef(false);

  /**
   * 注册快捷键
   */
  const registerShortcut = useCallback(
    (config: ShortcutConfig) => {
      shortcutStore.setShortcut(config.action, config);
    },
    [shortcutStore]
  );

  /**
   * 注销快捷键
   */
  const unregisterShortcut = useCallback(
    (action: string) => {
      shortcutStore.removeShortcut(action);
      delete actionMapRef.current[action];
    },
    [shortcutStore]
  );

  /**
   * 执行动作
   */
  const executeAction = useCallback((action: string) => {
    const callback = actionMapRef.current[action];
    if (callback && typeof callback === 'function') {
      try {
        callback();
      } catch (error) {
        console.error(`Error executing shortcut action "${action}":`, error);
      }
    } else {
      console.warn(`No callback registered for action: ${action}`);
    }
  }, []);

  /**
   * 检查快捷键是否被按下
   */
  const isShortcutPressed = useCallback((_keys: string[]): boolean => {
    // 这个方法主要用于外部组件检查特定快捷键状态
    // 实际的按键检测由键盘事件处理器完成
    return false;
  }, []);

  return {
    registerShortcut,
    unregisterShortcut,
    executeAction,
    isShortcutPressed,
  };
};

/**
 * 快捷键管理器Hook
 * 用于注册动作回调和处理键盘事件
 */
export const useShortcutManager = (actionMap: ShortcutActionMap = {}) => {
  const { shortcuts, isEnabled } = useShortcutStore();
  const { executeAction } = useShortcuts();
  const actionMapRef = useRef<ShortcutActionMap>(actionMap);

  // 更新动作映射
  useEffect(() => {
    actionMapRef.current = { ...actionMapRef.current, ...actionMap };
  }, [actionMap]);

  // 键盘事件处理器
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!isEnabled) return;

      // 防止在输入元素中触发快捷键（除了一些特殊的快捷键）
      const target = event.target as HTMLElement;
      const isInputElement =
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true';

      // 查找匹配的快捷键
      Object.entries(shortcuts).forEach(([actionKey, config]) => {
        if (!config.enabled) return;

        // 检查是否匹配当前按键事件
        if (isShortcutMatch(event, config.keys)) {
          // 对于某些终端操作，允许在任何地方触发
          const allowInInput = [
            'interruptProcess', // Ctrl+C 中断进程
            'copySelection', // Ctrl+C 复制
            'pasteText', // Ctrl+V 粘贴
          ].includes(config.action);

          if (isInputElement && !allowInInput) {
            return;
          }

          event.preventDefault();
          event.stopPropagation();

          // 执行对应的回调
          const callback = actionMapRef.current[config.action];
          if (callback) {
            try {
              callback();
            } catch (error) {
              console.error(`Error executing shortcut "${actionKey}":`, error);
            }
          } else {
            console.warn(`No callback registered for action: ${config.action}`);
          }
        }
      });
    },
    [shortcuts, isEnabled]
  );

  // 注册全局键盘监听
  useEffect(() => {
    if (!isEnabled) return;

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, isEnabled]);

  return {
    isEnabled,
    shortcuts,
    executeAction,
  };
};

/**
 * 特定功能的快捷键Hook基类
 * 其他功能模块可以基于这个创建自己的快捷键Hook
 */
export const useFeatureShortcuts = (
  featureActions: ShortcutActionMap,
  options: {
    enabled?: boolean;
    preventDefault?: boolean;
  } = {}
) => {
  const { enabled = true, preventDefault = true } = options;

  const { shortcuts, isEnabled: globalEnabled } = useShortcutStore();
  const isEnabled = globalEnabled && enabled;

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!isEnabled) return;

      Object.entries(featureActions).forEach(([action, callback]) => {
        const config = shortcuts[action];
        if (!config || !config.enabled) return;

        if (isShortcutMatch(event, config.keys)) {
          if (preventDefault) {
            event.preventDefault();
            event.stopPropagation();
          }

          try {
            callback();
          } catch (error) {
            console.error(
              `Error executing feature shortcut "${action}":`,
              error
            );
          }
        }
      });
    },
    [featureActions, shortcuts, isEnabled, preventDefault]
  );

  useEffect(() => {
    if (!isEnabled) return;

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, isEnabled]);

  return {
    isEnabled,
    shortcuts: Object.fromEntries(
      Object.keys(featureActions).map(action => [action, shortcuts[action]])
    ),
  };
};

/**
 * 初始化快捷键系统
 * 应在应用启动时调用
 */
export const useInitializeShortcuts = () => {
  const shortcutStore = useShortcutStore();

  useEffect(() => {
    // 初始化默认快捷键配置
    shortcutStore.initializeShortcuts();
  }, [shortcutStore]);

  return {
    platform: shortcutStore.platform,
    isReady: Object.keys(shortcutStore.shortcuts).length > 0,
  };
};
