// History Hook - Provides simplified interface for command history management
// Abstracts store logic and provides convenient methods for components

import { useCallback, useEffect } from 'react';
import { useHistoryStore } from '../stores/historyStore';
import { HistoryEntry } from '../types/history';

interface UseHistoryOptions {
  terminalId: string;
  autoLoad?: boolean;
  defaultLimit?: number;
}

interface UseHistoryReturn {
  // State
  entries: HistoryEntry[];
  filteredEntries: HistoryEntry[];
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  selectedEntry: HistoryEntry | null;

  // Actions
  loadHistory: (limit?: number) => Promise<void>;
  searchHistory: (query: string, limit?: number) => Promise<void>;
  clearSearch: () => void;
  selectEntry: (entry: HistoryEntry | null) => void;
  navigateUp: () => HistoryEntry | null;
  navigateDown: () => HistoryEntry | null;
  executeCommand: (entry: HistoryEntry) => void;
  copyCommand: (command: string) => Promise<boolean>;
  refresh: () => Promise<void>;

  // Utilities
  formatTimestamp: (timestamp: string) => string;
  formatDuration: (duration: number | null) => string;
  getExitStatus: (exitCode: number | null) => 'success' | 'error' | 'unknown';
  groupByDate: (entries?: HistoryEntry[]) => Record<string, HistoryEntry[]>;
}

export const useHistory = (options: UseHistoryOptions): UseHistoryReturn => {
  const { terminalId, autoLoad = true, defaultLimit = 100 } = options;

  const {
    entries,
    filteredEntries,
    searchQuery,
    isLoading,
    error,
    selectedEntry,
    loadHistory: storeLoadHistory,
    searchHistory: storeSearchHistory,
    clearSearch: storeClearSearch,
    selectEntry: storeSelectEntry,
    navigateHistory,
    executeHistoryCommand,
    resetState,
  } = useHistoryStore();

  // Auto-load history on mount
  useEffect(() => {
    if (autoLoad && terminalId) {
      storeLoadHistory(terminalId, defaultLimit);
    }

    // Cleanup on unmount
    return () => {
      resetState();
    };
  }, [terminalId, autoLoad, defaultLimit, storeLoadHistory, resetState]);

  // Wrapped actions with terminal ID
  const loadHistory = useCallback(
    async (limit = defaultLimit) => {
      return storeLoadHistory(terminalId, limit);
    },
    [terminalId, defaultLimit, storeLoadHistory]
  );

  const searchHistory = useCallback(
    async (query: string, limit = 50) => {
      return storeSearchHistory(terminalId, query, limit);
    },
    [terminalId, storeSearchHistory]
  );

  const clearSearch = useCallback(() => {
    storeClearSearch();
  }, [storeClearSearch]);

  const selectEntry = useCallback(
    (entry: HistoryEntry | null) => {
      storeSelectEntry(entry);
    },
    [storeSelectEntry]
  );

  const navigateUp = useCallback(() => {
    return navigateHistory('up');
  }, [navigateHistory]);

  const navigateDown = useCallback(() => {
    return navigateHistory('down');
  }, [navigateHistory]);

  const executeCommand = useCallback(
    (entry: HistoryEntry) => {
      executeHistoryCommand(entry);
    },
    [executeHistoryCommand]
  );

  const copyCommand = useCallback(async (command: string): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(command);
      return true;
    } catch (error) {
      console.error('Failed to copy command:', error);
      return false;
    }
  }, []);

  const refresh = useCallback(async () => {
    return loadHistory();
  }, [loadHistory]);

  // Utility functions
  const formatTimestamp = useCallback((timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  }, []);

  const formatDuration = useCallback((duration: number | null): string => {
    if (duration === null) return 'N/A';

    if (duration < 1000) {
      return `${duration}ms`;
    } else if (duration < 60000) {
      return `${(duration / 1000).toFixed(1)}s`;
    } else {
      const minutes = Math.floor(duration / 60000);
      const seconds = Math.floor((duration % 60000) / 1000);
      return `${minutes}m ${seconds}s`;
    }
  }, []);

  const getExitStatus = useCallback(
    (exitCode: number | null): 'success' | 'error' | 'unknown' => {
      if (exitCode === null) return 'unknown';
      return exitCode === 0 ? 'success' : 'error';
    },
    []
  );

  const groupByDate = useCallback(
    (historyEntries?: HistoryEntry[]): Record<string, HistoryEntry[]> => {
      const entriesToGroup = historyEntries || filteredEntries;

      return entriesToGroup.reduce(
        (groups, entry) => {
          const date = new Date(entry.timestamp);
          const dateKey = date.toDateString();

          if (!groups[dateKey]) {
            groups[dateKey] = [];
          }
          groups[dateKey].push(entry);

          return groups;
        },
        {} as Record<string, HistoryEntry[]>
      );
    },
    [filteredEntries]
  );

  // Listen for execute history command events
  useEffect(() => {
    const handleExecuteCommand = (event: CustomEvent) => {
      const { command } = event.detail;
      // Emit to parent or handle execution
      console.log('Execute history command:', command);
    };

    window.addEventListener(
      'executeHistoryCommand',
      handleExecuteCommand as any
    );

    return () => {
      window.removeEventListener(
        'executeHistoryCommand',
        handleExecuteCommand as any
      );
    };
  }, []);

  return {
    // State
    entries,
    filteredEntries,
    searchQuery,
    isLoading,
    error,
    selectedEntry,

    // Actions
    loadHistory,
    searchHistory,
    clearSearch,
    selectEntry,
    navigateUp,
    navigateDown,
    executeCommand,
    copyCommand,
    refresh,

    // Utilities
    formatTimestamp,
    formatDuration,
    getExitStatus,
    groupByDate,
  };
};

// Hook for keyboard navigation in terminal
export const useHistoryKeyboard = (terminalId: string) => {
  const { navigateUp, navigateDown, selectedEntry } = useHistory({
    terminalId,
    autoLoad: false,
  });

  const handleKeyDown = useCallback(
    (event: KeyboardEvent): HistoryEntry | null => {
      if (event.key === 'ArrowUp' && event.ctrlKey) {
        event.preventDefault();
        return navigateUp();
      } else if (event.key === 'ArrowDown' && event.ctrlKey) {
        event.preventDefault();
        return navigateDown();
      }

      return null;
    },
    [navigateUp, navigateDown]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    selectedEntry,
    navigateUp,
    navigateDown,
  };
};
