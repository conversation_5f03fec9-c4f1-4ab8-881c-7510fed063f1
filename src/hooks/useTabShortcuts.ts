import { useTerminalStore } from '@/stores';
import { SHORTCUT_ACTIONS } from '@/stores/shortcutStore';
import React from 'react';
import { useFeatureShortcuts } from './useShortcuts';

export const useTabShortcuts = () => {
  const { tabs, activeTabId, createTab, closeTab, setActiveTab } =
    useTerminalStore();

  // 定义Tab管理的动作映射
  const tabActions = {
    [SHORTCUT_ACTIONS.CREATE_TAB]: () => {
      const newTabId = createTab();
      setActiveTab(newTabId);
    },

    [SHORTCUT_ACTIONS.CLOSE_TAB]: () => {
      if (tabs.length > 1 && activeTabId) {
        closeTab(activeTabId);
      }
    },

    [SHORTCUT_ACTIONS.NEXT_TAB]: () => {
      if (tabs.length > 1 && activeTabId) {
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const nextIndex = (currentIndex + 1) % tabs.length;
        setActiveTab(tabs[nextIndex].id);
      }
    },

    [SHORTCUT_ACTIONS.PREV_TAB]: () => {
      if (tabs.length > 1 && activeTabId) {
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const prevIndex =
          currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        setActiveTab(tabs[prevIndex].id);
      }
    },

    [SHORTCUT_ACTIONS.SWITCH_TO_TAB]: (_tabIndex?: number) => {
      // 这个动作需要额外处理数字键
      // 在实际的键盘事件中，我们会检查按下的数字键
    },
  };

  // 使用新的快捷键系统
  const { isEnabled } = useFeatureShortcuts(tabActions);

  // 为数字键特别处理（1-9切换到对应Tab）
  const handleNumberKeys = (event: KeyboardEvent) => {
    if (!isEnabled) return;

    const isCtrlOrCmd = event.ctrlKey || event.metaKey;
    if (!isCtrlOrCmd) return;

    if (event.key >= '1' && event.key <= '9') {
      const tabIndex = parseInt(event.key) - 1;
      if (tabIndex < tabs.length) {
        event.preventDefault();
        setActiveTab(tabs[tabIndex].id);
      }
    }
  };

  // 注册数字键处理器
  React.useEffect(() => {
    if (!isEnabled) return;

    document.addEventListener('keydown', handleNumberKeys);

    return () => {
      document.removeEventListener('keydown', handleNumberKeys);
    };
  }, [isEnabled, tabs, setActiveTab]);

  return {
    isEnabled,
    canCloseTab: tabs.length > 1,
    canSwitchTab: tabs.length > 1,
  };
};
