import { SHORTCUT_ACTIONS } from '@/stores/shortcutStore';
import React from 'react';
import { useChatStore } from '../stores/chatStore';
import { useFeatureShortcuts } from './useShortcuts';

interface UseChatShortcutsProps {
  onOpenChat: () => void;
  onCloseChat: () => void;
}

export const useChatShortcuts = ({
  onOpenChat,
  onCloseChat,
}: UseChatShortcutsProps) => {
  const { isActive, endChat } = useChatStore();

  // 定义AI聊天的动作映射
  const chatActions = {
    [SHORTCUT_ACTIONS.OPEN_AI_CHAT]: () => {
      if (!isActive) {
        onOpenChat();
      }
    },

    [SHORTCUT_ACTIONS.TOGGLE_AI_CHAT]: () => {
      if (isActive) {
        endChat();
        onCloseChat();
      } else {
        onOpenChat();
      }
    },
  };

  // 使用新的快捷键系统
  const { isEnabled } = useFeatureShortcuts(chatActions);

  // ESC键特殊处理（关闭聊天）
  React.useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (!isEnabled || !isActive) return;

      if (event.key === 'Escape') {
        event.preventDefault();
        endChat();
        onCloseChat();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [isEnabled, isActive, endChat, onCloseChat]);

  return {
    isActive,
    isEnabled,
  };
};
