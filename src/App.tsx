import { ChatPanel } from '@/components/chat/ChatPanel';
import { MainLayout } from '@/components/layout';
import { TabContentManager } from '@/components/tabs';
import {
  ContextMenuManager,
  ErrorBoundary,
  ThemeProvider,
} from '@/components/ui';
import { useChatShortcuts } from '@/hooks/useChatShortcuts';
import { useChatStore } from '@/stores/chatStore';
import { useState } from 'react';

function App() {
  const [isChatVisible, setIsChatVisible] = useState(false);
  const { startChat } = useChatStore();

  // 聊天快捷键
  useChatShortcuts({
    onOpenChat: () => {
      startChat();
      setIsChatVisible(true);
    },
    onCloseChat: () => {
      setIsChatVisible(false);
    },
  });

  // 处理命令选择（从聊天面板选择命令执行）
  const handleCommandSelect = (command: string) => {
    // TODO: 将命令发送到当前活跃的终端
    console.log('Selected command from chat:', command);
    // 这里需要与终端组件集成，将命令发送到活跃的终端
  };

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <div className="h-screen w-screen overflow-hidden">
          <MainLayout>
            <TabContentManager className="h-full" />
          </MainLayout>

          {/* 聊天面板 */}
          <ChatPanel
            isVisible={isChatVisible}
            onClose={() => setIsChatVisible(false)}
            onCommandSelect={handleCommandSelect}
          />

          {/* 聊天快捷键提示（可选） */}
          {/* {!isChatVisible && (
            <div className="fixed bottom-4 right-4 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm opacity-75 hover:opacity-100 transition-opacity">
              按 Ctrl+Shift+C 打开AI助手
            </div>
          )} */}
        </div>

        {/* 右键菜单管理器 - 单独渲染，不包裹其他组件 */}
        <ContextMenuManager />
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
