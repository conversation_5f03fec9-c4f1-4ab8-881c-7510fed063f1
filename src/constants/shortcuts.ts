import { Platform, ShortcutCategory, ShortcutConfig } from '@/types';
import { detectPlatform } from '@/utils/shortcutUtils';

/**
 * 默认快捷键配置 - macOS版本
 */
const macOSShortcuts: Record<string, ShortcutConfig> = {
  // Tab管理
  'tab.new': {
    action: 'createTab',
    keys: ['cmd', 't'],
    description: '新建终端标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.close': {
    action: 'closeTab',
    keys: ['cmd', 'w'],
    description: '关闭当前标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.next': {
    action: 'nextTab',
    keys: ['cmd', 'shift', ']'],
    description: '切换到下一个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.prev': {
    action: 'prevTab',
    keys: ['cmd', 'shift', '['],
    description: '切换到上一个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.switch1': {
    action: 'switchToTab',
    keys: ['cmd', '1'],
    description: '切换到第1个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.switch2': {
    action: 'switchToTab',
    keys: ['cmd', '2'],
    description: '切换到第2个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.switch3': {
    action: 'switchToTab',
    keys: ['cmd', '3'],
    description: '切换到第3个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.switch4': {
    action: 'switchToTab',
    keys: ['cmd', '4'],
    description: '切换到第4个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'tab.switch5': {
    action: 'switchToTab',
    keys: ['cmd', '5'],
    description: '切换到第5个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },

  // 终端操作
  'terminal.copy': {
    action: 'copySelection',
    keys: ['cmd', 'c'],
    description: '复制选中内容',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'terminal.paste': {
    action: 'pasteText',
    keys: ['cmd', 'v'],
    description: '粘贴文本',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'terminal.selectAll': {
    action: 'selectAll',
    keys: ['cmd', 'a'],
    description: '全选终端内容',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'terminal.clear': {
    action: 'clearTerminal',
    keys: ['cmd', 'k'],
    description: '清空终端内容',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'terminal.interrupt': {
    action: 'interruptProcess',
    keys: ['ctrl', 'c'],
    description: '中断当前进程',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'terminal.find': {
    action: 'openSearch',
    keys: ['cmd', 'f'],
    description: '在终端中搜索',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },

  // AI功能
  'ai.chat': {
    action: 'openAIChat',
    keys: ['cmd', '/'],
    description: '打开AI对话',
    category: ShortcutCategory.AI_FUNCTION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'ai.command': {
    action: 'executeAICommand',
    keys: ['cmd', 'enter'],
    description: '执行AI命令建议',
    category: ShortcutCategory.AI_FUNCTION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'ai.toggle': {
    action: 'toggleAIChat',
    keys: ['cmd', 'shift', 'c'],
    description: '切换AI对话面板',
    category: ShortcutCategory.AI_FUNCTION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },

  // 应用功能
  'app.settings': {
    action: 'openSettings',
    keys: ['cmd', ','],
    description: '打开设置面板',
    category: ShortcutCategory.APPLICATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'app.theme': {
    action: 'toggleTheme',
    keys: ['cmd', 'shift', 't'],
    description: '切换主题',
    category: ShortcutCategory.APPLICATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },
  'app.help': {
    action: 'showHelp',
    keys: ['f1'],
    description: '显示帮助',
    category: ShortcutCategory.APPLICATION,
    global: false,
    enabled: true,
    platform: Platform.MACOS,
  },

  // 全局快捷键
  'global.toggle': {
    action: 'toggleApp',
    keys: ['cmd', '`'],
    description: '显示/隐藏应用窗口',
    category: ShortcutCategory.APPLICATION,
    global: true,
    enabled: true,
    platform: Platform.MACOS,
  },
  'global.newWindow': {
    action: 'newWindow',
    keys: ['cmd', 'shift', 'n'],
    description: '新建窗口',
    category: ShortcutCategory.APPLICATION,
    global: true,
    enabled: true,
    platform: Platform.MACOS,
  },
};

/**
 * 默认快捷键配置 - Windows/Linux版本
 */
const windowsLinuxShortcuts: Record<string, ShortcutConfig> = {
  // Tab管理
  'tab.new': {
    action: 'createTab',
    keys: ['ctrl', 't'],
    description: '新建终端标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.close': {
    action: 'closeTab',
    keys: ['ctrl', 'w'],
    description: '关闭当前标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.next': {
    action: 'nextTab',
    keys: ['ctrl', 'shift', ']'],
    description: '切换到下一个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.prev': {
    action: 'prevTab',
    keys: ['ctrl', 'shift', '['],
    description: '切换到上一个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.switch1': {
    action: 'switchToTab',
    keys: ['ctrl', '1'],
    description: '切换到第1个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.switch2': {
    action: 'switchToTab',
    keys: ['ctrl', '2'],
    description: '切换到第2个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.switch3': {
    action: 'switchToTab',
    keys: ['ctrl', '3'],
    description: '切换到第3个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.switch4': {
    action: 'switchToTab',
    keys: ['ctrl', '4'],
    description: '切换到第4个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },
  'tab.switch5': {
    action: 'switchToTab',
    keys: ['ctrl', '5'],
    description: '切换到第5个标签页',
    category: ShortcutCategory.TAB_MANAGEMENT,
    global: false,
    enabled: true,
  },

  // 终端操作
  'terminal.copy': {
    action: 'copySelection',
    keys: ['ctrl', 'c'],
    description: '复制选中内容',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
  },
  'terminal.paste': {
    action: 'pasteText',
    keys: ['ctrl', 'v'],
    description: '粘贴文本',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
  },
  'terminal.selectAll': {
    action: 'selectAll',
    keys: ['ctrl', 'a'],
    description: '全选终端内容',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
  },
  'terminal.clear': {
    action: 'clearTerminal',
    keys: ['ctrl', 'k'],
    description: '清空终端内容',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
  },
  'terminal.interrupt': {
    action: 'interruptProcess',
    keys: ['ctrl', 'c'],
    description: '中断当前进程',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
  },
  'terminal.find': {
    action: 'openSearch',
    keys: ['ctrl', 'f'],
    description: '在终端中搜索',
    category: ShortcutCategory.TERMINAL_OPERATION,
    global: false,
    enabled: true,
  },

  // AI功能
  'ai.chat': {
    action: 'openAIChat',
    keys: ['ctrl', '/'],
    description: '打开AI对话',
    category: ShortcutCategory.AI_FUNCTION,
    global: false,
    enabled: true,
  },
  'ai.command': {
    action: 'executeAICommand',
    keys: ['ctrl', 'enter'],
    description: '执行AI命令建议',
    category: ShortcutCategory.AI_FUNCTION,
    global: false,
    enabled: true,
  },
  'ai.toggle': {
    action: 'toggleAIChat',
    keys: ['ctrl', 'shift', 'c'],
    description: '切换AI对话面板',
    category: ShortcutCategory.AI_FUNCTION,
    global: false,
    enabled: true,
  },

  // 应用功能
  'app.settings': {
    action: 'openSettings',
    keys: ['ctrl', ','],
    description: '打开设置面板',
    category: ShortcutCategory.APPLICATION,
    global: false,
    enabled: true,
  },
  'app.theme': {
    action: 'toggleTheme',
    keys: ['ctrl', 'shift', 't'],
    description: '切换主题',
    category: ShortcutCategory.APPLICATION,
    global: false,
    enabled: true,
  },
  'app.help': {
    action: 'showHelp',
    keys: ['f1'],
    description: '显示帮助',
    category: ShortcutCategory.APPLICATION,
    global: false,
    enabled: true,
  },

  // 全局快捷键
  'global.toggle': {
    action: 'toggleApp',
    keys: ['ctrl', '`'],
    description: '显示/隐藏应用窗口',
    category: ShortcutCategory.APPLICATION,
    global: true,
    enabled: true,
  },
  'global.newWindow': {
    action: 'newWindow',
    keys: ['ctrl', 'shift', 'n'],
    description: '新建窗口',
    category: ShortcutCategory.APPLICATION,
    global: true,
    enabled: true,
  },
};

/**
 * 获取默认快捷键配置
 * @param platform 目标平台
 * @returns 默认快捷键配置
 */
export const getDefaultShortcuts = (
  platform?: Platform
): Record<string, ShortcutConfig> => {
  const currentPlatform = platform || detectPlatform();

  if (currentPlatform === Platform.MACOS) {
    return macOSShortcuts;
  } else {
    return windowsLinuxShortcuts;
  }
};

/**
 * 按类别获取快捷键
 * @param category 快捷键类别
 * @param platform 平台
 * @returns 该类别的快捷键配置
 */
export const getShortcutsByCategory = (
  category: ShortcutCategory,
  platform?: Platform
): Record<string, ShortcutConfig> => {
  const allShortcuts = getDefaultShortcuts(platform);

  return Object.fromEntries(
    Object.entries(allShortcuts).filter(
      ([, config]) => config.category === category
    )
  );
};

/**
 * 获取全局快捷键列表
 * @param platform 平台
 * @returns 全局快捷键配置
 */
export const getGlobalShortcuts = (
  platform?: Platform
): Record<string, ShortcutConfig> => {
  const allShortcuts = getDefaultShortcuts(platform);

  return Object.fromEntries(
    Object.entries(allShortcuts).filter(([, config]) => config.global)
  );
};

/**
 * 快捷键类别描述
 */
export const SHORTCUT_CATEGORY_LABELS: Record<ShortcutCategory, string> = {
  [ShortcutCategory.TAB_MANAGEMENT]: '标签页管理',
  [ShortcutCategory.TERMINAL_OPERATION]: '终端操作',
  [ShortcutCategory.AI_FUNCTION]: 'AI功能',
  [ShortcutCategory.APPLICATION]: '应用功能',
  [ShortcutCategory.CUSTOM]: '自定义',
};
