// 导出类型定义
export type {
  Theme,
  ThemeColors,
  ThemeEffects,
  ThemeState,
  ThemeTypography,
} from './types';

// 导出预设主题
export {
  defaultDarkTheme,
  defaultLightTheme,
  highContrastTheme,
  presetThemes,
} from './presets';

// 导入类型用于函数定义
import type { Theme } from './types';

// 工具函数
export const kebabCase = (str: string): string => {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
};

// 应用主题到 DOM
export const applyThemeToDOM = (theme: Theme): void => {
  const root = document.documentElement;

  // 设置基础 CSS 自定义属性
  Object.entries(theme.colors).forEach(([key, value]) => {
    if (typeof value === 'string') {
      root.style.setProperty(`--color-${kebabCase(key)}`, value);
    }
  });

  // 设置终端颜色
  Object.entries(theme.colors.terminal).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      // ANSI 颜色数组
      value.forEach((color, index) => {
        root.style.setProperty(`--terminal-ansi-${index}`, color);
      });
    } else {
      root.style.setProperty(`--terminal-${kebabCase(key)}`, value as string);
    }
  });

  // 设置字体样式
  root.style.setProperty('--font-family', theme.typography.fontFamily);
  root.style.setProperty('--font-size', `${theme.typography.fontSize}px`);
  root.style.setProperty(
    '--line-height',
    theme.typography.lineHeight.toString()
  );
  root.style.setProperty(
    '--font-weight',
    theme.typography.fontWeight.toString()
  );

  // 设置视觉效果
  root.style.setProperty('--border-radius', `${theme.effects.borderRadius}px`);
  root.style.setProperty('--box-shadow', theme.effects.boxShadow);
  root.style.setProperty(
    '--opacity-disabled',
    theme.effects.opacity.disabled.toString()
  );
  root.style.setProperty(
    '--opacity-hover',
    theme.effects.opacity.hover.toString()
  );
  root.style.setProperty(
    '--transition-duration',
    theme.effects.transition.duration
  );
  root.style.setProperty(
    '--transition-easing',
    theme.effects.transition.easing
  );

  // 设置主题类型
  root.setAttribute('data-theme', theme.type);
  root.className =
    root.className.replace(/theme-\w+/g, '') + ` theme-${theme.id}`;
};

// 获取系统主题偏好
export const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window !== 'undefined') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';
  }
  return 'dark';
};

// 主题验证函数
export const validateTheme = (theme: Partial<Theme>): theme is Theme => {
  return !!(
    theme.id &&
    theme.name &&
    theme.type &&
    theme.colors &&
    theme.typography &&
    theme.effects
  );
};
