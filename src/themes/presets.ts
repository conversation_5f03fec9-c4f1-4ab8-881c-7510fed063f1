import { Theme } from './types';

export const defaultDarkTheme: Theme = {
  id: 'dark-default',
  name: '暗色主题',
  type: 'dark',
  colors: {
    primary: '#3B82F6',
    secondary: '#6B7280',
    background: '#0F172A',
    surface: '#1E293B',
    text: '#F8FAFC',
    textSecondary: '#CBD5E1',
    border: '#334155',

    terminal: {
      background: '#0F172A',
      foreground: '#F8FAFC',
      cursor: '#3B82F6',
      selection: 'rgba(59, 130, 246, 0.3)',
      ansi: [
        '#1E293B',
        '#EF4444',
        '#10B981',
        '#F59E0B',
        '#3B82F6',
        '#8B5CF6',
        '#06B6D4',
        '#F8FAFC',
        '#475569',
        '#F87171',
        '#34D399',
        '#FBBF24',
        '#60A5FA',
        '#A78BFA',
        '#22D3EE',
        '#FFFFFF',
      ],
    },

    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    hover: 'rgba(255, 255, 255, 0.1)',
    active: 'rgba(255, 255, 255, 0.2)',
    focus: '#3B82F6',
    disabled: 'rgba(255, 255, 255, 0.3)',
  },
  typography: {
    fontFamily: '"Consolas", "Monaco", "Courier New", monospace',
    fontSize: 14,
    lineHeight: 1.5,
    fontWeight: 400,
  },
  effects: {
    borderRadius: 6,
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: {
      disabled: 0.5,
      hover: 0.8,
    },
    transition: {
      duration: '200ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};

export const defaultLightTheme: Theme = {
  id: 'light-default',
  name: '亮色主题',
  type: 'light',
  colors: {
    primary: '#2563EB',
    secondary: '#6B7280',
    background: '#FFFFFF',
    surface: '#F8FAFC',
    text: '#1E293B',
    textSecondary: '#475569',
    border: '#E2E8F0',

    terminal: {
      background: '#FFFFFF',
      foreground: '#1E293B',
      cursor: '#2563EB',
      selection: 'rgba(37, 99, 235, 0.2)',
      ansi: [
        '#1E293B',
        '#DC2626',
        '#059669',
        '#D97706',
        '#2563EB',
        '#7C3AED',
        '#0891B2',
        '#F8FAFC',
        '#475569',
        '#EF4444',
        '#10B981',
        '#F59E0B',
        '#3B82F6',
        '#8B5CF6',
        '#06B6D4',
        '#FFFFFF',
      ],
    },

    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
    info: '#2563EB',

    hover: 'rgba(0, 0, 0, 0.05)',
    active: 'rgba(0, 0, 0, 0.1)',
    focus: '#2563EB',
    disabled: 'rgba(0, 0, 0, 0.3)',
  },
  typography: {
    fontFamily: '"Consolas", "Monaco", "Courier New", monospace',
    fontSize: 14,
    lineHeight: 1.5,
    fontWeight: 400,
  },
  effects: {
    borderRadius: 6,
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
    opacity: {
      disabled: 0.5,
      hover: 0.8,
    },
    transition: {
      duration: '200ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};

// 高对比度主题
export const highContrastTheme: Theme = {
  id: 'high-contrast',
  name: '高对比度',
  type: 'dark',
  colors: {
    primary: '#FFFF00',
    secondary: '#CCCCCC',
    background: '#000000',
    surface: '#1A1A1A',
    text: '#FFFFFF',
    textSecondary: '#CCCCCC',
    border: '#444444',

    terminal: {
      background: '#000000',
      foreground: '#FFFFFF',
      cursor: '#FFFF00',
      selection: 'rgba(255, 255, 0, 0.4)',
      ansi: [
        '#000000',
        '#FF0000',
        '#00FF00',
        '#FFFF00',
        '#0000FF',
        '#FF00FF',
        '#00FFFF',
        '#FFFFFF',
        '#808080',
        '#FF8080',
        '#80FF80',
        '#FFFF80',
        '#8080FF',
        '#FF80FF',
        '#80FFFF',
        '#FFFFFF',
      ],
    },

    success: '#00FF00',
    warning: '#FFFF00',
    error: '#FF0000',
    info: '#00FFFF',

    hover: 'rgba(255, 255, 255, 0.2)',
    active: 'rgba(255, 255, 255, 0.3)',
    focus: '#FFFF00',
    disabled: 'rgba(255, 255, 255, 0.4)',
  },
  typography: {
    fontFamily: '"Consolas", "Monaco", "Courier New", monospace',
    fontSize: 14,
    lineHeight: 1.5,
    fontWeight: 500,
  },
  effects: {
    borderRadius: 4,
    boxShadow: '0 2px 4px rgba(255, 255, 255, 0.1)',
    opacity: {
      disabled: 0.6,
      hover: 0.9,
    },
    transition: {
      duration: '150ms',
      easing: 'linear',
    },
  },
};

// 所有预设主题
export const presetThemes: Theme[] = [
  defaultDarkTheme,
  defaultLightTheme,
  highContrastTheme,
];
