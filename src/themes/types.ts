// 主题类型定义
export interface Theme {
  id: string;
  name: string;
  type: 'light' | 'dark' | 'auto';
  colors: ThemeColors;
  typography: ThemeTypography;
  effects: ThemeEffects;
}

// 颜色配置
export interface ThemeColors {
  // 基础颜色
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;

  // 终端颜色
  terminal: {
    background: string;
    foreground: string;
    cursor: string;
    selection: string;
    ansi: string[]; // 16 色 ANSI 颜色
  };

  // 状态颜色
  success: string;
  warning: string;
  error: string;
  info: string;

  // 交互颜色
  hover: string;
  active: string;
  focus: string;
  disabled: string;
}

// 字体配置
export interface ThemeTypography {
  fontFamily: string;
  fontSize: number;
  lineHeight: number;
  fontWeight: number;
}

// 视觉效果
export interface ThemeEffects {
  borderRadius: number;
  boxShadow: string;
  opacity: {
    disabled: number;
    hover: number;
  };
  transition: {
    duration: string;
    easing: string;
  };
}

// 主题状态接口
export interface ThemeState {
  // 状态
  currentTheme: Theme;
  availableThemes: Theme[];
  systemTheme: 'light' | 'dark';

  // 操作
  setTheme: (themeId: string) => void;
  addCustomTheme: (theme: Theme) => void;
  removeCustomTheme: (themeId: string) => void;
  updateCustomTheme: (theme: Theme) => void;
  detectSystemTheme: () => void;

  // 配置
  autoSwitchEnabled: boolean;
  setAutoSwitch: (enabled: boolean) => void;
}
