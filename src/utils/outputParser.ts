import { TerminalLine, TerminalTheme, TextAttribute } from '../types';

export class OutputParser {
  private theme: TerminalTheme;

  constructor(theme: TerminalTheme) {
    this.theme = theme;
  }

  // 解析 ANSI 转义序列
  parseAnsiOutput(output: string): TerminalLine {
    const line: TerminalLine = {
      text: '',
      attributes: [],
      timestamp: Date.now(),
    };

    let currentIndex = 0;
    let currentAttributes: Partial<TextAttribute> = {};

    // 解析 ANSI 代码
    const ESC = String.fromCharCode(27);
    const ansiRegex = new RegExp(`${ESC}\\[[0-9;]*m`, 'g');
    let match;
    let lastIndex = 0;

    while ((match = ansiRegex.exec(output)) !== null) {
      // 添加前面的普通文本
      if (match.index > lastIndex) {
        const text = output.slice(lastIndex, match.index);
        line.text += text;

        if (Object.keys(currentAttributes).length > 0) {
          line.attributes.push({
            start: currentIndex,
            length: text.length,
            ...currentAttributes,
          });
        }

        currentIndex += text.length;
      }

      // 解析 ANSI 代码
      const ansiCode = match[0];
      currentAttributes = this.parseAnsiCode(ansiCode, currentAttributes);

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余文本
    if (lastIndex < output.length) {
      const text = output.slice(lastIndex);
      line.text += text;

      if (Object.keys(currentAttributes).length > 0) {
        line.attributes.push({
          start: currentIndex,
          length: text.length,
          ...currentAttributes,
        });
      }
    }

    return line;
  }

  private parseAnsiCode(
    code: string,
    current: Partial<TextAttribute>
  ): Partial<TextAttribute> {
    // 解析具体的 ANSI 颜色和样式代码
    const nums = code
      .slice(2, -1)
      .split(';')
      .map(n => parseInt(n) || 0);
    const newAttributes = { ...current };

    for (const num of nums) {
      switch (num) {
        case 0: // 重置
          return {};
        case 1: // 粗体
          newAttributes.bold = true;
          break;
        case 3: // 斜体
          newAttributes.italic = true;
          break;
        case 4: // 下划线
          newAttributes.underline = true;
          break;
        case 22: // 取消粗体
          newAttributes.bold = false;
          break;
        case 23: // 取消斜体
          newAttributes.italic = false;
          break;
        case 24: // 取消下划线
          newAttributes.underline = false;
          break;
        case 30:
        case 31:
        case 32:
        case 33:
        case 34:
        case 35:
        case 36:
        case 37:
          newAttributes.foreground = this.getAnsiColor(num - 30);
          break;
        case 40:
        case 41:
        case 42:
        case 43:
        case 44:
        case 45:
        case 46:
        case 47:
          newAttributes.background = this.getAnsiColor(num - 40);
          break;
        case 90:
        case 91:
        case 92:
        case 93:
        case 94:
        case 95:
        case 96:
        case 97:
          newAttributes.foreground = this.getBrightAnsiColor(num - 90);
          break;
        case 100:
        case 101:
        case 102:
        case 103:
        case 104:
        case 105:
        case 106:
        case 107:
          newAttributes.background = this.getBrightAnsiColor(num - 100);
          break;
        case 39: // 默认前景色
          newAttributes.foreground = this.theme.foreground;
          break;
        case 49: // 默认背景色
          newAttributes.background = undefined;
          break;
      }
    }

    return newAttributes;
  }

  private getAnsiColor(index: number): string {
    const colors = [
      this.theme.black,
      this.theme.red,
      this.theme.green,
      this.theme.yellow,
      this.theme.blue,
      this.theme.magenta,
      this.theme.cyan,
      this.theme.white,
    ];
    return colors[index] || this.theme.foreground;
  }

  private getBrightAnsiColor(index: number): string {
    const colors = [
      this.theme.brightBlack,
      this.theme.brightRed,
      this.theme.brightGreen,
      this.theme.brightYellow,
      this.theme.brightBlue,
      this.theme.brightMagenta,
      this.theme.brightCyan,
      this.theme.brightWhite,
    ];
    return colors[index] || this.theme.foreground;
  }

  // 解析多行输出
  parseMultilineOutput(output: string): TerminalLine[] {
    const lines = output.split('\n');
    return lines.map(line => this.parseAnsiOutput(line));
  }

  // 清理输出中的控制字符
  cleanOutput(output: string): string {
    // 移除除了 ANSI 转义序列外的其他控制字符
    const controlChars = [
      ...Array.from({ length: 9 }, (_, i) => String.fromCharCode(i)), // 0-8
      String.fromCharCode(11), // VT
      String.fromCharCode(12), // FF
      ...Array.from({ length: 18 }, (_, i) => String.fromCharCode(14 + i)), // 14-31
      String.fromCharCode(127), // DEL
    ];
    const controlCharRegex = new RegExp(`[${controlChars.join('')}]`, 'g');
    return output.replace(controlCharRegex, '');
  }

  // 估算文本显示宽度（考虑 Unicode 字符）
  getDisplayWidth(text: string): number {
    let width = 0;
    for (const char of text) {
      const code = char.codePointAt(0);
      if (code !== undefined) {
        // 简单的宽度估算，可以根据需要改进
        if (
          code > 0x1100 &&
          (code <= 0x115f || // Hangul Jamo
            code === 0x2329 ||
            code === 0x232a ||
            (code >= 0x2e80 && code <= 0x3247) || // CJK
            (code >= 0x3250 && code <= 0x4dbf) ||
            (code >= 0x4e00 && code <= 0x9fff) ||
            (code >= 0xa960 && code <= 0xa97f) ||
            (code >= 0xac00 && code <= 0xd7a3) ||
            (code >= 0xf900 && code <= 0xfaff) ||
            (code >= 0xfe10 && code <= 0xfe19) ||
            (code >= 0xfe30 && code <= 0xfe6f) ||
            (code >= 0xff00 && code <= 0xff60) ||
            (code >= 0xffe0 && code <= 0xffe6) ||
            (code >= 0x20000 && code <= 0x2fffd) ||
            (code >= 0x30000 && code <= 0x3fffd))
        ) {
          width += 2; // 全角字符
        } else {
          width += 1; // 半角字符
        }
      }
    }
    return width;
  }
}
