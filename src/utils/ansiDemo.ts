import { AnsiParser } from './ansiParser';

// 演示改进的ANSI解析器
export function demonstrateAnsiParser() {
  const parser = new AnsiParser();

  console.log('=== ANSI解析器改进演示 ===\n');

  // 测试用例1：您日志中的实际输出
  console.log('1. 处理复杂的终端输出（包含光标控制和颜色）:');
  const complexInput = '\u001b[0m\u001b[27m\u001b[24m\u001b[J(base) \u001b[1m\u001b[34m#\u001b[00m \u001b[36mmaojie \u001b[00m@ \u001b[32mMacBook-Pro-3 \u001b[00min \u001b[1m\u001b[33m~\u001b[00m [13:35:06]';

  console.log('原始输入:', JSON.stringify(complexInput));
  const segments1 = parser.parseLine(complexInput);
  console.log('解析结果:');
  segments1.forEach((segment, i) => {
    console.log(`  段落${i + 1}: "${segment.text}" 样式:`, segment.style);
  });
  console.log('合并文本:', segments1.map(s => s.text).join(''));
  console.log();

  // 测试用例2：颜色和样式
  console.log('2. 处理颜色和样式:');
  const colorInput = '\u001b[1m\u001b[31m错误:\u001b[0m \u001b[33m警告信息\u001b[0m \u001b[32m成功\u001b[0m';

  console.log('原始输入:', JSON.stringify(colorInput));
  const segments2 = parser.parseLine(colorInput);
  console.log('解析结果:');
  segments2.forEach((segment, i) => {
    console.log(`  段落${i + 1}: "${segment.text}" 样式:`, segment.style);
  });
  console.log();

  // 测试用例3：光标控制序列
  console.log('3. 处理光标控制序列:');
  const cursorInput = '\u001b[A\u001b[B\u001b[C\u001b[D\u001b[H\u001b[J\u001b[K正常文本\u001b[2K';

  console.log('原始输入:', JSON.stringify(cursorInput));
  const segments3 = parser.parseLine(cursorInput);
  console.log('解析结果:');
  segments3.forEach((segment, i) => {
    console.log(`  段落${i + 1}: "${segment.text}" 样式:`, segment.style);
  });
  console.log();

  // 测试用例4：私有模式序列
  console.log('4. 处理私有模式序列:');
  const privateInput = '\u001b[?25h\u001b[?1049h显示的文本\u001b[?25l\u001b[?1049l';

  console.log('原始输入:', JSON.stringify(privateInput));
  const segments4 = parser.parseLine(privateInput);
  console.log('解析结果:');
  segments4.forEach((segment, i) => {
    console.log(`  段落${i + 1}: "${segment.text}" 样式:`, segment.style);
  });
  console.log();

  // 测试用例5：OSC序列
  console.log('5. 处理OSC序列（操作系统命令）:');
  const oscInput = '\u001b]0;窗口标题\u0007实际内容\u001b]2;图标标题\u0007';

  console.log('原始输入:', JSON.stringify(oscInput));
  const segments5 = parser.parseLine(oscInput);
  console.log('解析结果:');
  segments5.forEach((segment, i) => {
    console.log(`  段落${i + 1}: "${segment.text}" 样式:`, segment.style);
  });
  console.log();

  // 测试用例6：特殊字符处理
  console.log('6. 处理特殊字符:');
  const specialInput = 'Hello\tWorld\b!\n中文测试\u001b[1m粗体中文\u001b[0m';

  console.log('原始输入:', JSON.stringify(specialInput));
  const segments6 = parser.parseLine(specialInput);
  console.log('解析结果:');
  segments6.forEach((segment, i) => {
    console.log(`  段落${i + 1}: "${segment.text}" 样式:`, segment.style);
  });
  console.log();

  console.log('=== 演示完成 ===');
}

// 如果直接运行此文件，执行演示
if (typeof window === 'undefined' && typeof process !== 'undefined') {
  demonstrateAnsiParser();
}
