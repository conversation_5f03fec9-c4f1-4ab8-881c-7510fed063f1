// ANSI转义序列解析器
// 用于解析终端输出中的颜色、格式和控制序列

export interface AnsiStyle {
  color?: string;
  backgroundColor?: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  strikethrough?: boolean;
  dim?: boolean;
  blink?: boolean;
  reverse?: boolean;
}

export interface ParsedSegment {
  text: string;
  style: AnsiStyle;
}

export interface TerminalState {
  currentStyle: AnsiStyle;
  cursorX: number;
  cursorY: number;
  lines: ParsedSegment[][];
  screenWidth: number;
  screenHeight: number;
}

// ANSI颜色映射
const ANSI_COLORS = {
  // 标准颜色 (30-37, 40-47)
  black: '#000000',
  red: '#cd0000',
  green: '#00cd00',
  yellow: '#cdcd00',
  blue: '#0000ee',
  magenta: '#cd00cd',
  cyan: '#00cdcd',
  white: '#e5e5e5',

  // 亮色 (90-97, 100-107)
  brightBlack: '#7f7f7f',
  brightRed: '#ff0000',
  brightGreen: '#00ff00',
  brightYellow: '#ffff00',
  brightBlue: '#5c5cff',
  brightMagenta: '#ff00ff',
  brightCyan: '#00ffff',
  brightWhite: '#ffffff',
};

const COLOR_MAP = [
  ANSI_COLORS.black,
  ANSI_COLORS.red,
  ANSI_COLORS.green,
  ANSI_COLORS.yellow,
  ANSI_COLORS.blue,
  ANSI_COLORS.magenta,
  ANSI_COLORS.cyan,
  ANSI_COLORS.white,
];

const BRIGHT_COLOR_MAP = [
  ANSI_COLORS.brightBlack,
  ANSI_COLORS.brightRed,
  ANSI_COLORS.brightGreen,
  ANSI_COLORS.brightYellow,
  ANSI_COLORS.brightBlue,
  ANSI_COLORS.brightMagenta,
  ANSI_COLORS.brightCyan,
  ANSI_COLORS.brightWhite,
];

export class AnsiParser {
  private state: TerminalState;

  constructor(width: number = 80, height: number = 24) {
    this.state = {
      currentStyle: {},
      cursorX: 0,
      cursorY: 0,
      lines: [],
      screenWidth: width,
      screenHeight: height,
    };

    // 初始化空行
    this.initializeLines();
  }

  private initializeLines() {
    this.state.lines = [];
    for (let i = 0; i < this.state.screenHeight; i++) {
      this.state.lines.push([]);
    }
  }

  /**
   * 解析ANSI转义序列并返回格式化的段落
   */
  public parse(input: string): ParsedSegment[][] {
    // 重置状态
    this.state.currentStyle = {};
    this.state.cursorX = 0;
    this.state.cursorY = 0;
    this.initializeLines();

    let i = 0;
    let currentText = '';

    while (i < input.length) {
      const char = input[i];

      if (char === '\x1b' || char === '\u001b') {
        // 遇到ESC字符，处理之前的文本
        if (currentText) {
          this.addText(currentText);
          currentText = '';
        }

        // 解析转义序列
        const escapeResult = this.parseEscapeSequence(input.slice(i));
        if (escapeResult && escapeResult.style) {
          // 应用样式变化
          this.state.currentStyle = this.applyStyleChange(
            this.state.currentStyle,
            escapeResult.style
          );
          i += escapeResult.length;
          continue;
        }
      } else if (char === '\n') {
        // 换行符
        if (currentText) {
          this.addText(currentText);
          currentText = '';
        }
        this.newLine();
      } else if (char === '\r') {
        // 回车符，移动到行首
        if (currentText) {
          this.addText(currentText);
          currentText = '';
        }
        this.state.cursorX = 0;
      } else if (char === '\t') {
        // 制表符
        if (currentText) {
          this.addText(currentText);
          currentText = '';
        }
        this.addText('    '); // 简单处理为4个空格
      } else if (char === '\b') {
        // 退格符
        if (currentText) {
          this.addText(currentText);
          currentText = '';
        }
        if (this.state.cursorX > 0) {
          this.state.cursorX--;
        }
      } else {
        // 普通字符
        currentText += char;
      }

      i++;
    }

    // 处理剩余文本
    if (currentText) {
      this.addText(currentText);
    }

    return this.state.lines;
  }

  /**
   * 解析单行ANSI字符串为段落数组
   */
  public parseLine(input: string): ParsedSegment[] {
    const segments: ParsedSegment[] = [];
    let currentStyle: AnsiStyle = {};
    let currentText = '';
    let i = 0;

    const flushText = () => {
      if (currentText) {
        segments.push({
          text: currentText,
          style: { ...currentStyle },
        });
        currentText = '';
      }
    };

    while (i < input.length) {
      const char = input[i];

      if (char === '\x1b' || char === '\u001b') {
        flushText();

        // 解析转义序列
        const escapeResult = this.parseEscapeSequence(input.slice(i));
        if (escapeResult) {
          if (escapeResult.style) {
            // 应用样式变化
            currentStyle = this.applyStyleChange(
              currentStyle,
              escapeResult.style
            );
          }
          i += escapeResult.length;
          continue;
        } else {
          // 如果解析失败，跳过这个字符
          i++;
          continue;
        }
      }

      // 处理控制字符
      if (char === '\n' || char === '\r') {
        // 换行符和回车符结束当前行
        flushText();
        break;
      } else if (char === '\t') {
        currentText += '    ';
      } else if (char === '\b') {
        // 退格符 - 删除最后一个字符
        if (currentText.length > 0) {
          currentText = currentText.slice(0, -1);
        }
      } else if (char.charCodeAt(0) >= 32 || char === ' ') {
        // 添加可打印字符和空格，包括中文字符
        currentText += char;
      } else if (char.charCodeAt(0) > 127) {
        // 添加所有Unicode字符（包括中文）
        currentText += char;
      }
      // 忽略其他控制字符

      i++;
    }

    flushText();

    // 如果没有任何段落，返回一个空的段落
    if (segments.length === 0) {
      segments.push({ text: '', style: {} });
    }

    return segments;
  }

  private parseEscapeSequence(
    sequence: string
  ): { length: number; style?: Partial<AnsiStyle> } | null {
    if (sequence.length < 2) return null;

    // ESC [ 开始的CSI序列
    if (sequence[1] === '[') {
      return this.parseCSISequence(sequence);
    }

    // ESC ] 开始的OSC序列 (操作系统命令)
    if (sequence[1] === ']') {
      return this.parseOSCSequence(sequence);
    }

    // 处理其他常见的转义序列
    const secondChar = sequence[1];
    switch (secondChar) {
      case '7': // 保存光标位置
      case '8': // 恢复光标位置
      case 'M': // 反向换行
      case 'D': // 换行
      case 'E': // 下一行
      case 'H': // 设置制表符
      case 'c': // 重置终端
        return { length: 2 };
      case '=': // 应用程序键盘模式
      case '>': // 数字键盘模式
        return { length: 2 };
      default:
        // 未知的转义序列，跳过
        return { length: 2 };
    }
  }

  private parseCSISequence(
    sequence: string
  ): { length: number; style?: Partial<AnsiStyle> } | null {
    if (sequence.length < 3) return null;

    let i = 2; // 跳过 ESC [
    let params = '';
    let hasQuestionMark = false;

    // 检查是否有问号前缀（私有模式）
    if (sequence[i] === '?') {
      hasQuestionMark = true;
      i++;
    }

    // 读取参数直到找到终止字符
    while (i < sequence.length) {
      const char = sequence[i];
      if ((char >= '0' && char <= '9') || char === ';' || char === ':') {
        params += char;
      } else {
        // 找到终止字符
        const command = char;
        const paramList = params
          ? params.split(';').map(p => parseInt(p) || 0)
          : [0];

        const result = this.processCSICommand(command, paramList, hasQuestionMark);
        return {
          length: i + 1,
          style: result,
        };
      }
      i++;
    }

    return null;
  }

  private parseOSCSequence(sequence: string): { length: number } | null {
    // OSC序列通常以 ESC ] 开始，以 BEL (\x07) 或 ESC \ 结束
    let i = 2; // 跳过 ESC ]

    while (i < sequence.length) {
      if (sequence[i] === '\x07') {
        return { length: i + 1 };
      } else if (
        sequence[i] === '\x1b' &&
        i + 1 < sequence.length &&
        sequence[i + 1] === '\\'
      ) {
        return { length: i + 2 };
      }
      i++;
    }

    // 没有找到结束符，尝试到行末
    return { length: Math.min(100, sequence.length) };
  }

  private processCSICommand(
    command: string,
    params: number[],
    hasQuestionMark: boolean = false
  ): Partial<AnsiStyle> | undefined {
    // 处理私有模式序列（以?开头）
    if (hasQuestionMark) {
      switch (command) {
        case 'h': // 设置私有模式
        case 'l': // 重置私有模式
          // 这些通常用于设置终端特性，不影响文本样式
          return undefined;
        default:
          return undefined;
      }
    }

    switch (command) {
      case 'm': // SGR (Select Graphic Rendition)
        return this.processSGR(params);
      case 'H': // CUP (Cursor Position)
      case 'f': {
        // HVP (Horizontal and Vertical Position)
        // 移动光标
        const row = (params[0] || 1) - 1;
        const col = (params[1] || 1) - 1;
        this.state.cursorY = Math.max(
          0,
          Math.min(row, this.state.screenHeight - 1)
        );
        this.state.cursorX = Math.max(
          0,
          Math.min(col, this.state.screenWidth - 1)
        );
        break;
      }
      case 'A': // CUU (Cursor Up)
        this.state.cursorY = Math.max(0, this.state.cursorY - (params[0] || 1));
        break;
      case 'B': // CUD (Cursor Down)
        this.state.cursorY = Math.min(
          this.state.screenHeight - 1,
          this.state.cursorY + (params[0] || 1)
        );
        break;
      case 'C': // CUF (Cursor Forward)
        this.state.cursorX = Math.min(
          this.state.screenWidth - 1,
          this.state.cursorX + (params[0] || 1)
        );
        break;
      case 'D': // CUB (Cursor Back)
        this.state.cursorX = Math.max(0, this.state.cursorX - (params[0] || 1));
        break;
      case 'K': // EL (Erase in Line)
        // 清除行的部分内容
        this.eraseLine(params[0] || 0);
        break;
      case 'J': // ED (Erase in Display)
        // 清除屏幕的部分内容
        this.eraseDisplay(params[0] || 0);
        break;
    }

    return undefined;
  }

  private processSGR(params: number[]): Partial<AnsiStyle> {
    if (params.length === 0) params = [0];

    const style: Partial<AnsiStyle> = {};

    for (let i = 0; i < params.length; i++) {
      const param = params[i];

      switch (param) {
        case 0: // Reset
          return {};
        case 1: // Bold
          style.bold = true;
          break;
        case 2: // Dim
          style.dim = true;
          break;
        case 3: // Italic
          style.italic = true;
          break;
        case 4: // Underline
          style.underline = true;
          break;
        case 5: // Blink
          style.blink = true;
          break;
        case 7: // Reverse
          style.reverse = true;
          break;
        case 9: // Strikethrough
          style.strikethrough = true;
          break;
        case 22: // Normal intensity
          style.bold = false;
          style.dim = false;
          break;
        case 23: // Not italic
          style.italic = false;
          break;
        case 24: // Not underlined
          style.underline = false;
          break;
        case 25: // Not blinking
          style.blink = false;
          break;
        case 27: // Not reversed
          style.reverse = false;
          break;
        case 29: // Not strikethrough
          style.strikethrough = false;
          break;
        // 前景色 (30-37)
        case 30:
          style.color = COLOR_MAP[0];
          break;
        case 31:
          style.color = COLOR_MAP[1];
          break;
        case 32:
          style.color = COLOR_MAP[2];
          break;
        case 33:
          style.color = COLOR_MAP[3];
          break;
        case 34:
          style.color = COLOR_MAP[4];
          break;
        case 35:
          style.color = COLOR_MAP[5];
          break;
        case 36:
          style.color = COLOR_MAP[6];
          break;
        case 37:
          style.color = COLOR_MAP[7];
          break;
        case 39: // Default foreground
          style.color = undefined;
          break;
        // 背景色 (40-47)
        case 40:
          style.backgroundColor = COLOR_MAP[0];
          break;
        case 41:
          style.backgroundColor = COLOR_MAP[1];
          break;
        case 42:
          style.backgroundColor = COLOR_MAP[2];
          break;
        case 43:
          style.backgroundColor = COLOR_MAP[3];
          break;
        case 44:
          style.backgroundColor = COLOR_MAP[4];
          break;
        case 45:
          style.backgroundColor = COLOR_MAP[5];
          break;
        case 46:
          style.backgroundColor = COLOR_MAP[6];
          break;
        case 47:
          style.backgroundColor = COLOR_MAP[7];
          break;
        case 49: // Default background
          style.backgroundColor = undefined;
          break;
        // 亮前景色 (90-97)
        case 90:
          style.color = BRIGHT_COLOR_MAP[0];
          break;
        case 91:
          style.color = BRIGHT_COLOR_MAP[1];
          break;
        case 92:
          style.color = BRIGHT_COLOR_MAP[2];
          break;
        case 93:
          style.color = BRIGHT_COLOR_MAP[3];
          break;
        case 94:
          style.color = BRIGHT_COLOR_MAP[4];
          break;
        case 95:
          style.color = BRIGHT_COLOR_MAP[5];
          break;
        case 96:
          style.color = BRIGHT_COLOR_MAP[6];
          break;
        case 97:
          style.color = BRIGHT_COLOR_MAP[7];
          break;
        // 亮背景色 (100-107)
        case 100:
          style.backgroundColor = BRIGHT_COLOR_MAP[0];
          break;
        case 101:
          style.backgroundColor = BRIGHT_COLOR_MAP[1];
          break;
        case 102:
          style.backgroundColor = BRIGHT_COLOR_MAP[2];
          break;
        case 103:
          style.backgroundColor = BRIGHT_COLOR_MAP[3];
          break;
        case 104:
          style.backgroundColor = BRIGHT_COLOR_MAP[4];
          break;
        case 105:
          style.backgroundColor = BRIGHT_COLOR_MAP[5];
          break;
        case 106:
          style.backgroundColor = BRIGHT_COLOR_MAP[6];
          break;
        case 107:
          style.backgroundColor = BRIGHT_COLOR_MAP[7];
          break;
        // 256色支持 (38;5;n 和 48;5;n)
        case 38:
          if (i + 2 < params.length && params[i + 1] === 5) {
            style.color = this.get256Color(params[i + 2]);
            i += 2;
          }
          break;
        case 48:
          if (i + 2 < params.length && params[i + 1] === 5) {
            style.backgroundColor = this.get256Color(params[i + 2]);
            i += 2;
          }
          break;
      }
    }

    return style;
  }

  private get256Color(colorIndex: number): string {
    // 256色调色板的简化实现
    if (colorIndex < 16) {
      // 标准16色
      return colorIndex < 8
        ? COLOR_MAP[colorIndex]
        : BRIGHT_COLOR_MAP[colorIndex - 8];
    } else if (colorIndex < 232) {
      // 216色立方体
      const index = colorIndex - 16;
      const r = Math.floor(index / 36) * 51;
      const g = (Math.floor(index / 6) % 6) * 51;
      const b = (index % 6) * 51;
      return `rgb(${r}, ${g}, ${b})`;
    } else {
      // 24级灰度
      const gray = (colorIndex - 232) * 10 + 8;
      return `rgb(${gray}, ${gray}, ${gray})`;
    }
  }

  private applyStyleChange(
    currentStyle: AnsiStyle,
    styleChange: Partial<AnsiStyle>
  ): AnsiStyle {
    // 如果styleChange是空对象，说明是重置序列
    if (Object.keys(styleChange).length === 0) {
      return {};
    }
    return { ...currentStyle, ...styleChange };
  }

  /**
   * 计算字符的显示宽度
   * 中文字符、全角字符占2个字符宽度，英文字符占1个字符宽度
   */
  private getCharDisplayWidth(char: string): number {
    const code = char.charCodeAt(0);

    // 中文字符范围
    if (
      (code >= 0x4e00 && code <= 0x9fff) || // 基本中文字符
      (code >= 0x3400 && code <= 0x4dbf) || // 扩展A
      (code >= 0xf900 && code <= 0xfaff) || // 兼容字符
      (code >= 0x2e80 && code <= 0x2eff) || // 中日韩符号
      (code >= 0x2f00 && code <= 0x2fdf) || // 康熙部首
      (code >= 0x3000 && code <= 0x303f) || // 中日韩符号和标点
      (code >= 0x3040 && code <= 0x309f) || // 平假名
      (code >= 0x30a0 && code <= 0x30ff) || // 片假名
      (code >= 0xff00 && code <= 0xffef) // 全角字符
    ) {
      return 2;
    }

    return 1;
  }

  /**
   * 计算字符串的显示宽度
   */
  private getStringDisplayWidth(text: string): number {
    let width = 0;
    for (let i = 0; i < text.length; i++) {
      width += this.getCharDisplayWidth(text[i]);
    }
    return width;
  }

  private addText(text: string) {
    if (!text) return;

    // 确保当前行存在
    while (this.state.cursorY >= this.state.lines.length) {
      this.state.lines.push([]);
    }

    const currentLine = this.state.lines[this.state.cursorY];

    // 添加文本段落
    currentLine.push({
      text,
      style: { ...this.state.currentStyle },
    });

    // 更新光标位置 - 使用正确的字符宽度计算
    this.state.cursorX += this.getStringDisplayWidth(text);

    // 处理行溢出
    if (this.state.cursorX >= this.state.screenWidth) {
      this.newLine();
    }
  }

  private newLine() {
    this.state.cursorX = 0;
    this.state.cursorY++;

    // 确保不超出屏幕高度
    if (this.state.cursorY >= this.state.screenHeight) {
      // 滚动屏幕：移除第一行，添加新行
      this.state.lines.shift();
      this.state.lines.push([]);
      this.state.cursorY = this.state.screenHeight - 1;
    }
  }

  private eraseLine(mode: number) {
    if (this.state.cursorY >= this.state.lines.length) return;

    switch (mode) {
      case 0: // 从光标到行末
        // 简化处理：清空整行
        this.state.lines[this.state.cursorY] = [];
        break;
      case 1: // 从行首到光标
        // 简化处理：清空整行
        this.state.lines[this.state.cursorY] = [];
        break;
      case 2: // 整行
        this.state.lines[this.state.cursorY] = [];
        break;
    }
  }

  private eraseDisplay(mode: number) {
    switch (mode) {
      case 0: // 从光标到屏幕末尾
      case 1: // 从屏幕开始到光标
      case 2: // 整个屏幕
        this.initializeLines();
        this.state.cursorX = 0;
        this.state.cursorY = 0;
        break;
    }
  }

  /**
   * 获取当前终端状态
   */
  public getState(): TerminalState {
    return { ...this.state };
  }

  /**
   * 设置终端尺寸
   */
  public resize(width: number, height: number) {
    this.state.screenWidth = width;
    this.state.screenHeight = height;

    // 调整行数
    if (this.state.lines.length > height) {
      this.state.lines = this.state.lines.slice(-height);
    } else {
      while (this.state.lines.length < height) {
        this.state.lines.push([]);
      }
    }

    // 调整光标位置
    this.state.cursorY = Math.min(this.state.cursorY, height - 1);
    this.state.cursorX = Math.min(this.state.cursorX, width - 1);
  }
}

/**
 * 简单的ANSI解析函数，用于快速解析单行文本
 */
export function parseAnsiLine(text: string): ParsedSegment[] {
  const parser = new AnsiParser();
  return parser.parseLine(text);
}

/**
 * 移除ANSI转义序列，返回纯文本
 */
export function stripAnsi(text: string): string {
  // eslint-disable-next-line no-control-regex
  return text.replace(/\x1b\[[0-9;]*[mGKHfABCD]/g, '');
}
