import { Platform, ShortcutConfig } from '@/types';

/**
 * 检测当前运行平台
 */
export const detectPlatform = (): Platform => {
  const userAgent = navigator.userAgent.toLowerCase();

  if (userAgent.includes('mac')) {
    return Platform.MACOS;
  } else if (userAgent.includes('win')) {
    return Platform.WINDOWS;
  } else {
    return Platform.LINUX;
  }
};

/**
 * 检查快捷键是否匹配当前按键事件
 * @param event 键盘事件
 * @param keys 快捷键组合
 * @returns 是否匹配
 */
export const isShortcutMatch = (
  event: KeyboardEvent,
  keys: string[]
): boolean => {
  const platform = detectPlatform();
  const normalizedKeys = normalizeShortcutKeys(keys, platform);

  // 检查修饰键
  const hasCtrl = normalizedKeys.includes('ctrl')
    ? event.ctrlKey
    : !event.ctrlKey;
  const hasCmd = normalizedKeys.includes('cmd')
    ? event.metaKey
    : !event.metaKey;
  const hasShift = normalizedKeys.includes('shift')
    ? event.shiftKey
    : !event.shiftKey;
  const hasAlt = normalizedKeys.includes('alt') ? event.altKey : !event.altKey;

  // 获取主按键
  const mainKey = normalizedKeys.find(
    key => !['ctrl', 'cmd', 'shift', 'alt'].includes(key)
  );

  const keyMatch = mainKey
    ? event.key.toLowerCase() === mainKey.toLowerCase() ||
      event.code.toLowerCase() === mainKey.toLowerCase()
    : true;

  return hasCtrl && hasCmd && hasShift && hasAlt && keyMatch;
};

/**
 * 根据平台标准化快捷键
 * @param keys 原始按键组合
 * @param platform 目标平台
 * @returns 标准化后的按键组合
 */
export const normalizeShortcutKeys = (
  keys: string[],
  platform: Platform
): string[] => {
  const normalized = [...keys];

  // 在非macOS平台将cmd转换为ctrl
  if (platform !== Platform.MACOS) {
    const cmdIndex = normalized.indexOf('cmd');
    if (cmdIndex !== -1) {
      normalized[cmdIndex] = 'ctrl';
    }
  }

  // 在macOS平台将ctrl转换为cmd（如果没有显式指定ctrl）
  if (platform === Platform.MACOS) {
    const ctrlIndex = normalized.indexOf('ctrl');
    const hasCmdKey = normalized.includes('cmd');
    if (ctrlIndex !== -1 && !hasCmdKey) {
      // 只有在这是主修饰键时才转换
      const isMainModifier = !keys.find(key =>
        ['cmd', 'option', 'meta'].includes(key)
      );
      if (isMainModifier) {
        normalized[ctrlIndex] = 'cmd';
      }
    }
  }

  return normalized;
};

/**
 * 将按键组合转换为显示字符串
 * @param keys 按键组合
 * @param platform 平台
 * @returns 显示字符串
 */
export const formatShortcutDisplay = (
  keys: string[],
  platform?: Platform
): string => {
  const currentPlatform = platform || detectPlatform();
  const normalized = normalizeShortcutKeys(keys, currentPlatform);

  const symbolMap: Record<string, string> = {
    cmd: currentPlatform === Platform.MACOS ? '⌘' : 'Ctrl',
    ctrl: currentPlatform === Platform.MACOS ? '⌃' : 'Ctrl',
    shift: currentPlatform === Platform.MACOS ? '⇧' : 'Shift',
    alt: currentPlatform === Platform.MACOS ? '⌥' : 'Alt',
    option: '⌥',
    meta: '⌘',
    tab: 'Tab',
    enter: 'Enter',
    escape: 'Esc',
    space: 'Space',
    backspace: 'Backspace',
    delete: 'Delete',
  };

  return normalized
    .map(key => symbolMap[key.toLowerCase()] || key.toUpperCase())
    .join(currentPlatform === Platform.MACOS ? '' : '+');
};

/**
 * 检查快捷键冲突
 * @param keys 要检查的按键组合
 * @param existingShortcuts 现有快捷键配置
 * @param excludeAction 排除的动作（用于编辑时排除自己）
 * @returns 冲突的动作列表
 */
export const checkShortcutConflict = (
  keys: string[],
  existingShortcuts: Record<string, ShortcutConfig>,
  excludeAction?: string
): string[] => {
  const platform = detectPlatform();
  const normalizedKeys = normalizeShortcutKeys(keys, platform);
  const conflicts: string[] = [];

  Object.entries(existingShortcuts).forEach(([action, config]) => {
    if (excludeAction && action === excludeAction) return;
    if (!config.enabled) return;

    const configKeys = normalizeShortcutKeys(config.keys, platform);

    // 检查按键组合是否完全匹配
    if (arraysEqual(normalizedKeys.sort(), configKeys.sort())) {
      conflicts.push(action);
    }
  });

  return conflicts;
};

/**
 * 数组相等比较
 */
const arraysEqual = (a: string[], b: string[]): boolean => {
  if (a.length !== b.length) return false;
  return a.every(item => b.includes(item));
};

/**
 * 验证快捷键组合的有效性
 * @param keys 按键组合
 * @returns 验证结果
 */
export const validateShortcutKeys = (
  keys: string[]
): {
  valid: boolean;
  error?: string;
} => {
  if (!keys || keys.length === 0) {
    return { valid: false, error: '快捷键不能为空' };
  }

  const modifierKeys = ['ctrl', 'cmd', 'shift', 'alt', 'option', 'meta'];
  const hasModifier = keys.some(key =>
    modifierKeys.includes(key.toLowerCase())
  );
  const mainKey = keys.find(key => !modifierKeys.includes(key.toLowerCase()));

  if (!hasModifier && mainKey && mainKey.length === 1) {
    return { valid: false, error: '单个字符键必须配合修饰键使用' };
  }

  // 检查特殊键
  const specialKeys = [
    'tab',
    'enter',
    'escape',
    'space',
    'backspace',
    'delete',
    'f1',
    'f2',
    'f3',
    'f4',
    'f5',
    'f6',
    'f7',
    'f8',
    'f9',
    'f10',
    'f11',
    'f12',
  ];
  if (
    mainKey &&
    !specialKeys.includes(mainKey.toLowerCase()) &&
    mainKey.length > 1
  ) {
    return { valid: false, error: '无效的按键名称' };
  }

  return { valid: true };
};

/**
 * 解析快捷键字符串
 * @param shortcutString 快捷键字符串，如 "cmd+t" 或 "ctrl+shift+c"
 * @returns 按键组合数组
 */
export const parseShortcutString = (shortcutString: string): string[] => {
  return shortcutString
    .toLowerCase()
    .split('+')
    .map(key => key.trim())
    .filter(key => key.length > 0);
};

/**
 * 将按键组合转换为字符串
 * @param keys 按键组合
 * @returns 快捷键字符串
 */
export const stringifyShortcutKeys = (keys: string[]): string => {
  return keys.join('+');
};

/**
 * 获取系统保留的快捷键（避免冲突）
 * @param platform 平台
 * @returns 系统保留快捷键列表
 */
export const getSystemReservedShortcuts = (platform: Platform): string[][] => {
  const common = [
    ['ctrl', 'c'], // 复制
    ['ctrl', 'v'], // 粘贴
    ['ctrl', 'x'], // 剪切
    ['ctrl', 'z'], // 撤销
    ['ctrl', 'y'], // 重做
    ['ctrl', 'a'], // 全选
    ['ctrl', 's'], // 保存
    ['alt', 'f4'], // 关闭窗口 (Windows)
  ];

  if (platform === Platform.MACOS) {
    return [
      ...common.map(keys => keys.map(k => (k === 'ctrl' ? 'cmd' : k))),
      ['cmd', 'q'], // 退出应用
      ['cmd', 'm'], // 最小化
      ['cmd', 'h'], // 隐藏应用
      ['cmd', 'tab'], // 应用切换
      ['cmd', 'space'], // Spotlight
    ];
  }

  return [
    ...common,
    ['ctrl', 'shift', 'esc'], // 任务管理器
    ['alt', 'tab'], // 窗口切换
    ['ctrl', 'alt', 'delete'], // 安全选项
  ];
};
