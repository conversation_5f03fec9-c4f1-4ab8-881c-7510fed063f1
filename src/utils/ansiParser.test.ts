import { AnsiParser } from './ansiParser';

describe('AnsiParser', () => {
  let parser: AnsiParser;

  beforeEach(() => {
    parser = new AnsiParser();
  });

  describe('处理复杂的终端输出', () => {
    test('应该正确处理包含回车和光标控制的输出', () => {
      // 这是从您的日志中提取的实际输出，但我们只测试第一行
      const input = '\u001b[0m\u001b[27m\u001b[24m\u001b[J(base) \u001b[1m\u001b[34m#\u001b[00m \u001b[36mmaojie \u001b[00m@ \u001b[32mMacBook-Pro-3 \u001b[00min \u001b[1m\u001b[33m~\u001b[00m [13:35:06]';

      // 解析输入
      const segments = parser.parseLine(input);

      // 验证解析结果
      expect(segments).toBeDefined();
      expect(segments.length).toBeGreaterThan(0);

      // 检查是否包含预期的文本内容
      const fullText = segments.map(s => s.text).join('');
      expect(fullText).toContain('maojie');
      expect(fullText).toContain('MacBook-Pro-3');
    });

    test('应该正确处理颜色转义序列', () => {
      const input = '\u001b[1m\u001b[34m#\u001b[00m \u001b[36mmaojie\u001b[00m';

      const segments = parser.parseLine(input);

      // 应该有多个段落，每个都有不同的样式
      expect(segments.length).toBeGreaterThan(1);

      // 检查是否有颜色样式
      const hasColoredSegment = segments.some(s => s.style.color);
      expect(hasColoredSegment).toBe(true);

      // 检查是否有粗体样式
      const hasBoldSegment = segments.some(s => s.style.bold);
      expect(hasBoldSegment).toBe(true);
    });

    test('应该正确处理重置序列', () => {
      const input = '\u001b[1mBold\u001b[0mNormal';

      const segments = parser.parseLine(input);

      expect(segments.length).toBe(2);
      expect(segments[0].style.bold).toBe(true);
      expect(segments[1].style.bold).toBeUndefined();
    });

    test('应该忽略光标控制序列', () => {
      const input = '\u001b[A\u001b[B\u001b[C\u001b[D\u001b[H\u001b[J\u001b[KHello';

      const segments = parser.parseLine(input);

      // 应该只包含 "Hello" 文本
      expect(segments.length).toBe(1);
      expect(segments[0].text).toBe('Hello');
    });

    test('应该处理私有模式序列', () => {
      const input = '\u001b[?25h\u001b[?1049hHello\u001b[?25l\u001b[?1049l';

      const segments = parser.parseLine(input);

      // 应该只包含 "Hello" 文本
      expect(segments.length).toBe(1);
      expect(segments[0].text).toBe('Hello');
    });

    test('应该处理OSC序列', () => {
      const input = '\u001b]0;Terminal Title\u0007Hello';

      const segments = parser.parseLine(input);

      // 应该只包含 "Hello" 文本
      expect(segments.length).toBe(1);
      expect(segments[0].text).toBe('Hello');
    });

    test('应该处理空输入', () => {
      const segments = parser.parseLine('');

      expect(segments.length).toBe(1);
      expect(segments[0].text).toBe('');
    });

    test('应该处理只包含转义序列的输入', () => {
      const input = '\u001b[A\u001b[B\u001b[H\u001b[J';

      const segments = parser.parseLine(input);

      expect(segments.length).toBe(1);
      expect(segments[0].text).toBe('');
    });
  });

  describe('处理特殊字符', () => {
    test('应该正确处理制表符', () => {
      const input = 'Hello\tWorld';

      const segments = parser.parseLine(input);

      expect(segments[0].text).toBe('Hello    World');
    });

    test('应该正确处理退格符', () => {
      const input = 'Hello\bWorld';

      const segments = parser.parseLine(input);

      expect(segments[0].text).toBe('HellWorld');
    });

    test('应该正确处理退格符后跟命令的情况', () => {
      // 模拟您遇到的实际情况：\bls
      const input = '\bls';

      const segments = parser.parseLine(input);

      // 退格符应该被处理，只显示 "ls"
      expect(segments[0].text).toBe('ls');
    });

    test('应该正确处理多个退格符', () => {
      const input = 'Hello\b\b\bWorld';

      const segments = parser.parseLine(input);

      // "Hello" -> "Hell" -> "Hel" -> "He" -> "HeWorld"
      expect(segments[0].text).toBe('HeWorld');
    });

    test('应该正确处理中文字符', () => {
      const input = '你好\u001b[1m世界\u001b[0m';

      const segments = parser.parseLine(input);

      expect(segments.length).toBe(2);
      expect(segments[0].text).toBe('你好');
      expect(segments[1].text).toBe('世界');
      expect(segments[1].style.bold).toBe(true);
    });
  });
});
