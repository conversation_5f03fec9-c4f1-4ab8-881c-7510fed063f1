/* Font loading - must be at the top */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* Import terminal styles */
@import './terminal.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    /* 更深的背景色，更接近真实终端 */
    --background: 0 0% 7%;
    --foreground: 0 0% 85%;
    --card: 0 0% 9%;
    --card-foreground: 0 0% 85%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 85%;
    /* 绿色主题，符合终端风格 */
    --primary: 120 100% 50%;
    --primary-foreground: 0 0% 7%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 85%;
    --muted: 0 0% 12%;
    --muted-foreground: 0 0% 60%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 85%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 20%;
    --input: 0 0% 15%;
    --ring: 120 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, sans-serif;
  }

  /* 等宽字体应用到终端和代码区域 */
  .font-mono,
  pre,
  code,
  .terminal,
  [data-terminal] {
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace !important;
  }
}

/* Custom scrollbar styles */
@layer utilities {

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Firefox 滚动条 */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
  }

  /* 隐藏滚动条 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
  }
}

/* Selection styles */
::selection {
  @apply bg-primary/20 text-foreground;
}

/* Base layout styles */
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 终端特定样式 */
@layer components {
  .terminal-cursor {
    @apply animate-blink;
  }

  .terminal-text {
    @apply font-mono;
    line-height: 1.2;
    letter-spacing: 0.5px;
  }

  /* 终端风格的组件样式 */
  .terminal-window {
    @apply bg-background border border-border rounded-lg shadow-2xl;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
  }

  .terminal-header {
    @apply bg-muted border-b border-border px-4 py-2 rounded-t-lg;
    background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--muted) / 0.8) 100%);
  }

  .terminal-tab {
    @apply relative bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground;
    @apply transition-all duration-200 border-r border-border;
  }

  .terminal-tab.active {
    @apply bg-background text-foreground;
    @apply border-b-2 border-primary;
  }

  .terminal-button {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium;
    @apply transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring;
    @apply disabled:pointer-events-none disabled:opacity-50;
    @apply hover:bg-accent hover:text-accent-foreground;
    @apply border border-border bg-background;
  }

  /* 自定义组件样式 */
  .card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
  }

  .button-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  .input-field {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* 动画工具类 */
  .animate-in {
    animation-duration: 0.15s;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 0.15s;
    animation-fill-mode: forwards;
  }

  /* Focus 可见性 */
  .focus-visible {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* 响应式文本大小 */
  .text-responsive {
    @apply text-sm sm:text-base;
  }

  /* 玻璃态效果 */
  .glass {
    @apply bg-background/80 backdrop-blur-sm border border-border/50;
  }

  /* 渐变边框 */
  .gradient-border {
    @apply relative;
  }

  .gradient-border::before {
    @apply absolute inset-0 rounded-lg p-[1px] bg-gradient-to-r from-primary to-accent;
    content: '';
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
  }
}

/* 深色模式特定样式 */
.dark {
  color-scheme: dark;
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 20%;
    --input: 0 0% 20%;
  }

  .dark {
    --border: 0 0% 80%;
    --input: 0 0% 80%;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
