/* 主题系统基础样式 */

/* CSS 变量定义 - 这些变量会被 JavaScript 动态设置 */
:root {
  /* 基础颜色 */
  --color-primary: #3B82F6;
  --color-secondary: #6B7280;
  --color-background: #0F172A;
  --color-surface: #1E293B;
  --color-text: #F8FAFC;
  --color-text-secondary: #CBD5E1;
  --color-border: #334155;

  /* 终端颜色 */
  --terminal-background: #0F172A;
  --terminal-foreground: #F8FAFC;
  --terminal-cursor: #3B82F6;
  --terminal-selection: rgba(59, 130, 246, 0.3);

  /* ANSI 颜色 (0-15) */
  --terminal-ansi-0: #1E293B;
  --terminal-ansi-1: #EF4444;
  --terminal-ansi-2: #10B981;
  --terminal-ansi-3: #F59E0B;
  --terminal-ansi-4: #3B82F6;
  --terminal-ansi-5: #8B5CF6;
  --terminal-ansi-6: #06B6D4;
  --terminal-ansi-7: #F8FAFC;
  --terminal-ansi-8: #475569;
  --terminal-ansi-9: #F87171;
  --terminal-ansi-10: #34D399;
  --terminal-ansi-11: #FBBF24;
  --terminal-ansi-12: #60A5FA;
  --terminal-ansi-13: #A78BFA;
  --terminal-ansi-14: #22D3EE;
  --terminal-ansi-15: #FFFFFF;

  /* 状态颜色 */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;

  /* 交互颜色 */
  --color-hover: rgba(255, 255, 255, 0.1);
  --color-active: rgba(255, 255, 255, 0.2);
  --color-focus: #3B82F6;
  --color-disabled: rgba(255, 255, 255, 0.3);

  /* 字体配置 */
  --font-family: "Consolas", "Monaco", "Courier New", monospace;
  --font-size: 14px;
  --line-height: 1.5;
  --font-weight: 400;

  /* 视觉效果 */
  --border-radius: 6px;
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --opacity-disabled: 0.5;
  --opacity-hover: 0.8;
  --transition-duration: 200ms;
  --transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主题切换动画 */
* {
  transition:
    background-color var(--transition-duration) var(--transition-easing),
    color var(--transition-duration) var(--transition-easing),
    border-color var(--transition-duration) var(--transition-easing),
    box-shadow var(--transition-duration) var(--transition-easing);
}

/* 基础组件样式 */
.theme-container {
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--font-family);
  font-size: var(--font-size);
  line-height: var(--line-height);
  font-weight: var(--font-weight);
  min-height: 100vh;
}

/* 表面元素 */
.surface {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* 文本样式 */
.text-primary {
  color: var(--color-text);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-error {
  color: var(--color-error);
}

.text-info {
  color: var(--color-info);
}

/* 按钮样式 */
.btn {
  background-color: var(--color-primary);
  color: var(--color-background);
  border: none;
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  transition: all var(--transition-duration) var(--transition-easing);
}

.btn:hover:not(:disabled) {
  opacity: var(--opacity-hover);
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-surface);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--color-hover);
}

/* 主题切换按钮 */
.theme-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  padding: 0.5rem;
  cursor: pointer;
  transition: all var(--transition-duration) var(--transition-easing);
}

.theme-toggle-btn:hover {
  background-color: var(--color-hover);
  color: var(--color-primary);
}

.theme-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 输入框样式 */
.input {
  background-color: var(--color-surface);
  color: var(--color-text);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: 0.5rem 0.75rem;
  font-family: inherit;
  font-size: inherit;
  transition: all var(--transition-duration) var(--transition-easing);
}

.input:focus {
  outline: none;
  border-color: var(--color-focus);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.input::placeholder {
  color: var(--color-text-secondary);
  opacity: var(--opacity-disabled);
}

/* 终端特定样式 */
.terminal {
  background-color: var(--terminal-background);
  color: var(--terminal-foreground);
  font-family: var(--font-family);
  font-size: var(--font-size);
  line-height: var(--line-height);
}

.terminal-cursor {
  background-color: var(--terminal-cursor);
  animation: terminal-cursor-blink 1s infinite;
}

@keyframes terminal-cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.terminal-selection {
  background-color: var(--terminal-selection);
}

/* ANSI 颜色类 */
.ansi-0 { color: var(--terminal-ansi-0); }
.ansi-1 { color: var(--terminal-ansi-1); }
.ansi-2 { color: var(--terminal-ansi-2); }
.ansi-3 { color: var(--terminal-ansi-3); }
.ansi-4 { color: var(--terminal-ansi-4); }
.ansi-5 { color: var(--terminal-ansi-5); }
.ansi-6 { color: var(--terminal-ansi-6); }
.ansi-7 { color: var(--terminal-ansi-7); }
.ansi-8 { color: var(--terminal-ansi-8); }
.ansi-9 { color: var(--terminal-ansi-9); }
.ansi-10 { color: var(--terminal-ansi-10); }
.ansi-11 { color: var(--terminal-ansi-11); }
.ansi-12 { color: var(--terminal-ansi-12); }
.ansi-13 { color: var(--terminal-ansi-13); }
.ansi-14 { color: var(--terminal-ansi-14); }
.ansi-15 { color: var(--terminal-ansi-15); }

/* 背景颜色类 */
.ansi-bg-0 { background-color: var(--terminal-ansi-0); }
.ansi-bg-1 { background-color: var(--terminal-ansi-1); }
.ansi-bg-2 { background-color: var(--terminal-ansi-2); }
.ansi-bg-3 { background-color: var(--terminal-ansi-3); }
.ansi-bg-4 { background-color: var(--terminal-ansi-4); }
.ansi-bg-5 { background-color: var(--terminal-ansi-5); }
.ansi-bg-6 { background-color: var(--terminal-ansi-6); }
.ansi-bg-7 { background-color: var(--terminal-ansi-7); }
.ansi-bg-8 { background-color: var(--terminal-ansi-8); }
.ansi-bg-9 { background-color: var(--terminal-ansi-9); }
.ansi-bg-10 { background-color: var(--terminal-ansi-10); }
.ansi-bg-11 { background-color: var(--terminal-ansi-11); }
.ansi-bg-12 { background-color: var(--terminal-ansi-12); }
.ansi-bg-13 { background-color: var(--terminal-ansi-13); }
.ansi-bg-14 { background-color: var(--terminal-ansi-14); }
.ansi-bg-15 { background-color: var(--terminal-ansi-15); }

/* 主题选择器样式 */
.theme-selector {
  padding: 1rem;
}

.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  margin: 1rem 0;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--color-surface);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-duration) var(--transition-easing);
}

.theme-option:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
}

.theme-option.active {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.theme-preview {
  width: 80px;
  height: 50px;
  border-radius: calc(var(--border-radius) * 0.5);
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.preview-terminal {
  width: 100%;
  height: 100%;
  padding: 0.25rem;
  font-family: var(--font-family);
  font-size: 10px;
  line-height: 1.2;
  display: flex;
  align-items: center;
}

.auto-switch-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  cursor: pointer;
  color: var(--color-text-secondary);
}

.auto-switch-toggle input[type="checkbox"],
.theme-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-surface);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-duration) var(--transition-easing);
}

.auto-switch-toggle input[type="checkbox"]:checked,
.theme-checkbox:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.auto-switch-toggle input[type="checkbox"]:checked::after,
.theme-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 头部样式 */
.header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

/* 边框工具类 */
.border-border {
  border-color: var(--color-border);
}

.border-b {
  border-bottom-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

/* 背景工具类 */
.bg-surface {
  background-color: var(--color-surface);
}

.bg-info {
  background-color: var(--color-info);
}

/* 文本大小工具类 */
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

/* 字体粗细工具类 */
.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* 间距工具类 */
.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

/* 尺寸工具类 */
.w-2 {
  width: 0.5rem;
}

.h-2 {
  height: 0.5rem;
}

.w-5 {
  width: 1.25rem;
}

.h-5 {
  height: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.h-12 {
  height: 3rem;
}

/* 圆角工具类 */
.rounded-md {
  border-radius: var(--border-radius);
}

.rounded-full {
  border-radius: 9999px;
}

/* 动画工具类 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .theme-preview {
    width: 60px;
    height: 40px;
  }

  .preview-terminal {
    font-size: 8px;
  }
}

/* 高对比度模式特殊样式 */
[data-theme="dark"].theme-high-contrast {
  --color-focus: #FFFF00;
}

[data-theme="dark"].theme-high-contrast .btn:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .terminal-cursor {
    animation: none !important;
  }
}

/* 打印样式 */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}
