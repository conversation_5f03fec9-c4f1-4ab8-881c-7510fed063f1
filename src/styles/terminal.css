/* Terminal Display Styles */
.terminal-display {
  @apply w-full h-full overflow-auto relative;
  @apply focus:outline-none;
  @apply select-none;
  font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', 'Microsoft YaHei', 'SimHei', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Code Pro', monospace;
  line-height: 1.2;
}

.terminal-display:focus {
  outline: none;
}

/* Terminal Buffer Styles */
.terminal-buffer {
  @apply w-full h-full;
  @apply font-mono text-sm leading-tight;
  font-family: inherit;
}

/* Terminal Line Styles */
.terminal-line {
  @apply whitespace-pre-wrap break-words;
  @apply min-h-[1.2em] px-1;
  @apply hover:bg-opacity-5 hover:bg-white;
  font-family: inherit;
  line-height: inherit;
}

.terminal-line.selected {
  @apply bg-blue-500 bg-opacity-20;
}

.terminal-line:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

/* Terminal Cursor Styles */
.terminal-cursor {
  @apply absolute;
  @apply pointer-events-none;
  border-radius: 1px;
  background-color: hsl(120 100% 50%);
  animation: blink 1s infinite;
}

.terminal-cursor.block {
  @apply w-2 h-5;
}

.terminal-cursor.underline {
  @apply w-2 h-0.5;
  bottom: 0;
}

.terminal-cursor.bar {
  @apply w-0.5 h-5;
}

.terminal-cursor.hidden {
  @apply opacity-0;
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

/* Terminal Selection Styles */
.terminal-selection {
  @apply absolute top-0 left-0;
  @apply pointer-events-none;
}

.terminal-selection-line {
  @apply absolute;
  @apply pointer-events-none;
}

/* Scrollbar Styles */
.terminal-display::-webkit-scrollbar {
  width: 8px;
}

.terminal-display::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.terminal-display::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.terminal-display::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Text Selection */
.terminal-display ::selection {
  background-color: hsl(120 100% 50% / 0.3);
  color: hsl(0 0% 100%);
}

.terminal-display ::-moz-selection {
  background-color: hsl(120 100% 50% / 0.3);
  color: hsl(0 0% 100%);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Dark theme specific styles */
.dark .terminal-display {
  background-color: hsl(0 0% 7%);
  color: hsl(0 0% 85%);
}

.dark .terminal-line:hover {
  background-color: hsl(0 0% 15% / 0.5);
}

.dark .terminal-display::-webkit-scrollbar-track {
  background: hsl(0 0% 12%);
}

.dark .terminal-display::-webkit-scrollbar-thumb {
  background: hsl(0 0% 30%);
}

.dark .terminal-display::-webkit-scrollbar-thumb:hover {
  background: hsl(120 100% 50% / 0.3);
}

/* Light theme specific styles */
.light .terminal-display {
  background-color: #ffffff;
  color: #333333;
}

.light .terminal-line:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.light .terminal-display::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.light .terminal-display::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
}

.light .terminal-display::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}
