interface SettingsNavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  searchQuery: string;
}

const navigationItems = [
  {
    id: 'general',
    icon: '⚙️',
    label: '通用设置',
    description: '语言、启动等基础配置',
    keywords: ['语言', '启动', '通用', '基础'],
  },
  {
    id: 'terminal',
    icon: '🖥️',
    label: '终端设置',
    description: '字体、外观、行为配置',
    keywords: ['字体', '终端', '外观', '行为', '光标'],
  },
  {
    id: 'appearance',
    icon: '🎨',
    label: '外观设置',
    description: '主题、颜色、透明度',
    keywords: ['主题', '颜色', '外观', '透明度', '暗色', '亮色'],
  },
  {
    id: 'ai',
    icon: '🤖',
    label: 'AI 设置',
    description: 'AI 模型、自动建议配置',
    keywords: ['AI', '模型', '建议', '智能', '命令'],
  },
  {
    id: 'shortcuts',
    icon: '⌨️',
    label: '快捷键',
    description: '键盘快捷键配置',
    keywords: ['快捷键', '键盘', '热键', '按键'],
  },
  {
    id: 'security',
    icon: '🔒',
    label: '安全设置',
    description: '权限管理、危险命令',
    keywords: ['安全', '权限', '危险', '命令', '保护'],
  },
];

export function SettingsNavigation({
  activeSection,
  onSectionChange,
  searchQuery,
}: SettingsNavigationProps) {
  // 根据搜索关键词过滤导航项
  const filteredItems = navigationItems.filter(item => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      item.label.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query) ||
      item.keywords.some(keyword => keyword.toLowerCase().includes(query))
    );
  });

  return (
    <nav className="px-2 pb-4">
      {filteredItems.map(item => (
        <button
          key={item.id}
          onClick={() => onSectionChange(item.id)}
          className={`w-full p-3 mb-1 rounded-lg text-left transition-colors duration-200
                     hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500
                     ${
                       activeSection === item.id
                         ? 'bg-blue-600 text-white'
                         : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                     }`}
        >
          <div className="flex items-start space-x-3">
            <span className="text-lg mt-0.5">{item.icon}</span>
            <div className="flex-1 min-w-0">
              <div className="font-medium truncate">{item.label}</div>
              <div
                className={`text-xs mt-1 truncate
                           ${
                             activeSection === item.id
                               ? 'text-blue-100'
                               : 'text-gray-500 dark:text-gray-400'
                           }`}
              >
                {item.description}
              </div>
            </div>
          </div>
        </button>
      ))}

      {filteredItems.length === 0 && searchQuery && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <div className="text-lg mb-2">🔍</div>
          <div className="text-sm">未找到匹配的设置项</div>
        </div>
      )}
    </nav>
  );
}
