import { useSettingsStore } from '@/stores/settingsStore';
import { useState } from 'react';
import { SettingsNavigation } from './SettingsNavigation';
import { AISettings } from './sections/AISettings';
import { AppearanceSettings } from './sections/AppearanceSettings';
import { GeneralSettings } from './sections/GeneralSettings';
import { SecuritySettings } from './sections/SecuritySettings';
import { ShortcutSettings } from './sections/ShortcutSettings';
import { TerminalSettings } from './sections/TerminalSettings';

type SettingsSection =
  | 'general'
  | 'terminal'
  | 'appearance'
  | 'ai'
  | 'shortcuts'
  | 'security';

export function SettingsPanel() {
  const [activeSection, setActiveSection] =
    useState<SettingsSection>('general');
  const [searchQuery, setSearchQuery] = useState('');

  const { exportSettings, importSettings, resetSettings } = useSettingsStore();

  const handleExport = () => {
    const settingsJson = exportSettings();
    const blob = new Blob([settingsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'tagent-settings.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = e => {
          const settingsJson = e.target?.result as string;
          const success = importSettings(settingsJson);
          if (success) {
            alert('设置导入成功！');
          } else {
            alert('设置导入失败，请检查文件格式。');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const handleReset = () => {
    if (window.confirm('确定要重置所有设置到默认值吗？此操作无法撤销。')) {
      resetSettings();
      alert('设置已重置到默认值。');
    }
  };

  const renderSettingsSection = () => {
    switch (activeSection) {
      case 'general':
        return <GeneralSettings />;
      case 'terminal':
        return <TerminalSettings />;
      case 'appearance':
        return <AppearanceSettings />;
      case 'ai':
        return <AISettings />;
      case 'shortcuts':
        return <ShortcutSettings />;
      case 'security':
        return <SecuritySettings />;
      default:
        return <GeneralSettings />;
    }
  };

  return (
    <div className="flex h-full bg-background">
      {/* 左侧导航 */}
      <div className="w-64 border-r border-border bg-card">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4">设置</h2>

          {/* 搜索框 */}
          <input
            type="text"
            placeholder="搜索设置..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="w-full px-3 py-2 text-sm border border-border rounded-md
                       bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          />
        </div>

        <SettingsNavigation
          activeSection={activeSection}
          onSectionChange={section =>
            setActiveSection(section as SettingsSection)
          }
          searchQuery={searchQuery}
        />

        {/* 底部操作按钮 */}
        <div className="p-4 border-t border-border space-y-2">
          <button
            onClick={handleExport}
            className="w-full px-3 py-2 text-sm border border-border rounded-md
                       hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            导出设置
          </button>

          <button
            onClick={handleImport}
            className="w-full px-3 py-2 text-sm border border-border rounded-md
                       hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            导入设置
          </button>

          <button
            onClick={handleReset}
            className="w-full px-3 py-2 text-sm border border-destructive text-destructive-foreground
                       bg-destructive rounded-md hover:bg-destructive/90 transition-colors"
          >
            重置设置
          </button>
        </div>
      </div>

      {/* 右侧设置内容 */}
      <div className="flex-1 overflow-auto">{renderSettingsSection()}</div>
    </div>
  );
}
