interface InputFieldProps {
  label: string;
  description?: string;
  value: string | number;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'number' | 'password' | 'email';
  disabled?: boolean;
  min?: number;
  max?: number;
  step?: number;
}

export function InputField({
  label,
  description,
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false,
  min,
  max,
  step,
}: InputFieldProps) {
  return (
    <div className="space-y-2">
      <div>
        <label className="text-sm font-medium text-foreground">{label}</label>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </div>

      <input
        type={type}
        value={value}
        onChange={e => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        min={min}
        max={max}
        step={step}
        className={`w-full px-3 py-2 text-sm border border-border rounded-md
                   bg-background focus:outline-none focus:ring-2 focus:ring-blue-500
                   ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      />
    </div>
  );
}
