interface SliderFieldProps {
  label: string;
  description?: string;
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step?: number;
  disabled?: boolean;
  unit?: string;
}

export function SliderField({
  label,
  description,
  value,
  onChange,
  min,
  max,
  step = 1,
  disabled = false,
  unit = '',
}: SliderFieldProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div>
          <label className="text-sm font-medium text-foreground">{label}</label>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>

        <div className="text-sm font-mono text-muted-foreground">
          {value}
          {unit}
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <span className="text-xs text-muted-foreground">{min}</span>
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={e => onChange(Number(e.target.value))}
          disabled={disabled}
          className={`flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none
                     cursor-pointer slider
                     ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        />
        <span className="text-xs text-muted-foreground">{max}</span>
      </div>
    </div>
  );
}
