import { SettingsSection } from '../SettingsSection';
import { InputField } from '../fields/InputField';
import { SelectField } from '../fields/SelectField';
import { ToggleField } from '../fields/ToggleField';

export function GeneralSettings() {
  // 由于现有的设置结构中没有通用设置分类，我们暂时只显示一些基础信息
  // 实际项目中可以扩展设置结构来支持通用设置

  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">通用设置</h2>
        <p className="text-muted-foreground">配置应用的基础功能和行为设置</p>
      </div>

      <SettingsSection title="应用信息" description="查看应用版本和系统信息">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">应用版本</span>
            <span className="text-sm text-muted-foreground">1.0.0</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">构建版本</span>
            <span className="text-sm text-muted-foreground">dev-build</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">系统平台</span>
            <span className="text-sm text-muted-foreground">
              {navigator.platform}
            </span>
          </div>
        </div>
      </SettingsSection>

      <SettingsSection title="语言设置" description="界面语言和本地化选项">
        <SelectField
          label="界面语言"
          description="选择应用界面显示语言"
          value="zh-CN"
          onChange={value => {
            // TODO: 实现语言切换功能
            console.log('Language changed to:', value);
          }}
          options={[
            { value: 'zh-CN', label: '简体中文' },
            { value: 'en-US', label: 'English' },
            { value: 'ja-JP', label: '日本語' },
          ]}
        />
      </SettingsSection>

      <SettingsSection title="启动设置" description="应用启动和行为配置">
        <ToggleField
          label="开机自启动"
          description="系统启动时自动启动 TAgent"
          value={false}
          onChange={value => {
            // TODO: 实现自启动功能
            console.log('Auto start changed to:', value);
          }}
        />

        <ToggleField
          label="最小化到系统托盘"
          description="关闭窗口时最小化到系统托盘而不是退出"
          value={true}
          onChange={value => {
            // TODO: 实现托盘功能
            console.log('Minimize to tray changed to:', value);
          }}
        />

        <InputField
          label="默认工作目录"
          description="新终端标签页的默认工作目录"
          value={process.env.HOME || '/Users'}
          onChange={value => {
            // TODO: 保存默认工作目录设置
            console.log('Default directory changed to:', value);
          }}
          placeholder="输入目录路径"
        />
      </SettingsSection>

      <SettingsSection title="更新设置" description="应用更新和版本管理">
        <ToggleField
          label="自动检查更新"
          description="定期检查并通知可用的应用更新"
          value={true}
          onChange={value => {
            // TODO: 实现自动更新检查
            console.log('Auto update check changed to:', value);
          }}
        />

        <ToggleField
          label="参与 Beta 测试"
          description="接收 Beta 版本更新和实验性功能"
          value={false}
          onChange={value => {
            // TODO: 实现 Beta 更新设置
            console.log('Beta updates changed to:', value);
          }}
        />
      </SettingsSection>

      <SettingsSection title="隐私设置" description="数据收集和隐私保护选项">
        <ToggleField
          label="发送使用统计"
          description="发送匿名使用统计数据以帮助改进应用"
          value={false}
          onChange={value => {
            // TODO: 实现统计数据设置
            console.log('Usage statistics changed to:', value);
          }}
        />

        <ToggleField
          label="错误报告"
          description="自动发送崩溃报告和错误信息"
          value={true}
          onChange={value => {
            // TODO: 实现错误报告设置
            console.log('Error reporting changed to:', value);
          }}
        />
      </SettingsSection>
    </div>
  );
}
