import {
  updateTerminalSettings,
  useSettingsStore,
} from '@/stores/settingsStore';
import { SettingsSection } from '../SettingsSection';
import { SelectField } from '../fields/SelectField';
import { SliderField } from '../fields/SliderField';
import { ToggleField } from '../fields/ToggleField';

export function TerminalSettings() {
  const { terminal } = useSettingsStore();

  const fontFamilyOptions = [
    {
      value: 'Monaco, "Cascadia Code", "Fira Code", monospace',
      label: 'Monaco (默认)',
    },
    { value: '"Cascadia Code", monospace', label: 'Cascadia Code' },
    { value: '"Fira Code", monospace', label: 'Fira Code' },
    { value: '"JetBrains Mono", monospace', label: 'JetBrains Mono' },
    { value: '"SF Mono", Monaco, monospace', label: 'SF Mono' },
    { value: '"Menlo", Monaco, monospace', label: '<PERSON><PERSON>' },
    { value: '"Consolas", monospace', label: 'Consolas' },
    { value: '"DejaVu Sans Mono", monospace', label: 'DejaVu Sans Mono' },
  ];

  const cursorStyleOptions = [
    { value: 'block', label: '块状光标' },
    { value: 'underline', label: '下划线光标' },
    { value: 'bar', label: '竖线光标' },
  ];

  const shellOptions = [
    { value: 'zsh', label: 'Zsh' },
    { value: 'bash', label: 'Bash' },
    { value: 'fish', label: 'Fish' },
    { value: 'powershell', label: 'PowerShell' },
    { value: 'cmd', label: 'Command Prompt' },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">终端设置</h2>
        <p className="text-muted-foreground">配置终端的外观、字体和行为设置</p>
      </div>

      <SettingsSection
        title="Shell 设置"
        description="选择默认的终端 Shell 环境"
      >
        <SelectField
          label="默认 Shell"
          description="新终端标签页使用的默认 Shell"
          value={terminal.defaultShell}
          onChange={value => updateTerminalSettings({ defaultShell: value })}
          options={shellOptions}
        />
      </SettingsSection>

      <SettingsSection title="字体设置" description="配置终端文本的字体和大小">
        <SelectField
          label="字体家族"
          description="选择终端使用的等宽字体"
          value={terminal.fontFamily}
          onChange={value => updateTerminalSettings({ fontFamily: value })}
          options={fontFamilyOptions}
        />

        <SliderField
          label="字体大小"
          description="调整终端文本的大小"
          value={terminal.fontSize}
          onChange={value => updateTerminalSettings({ fontSize: value })}
          min={8}
          max={32}
          step={1}
          unit="px"
        />

        <div className="p-4 border border-border rounded-lg bg-muted/50">
          <div className="text-xs text-muted-foreground mb-2">字体预览</div>
          <div
            className="font-mono text-sm p-2 bg-background border rounded"
            style={{
              fontFamily: terminal.fontFamily,
              fontSize: `${terminal.fontSize}px`,
            }}
          >
            $ echo "Hello, TAgent Terminal!"
            <br />
            Hello, TAgent Terminal!
            <br />
            $ ls -la | grep config
            <br />
            drwxr-xr-x config/
          </div>
        </div>
      </SettingsSection>

      <SettingsSection title="光标设置" description="配置终端光标的样式">
        <SelectField
          label="光标样式"
          description="选择终端光标的显示样式"
          value={terminal.cursorStyle}
          onChange={value =>
            updateTerminalSettings({
              cursorStyle: value as 'block' | 'underline' | 'bar',
            })
          }
          options={cursorStyleOptions}
        />
      </SettingsSection>

      <SettingsSection title="行为设置" description="配置终端的行为和性能选项">
        <SliderField
          label="滚动缓冲区大小"
          description="终端可以保留的历史行数"
          value={terminal.scrollback}
          onChange={value => updateTerminalSettings({ scrollback: value })}
          min={100}
          max={10000}
          step={100}
          unit=" 行"
        />

        <ToggleField
          label="启用终端响铃"
          description="在需要用户注意时播放系统提示音"
          value={terminal.enableBell}
          onChange={value => updateTerminalSettings({ enableBell: value })}
        />
      </SettingsSection>

      <SettingsSection title="高级设置" description="高级终端配置选项">
        <ToggleField
          label="文本选择自动复制"
          description="选择文本时自动复制到剪贴板"
          value={false} // TODO: 添加到设置结构中
          onChange={value => {
            // TODO: 实现自动复制设置
            console.log('Auto copy changed to:', value);
          }}
        />

        <ToggleField
          label="粘贴时确认"
          description="粘贴多行内容时显示确认对话框"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            // TODO: 实现粘贴确认设置
            console.log('Paste confirmation changed to:', value);
          }}
        />

        <ToggleField
          label="自动换行"
          description="当行内容超出终端宽度时自动换行"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            // TODO: 实现自动换行设置
            console.log('Word wrap changed to:', value);
          }}
        />
      </SettingsSection>

      <SettingsSection title="性能优化" description="终端性能相关设置">
        <SliderField
          label="渲染更新频率"
          description="终端内容更新的频率 (fps)"
          value={60} // TODO: 添加到设置结构中
          onChange={value => {
            // TODO: 实现渲染频率设置
            console.log('Render fps changed to:', value);
          }}
          min={30}
          max={120}
          step={10}
          unit=" fps"
        />

        <ToggleField
          label="硬件加速"
          description="使用 GPU 加速终端渲染（重启后生效）"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            // TODO: 实现硬件加速设置
            console.log('Hardware acceleration changed to:', value);
          }}
        />
      </SettingsSection>
    </div>
  );
}
