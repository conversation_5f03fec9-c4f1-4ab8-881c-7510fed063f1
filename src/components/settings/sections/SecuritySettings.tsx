import {
  updateSecuritySettings,
  useSettingsStore,
} from '@/stores/settingsStore';
import { useState } from 'react';
import { SettingsSection } from '../SettingsSection';
import { InputField } from '../fields/InputField';
import { ToggleField } from '../fields/ToggleField';

export function SecuritySettings() {
  const { security } = useSettingsStore();
  const [newDangerousCommand, setNewDangerousCommand] = useState('');

  const addDangerousCommand = () => {
    if (
      newDangerousCommand.trim() &&
      !security.dangerousCommands.includes(newDangerousCommand.trim())
    ) {
      updateSecuritySettings({
        dangerousCommands: [
          ...security.dangerousCommands,
          newDangerousCommand.trim(),
        ],
      });
      setNewDangerousCommand('');
    }
  };

  const removeDangerousCommand = (command: string) => {
    updateSecuritySettings({
      dangerousCommands: security.dangerousCommands.filter(
        cmd => cmd !== command
      ),
    });
  };

  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">安全设置</h2>
        <p className="text-muted-foreground">
          配置安全机制、权限管理和危险命令检测
        </p>
      </div>

      <SettingsSection title="安全模式" description="基础安全保护机制">
        <ToggleField
          label="启用安全模式"
          description="启用所有安全检查和保护机制"
          value={security.enableSafeMode}
          onChange={value => updateSecuritySettings({ enableSafeMode: value })}
        />

        <ToggleField
          label="需要确认危险操作"
          description="执行危险命令前需要用户确认"
          value={security.requireConfirmation}
          onChange={value =>
            updateSecuritySettings({ requireConfirmation: value })
          }
        />

        <ToggleField
          label="启用审计日志"
          description="记录所有命令执行历史到日志文件"
          value={security.auditLog}
          onChange={value => updateSecuritySettings({ auditLog: value })}
        />
      </SettingsSection>

      <SettingsSection
        title="危险命令管理"
        description="管理需要特别注意的危险命令列表"
      >
        <div className="space-y-4">
          <div className="flex space-x-2">
            <div className="flex-1">
              <InputField
                label=""
                value={newDangerousCommand}
                onChange={setNewDangerousCommand}
                placeholder="输入危险命令模式，如 'rm -rf'"
              />
            </div>
            <button
              onClick={addDangerousCommand}
              disabled={!newDangerousCommand.trim()}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md
                         hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                         h-fit mt-auto"
            >
              添加
            </button>
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">当前危险命令列表:</div>
            <div className="max-h-60 overflow-y-auto space-y-1">
              {security.dangerousCommands.map((command, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-muted rounded border"
                >
                  <code className="text-sm font-mono">{command}</code>
                  <button
                    onClick={() => removeDangerousCommand(command)}
                    className="text-xs text-red-600 hover:text-red-800
                               px-2 py-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    删除
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </SettingsSection>

      <SettingsSection
        title="文件系统保护"
        description="限制文件和目录访问权限"
      >
        <ToggleField
          label="限制系统目录访问"
          description="禁止直接访问关键系统目录"
          value={true}
          onChange={value => {
            console.log('Restrict system dirs changed to:', value);
          }}
        />

        <ToggleField
          label="保护隐藏文件"
          description="对隐藏文件和配置文件进行额外保护"
          value={true}
          onChange={value => {
            console.log('Protect hidden files changed to:', value);
          }}
        />

        <div className="space-y-2">
          <div className="text-sm font-medium">受保护的目录:</div>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>• /System (macOS 系统目录)</div>
            <div>• /bin, /sbin (系统二进制文件)</div>
            <div>• /etc (系统配置文件)</div>
            <div>• /var/log (系统日志)</div>
            <div>• ~/.ssh (SSH 密钥)</div>
          </div>
        </div>
      </SettingsSection>

      <SettingsSection
        title="网络安全"
        description="网络访问和数据传输安全设置"
      >
        <ToggleField
          label="限制网络访问"
          description="限制终端应用的网络访问权限"
          value={false}
          onChange={value => {
            console.log('Restrict network access changed to:', value);
          }}
        />

        <ToggleField
          label="HTTPS 优先"
          description="优先使用 HTTPS 连接进行数据传输"
          value={true}
          onChange={value => {
            console.log('HTTPS priority changed to:', value);
          }}
        />
      </SettingsSection>

      <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
          ⚠️ 安全提醒
        </div>
        <div className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
          修改安全设置可能影响系统安全性，请谨慎操作。建议保持默认的安全保护机制。
        </div>
      </div>
    </div>
  );
}
