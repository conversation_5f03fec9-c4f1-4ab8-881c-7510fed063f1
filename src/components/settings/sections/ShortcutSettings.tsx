import React, { useCallback, useEffect, useState } from 'react';
import { useShortcutStore } from '../../../stores/shortcutStore';
import { Platform, ShortcutCategory, ShortcutConfig } from '../../../types';
import {
  checkShortcutConflict,
  detectPlatform,
  formatShortcutDisplay,
  validateShortcutKeys,
} from '../../../utils/shortcutUtils';
import { Button } from '../../ui/Button';
import { Input } from '../../ui/Input';

interface ShortcutFieldProps {
  shortcut: ShortcutConfig;
  onUpdate: (action: string, keys: string[]) => void;
  conflicts: string[];
}

const ShortcutField: React.FC<ShortcutFieldProps> = ({
  shortcut,
  onUpdate,
  conflicts,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [currentKeys, setCurrentKeys] = useState<string[]>([]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (!isRecording) return;

      e.preventDefault();
      e.stopPropagation();

      const keys: string[] = [];

      if (e.ctrlKey) keys.push('ctrl');
      if (e.metaKey) keys.push('cmd');
      if (e.altKey) keys.push('alt');
      if (e.shiftKey) keys.push('shift');

      if (e.key && !['Control', 'Meta', 'Alt', 'Shift'].includes(e.key)) {
        keys.push(e.key.toLowerCase());
      }

      if (keys.length > 1) {
        setCurrentKeys(keys);
      }
    },
    [isRecording]
  );

  const handleKeyUp = useCallback(
    (e: KeyboardEvent) => {
      if (!isRecording || currentKeys.length === 0) return;

      e.preventDefault();

      if (validateShortcutKeys(currentKeys)) {
        onUpdate(shortcut.action, currentKeys);
      }

      setIsRecording(false);
      setCurrentKeys([]);
    },
    [isRecording, currentKeys, shortcut.action, onUpdate]
  );

  useEffect(() => {
    if (isRecording) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keyup', handleKeyUp);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keyup', handleKeyUp);
      };
    }
  }, [isRecording, handleKeyDown, handleKeyUp]);

  const displayKeys = isRecording ? currentKeys : shortcut.keys;
  const hasConflict = conflicts.length > 0;

  return (
    <div className="flex items-center justify-between py-2 px-3 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="flex-1">
        <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
          {shortcut.description}
        </div>
        {hasConflict && (
          <div className="text-xs text-red-500 mt-1">
            冲突: {conflicts.join(', ')}
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        <div
          className={`
            px-3 py-1 rounded border text-sm font-mono min-w-[100px] text-center cursor-pointer
            ${
              isRecording
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                : hasConflict
                  ? 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                  : 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            }
          `}
          onClick={() => {
            if (!isRecording) {
              setIsRecording(true);
              setCurrentKeys([]);
            }
          }}
        >
          {isRecording ? '按下快捷键...' : formatShortcutDisplay(displayKeys)}
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => onUpdate(shortcut.action, [])}
          disabled={shortcut.keys.length === 0}
        >
          清除
        </Button>
      </div>
    </div>
  );
};

interface ShortcutCategorySectionProps {
  category: ShortcutCategory;
  shortcuts: ShortcutConfig[];
  onUpdateShortcut: (action: string, keys: string[]) => void;
  allShortcuts: ShortcutConfig[];
}

const ShortcutCategorySection: React.FC<ShortcutCategorySectionProps> = ({
  category,
  shortcuts,
  onUpdateShortcut,
  allShortcuts,
}) => {
  const categoryNames = {
    [ShortcutCategory.TAB_MANAGEMENT]: 'Tab 管理',
    [ShortcutCategory.TERMINAL_OPERATION]: '终端操作',
    [ShortcutCategory.AI_FUNCTION]: 'AI 功能',
    [ShortcutCategory.APPLICATION]: '应用功能',
    [ShortcutCategory.CUSTOM]: '自定义',
  };

  return (
    <div className="space-y-3">
      <h4 className="font-semibold text-gray-900 dark:text-gray-100">
        {categoryNames[category]}
      </h4>
      <div className="space-y-2">
        {shortcuts.map(shortcut => {
          const conflicts =
            shortcut.keys.length > 0
              ? checkShortcutConflict(
                  shortcut.keys,
                  Object.fromEntries(allShortcuts.map(s => [s.action, s])),
                  shortcut.action
                )
              : [];

          return (
            <ShortcutField
              key={shortcut.action}
              shortcut={shortcut}
              onUpdate={onUpdateShortcut}
              conflicts={conflicts}
            />
          );
        })}
      </div>
    </div>
  );
};

export const ShortcutSettings: React.FC = () => {
  const {
    shortcuts,
    isEnabled,
    setShortcut,
    resetShortcuts,
    enableShortcuts,
    exportConfig,
    importConfig,
  } = useShortcutStore();

  const [platform] = useState<Platform>(detectPlatform());
  const [importData, setImportData] = useState('');
  const [showImport, setShowImport] = useState(false);

  const shortcutsList = Object.values(shortcuts);

  const categorizedShortcuts = {
    [ShortcutCategory.TAB_MANAGEMENT]: shortcutsList.filter(
      s => s.category === ShortcutCategory.TAB_MANAGEMENT
    ),
    [ShortcutCategory.TERMINAL_OPERATION]: shortcutsList.filter(
      s => s.category === ShortcutCategory.TERMINAL_OPERATION
    ),
    [ShortcutCategory.AI_FUNCTION]: shortcutsList.filter(
      s => s.category === ShortcutCategory.AI_FUNCTION
    ),
    [ShortcutCategory.APPLICATION]: shortcutsList.filter(
      s => s.category === ShortcutCategory.APPLICATION
    ),
    [ShortcutCategory.CUSTOM]: shortcutsList.filter(
      s => s.category === ShortcutCategory.CUSTOM
    ),
  };

  const handleUpdateShortcut = useCallback(
    (action: string, keys: string[]) => {
      const existingShortcut = shortcuts[action];
      if (existingShortcut) {
        setShortcut(action, {
          ...existingShortcut,
          keys,
        });
      }
    },
    [setShortcut, shortcuts]
  );

  const handleResetShortcuts = useCallback(() => {
    if (confirm('确定要重置所有快捷键到默认设置吗？此操作不可撤销。')) {
      resetShortcuts();
    }
  }, [resetShortcuts]);

  const handleExportConfig = useCallback(() => {
    const config = exportConfig();
    const blob = new Blob([JSON.stringify(config, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `shortcuts-config-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }, [exportConfig]);

  const handleImportConfig = useCallback(() => {
    try {
      const config = JSON.parse(importData);
      importConfig(config);
      setImportData('');
      setShowImport(false);
      alert('快捷键配置导入成功！');
    } catch (error) {
      alert('导入失败：配置格式无效');
    }
  }, [importData, importConfig]);

  return (
    <div className="space-y-6">
      {/* 全局控制 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            快捷键设置
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            当前平台: {platform === Platform.MACOS ? 'macOS' : 'Windows/Linux'}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={isEnabled}
              onChange={e => enableShortcuts(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              启用全局快捷键
            </span>
          </label>
        </div>
      </div>

      {/* 快捷键分类 */}
      <div className="space-y-6">
        {Object.entries(categorizedShortcuts).map(([category, shortcuts]) => {
          if (shortcuts.length === 0) return null;

          return (
            <ShortcutCategorySection
              key={category}
              category={category as ShortcutCategory}
              shortcuts={shortcuts}
              onUpdateShortcut={handleUpdateShortcut}
              allShortcuts={shortcutsList}
            />
          );
        })}
      </div>

      {/* 配置管理 */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">
          配置管理
        </h4>

        <div className="flex flex-wrap gap-3">
          <Button variant="outline" onClick={handleResetShortcuts}>
            重置为默认
          </Button>

          <Button variant="outline" onClick={handleExportConfig}>
            导出配置
          </Button>

          <Button variant="outline" onClick={() => setShowImport(!showImport)}>
            导入配置
          </Button>
        </div>

        {showImport && (
          <div className="mt-4 space-y-3">
            <Input
              value={importData}
              onChange={e => setImportData(e.target.value)}
              placeholder="粘贴配置JSON数据..."
              className="font-mono text-sm"
            />
            <div className="flex gap-2">
              <Button
                onClick={handleImportConfig}
                disabled={!importData.trim()}
              >
                导入
              </Button>
              <Button
                variant="ghost"
                onClick={() => {
                  setShowImport(false);
                  setImportData('');
                }}
              >
                取消
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
