import {
  updateAppearanceSettings,
  useSettingsStore,
} from '@/stores/settingsStore';
import { SettingsSection } from '../SettingsSection';
import { SelectField } from '../fields/SelectField';
import { SliderField } from '../fields/SliderField';
import { ToggleField } from '../fields/ToggleField';

export function AppearanceSettings() {
  const { appearance } = useSettingsStore();

  const themeOptions = [
    { value: 'dark', label: '暗色主题' },
    { value: 'light', label: '亮色主题' },
    { value: 'auto', label: '跟随系统' },
  ];

  const colorSchemeOptions = [
    { value: 'default', label: '默认配色' },
    { value: 'solarized', label: 'Solarized' },
    { value: 'monokai', label: 'Monokai' },
    { value: 'dracula', label: 'Dracula' },
    { value: 'one-dark', label: 'One Dark' },
    { value: 'github', label: 'GitHub' },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">外观设置</h2>
        <p className="text-muted-foreground">配置应用的主题、颜色和视觉效果</p>
      </div>

      <SettingsSection title="主题设置" description="选择应用的整体主题风格">
        <SelectField
          label="主题模式"
          description="选择暗色、亮色主题或跟随系统设置"
          value={appearance.theme}
          onChange={value =>
            updateAppearanceSettings({
              theme: value as 'dark' | 'light' | 'auto',
            })
          }
          options={themeOptions}
        />

        <SelectField
          label="颜色方案"
          description="选择终端和界面的颜色配色方案"
          value={appearance.colorScheme}
          onChange={value => updateAppearanceSettings({ colorScheme: value })}
          options={colorSchemeOptions}
        />
      </SettingsSection>

      <SettingsSection
        title="透明效果"
        description="配置窗口的透明度和模糊效果"
      >
        <SliderField
          label="窗口透明度"
          description="调整应用窗口的透明程度"
          value={appearance.opacity}
          onChange={value => updateAppearanceSettings({ opacity: value })}
          min={0.3}
          max={1.0}
          step={0.05}
          unit=""
        />

        <ToggleField
          label="背景模糊"
          description="在透明模式下启用背景模糊效果"
          value={appearance.blur}
          onChange={value => updateAppearanceSettings({ blur: value })}
        />
      </SettingsSection>

      <SettingsSection title="预览效果" description="实时预览当前的外观设置">
        <div
          className="p-4 border border-border rounded-lg"
          style={{
            opacity: appearance.opacity,
            backdropFilter: appearance.blur ? 'blur(10px)' : 'none',
          }}
        >
          <div className="text-sm font-medium mb-2">TAgent 终端预览</div>
          <div className="font-mono text-xs p-3 bg-muted rounded border">
            <div className="text-green-400">user@computer:~$</div>
            <div className="text-yellow-300">
              echo "当前主题: {appearance.theme}"
            </div>
            <div>当前主题: {appearance.theme}</div>
            <div className="text-blue-300">ls -la</div>
            <div className="text-muted-foreground">
              drwxr-xr-x config/
              <br />
              -rw-r--r-- settings.json
              <br />
              -rw-r--r-- theme.css
            </div>
          </div>
        </div>
      </SettingsSection>

      <SettingsSection title="动画效果" description="界面动画和过渡效果设置">
        <ToggleField
          label="启用动画"
          description="启用界面切换和悬停动画效果"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Animations changed to:', value);
          }}
        />

        <ToggleField
          label="减少动画"
          description="减少动画效果以提高性能（遵循系统偏好）"
          value={false} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Reduce animations changed to:', value);
          }}
        />
      </SettingsSection>

      <SettingsSection title="字体渲染" description="字体显示和渲染质量设置">
        <ToggleField
          label="子像素抗锯齿"
          description="启用子像素渲染以获得更清晰的文本"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Subpixel antialiasing changed to:', value);
          }}
        />

        <SelectField
          label="渲染质量"
          description="选择文本渲染的质量级别"
          value="high" // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Render quality changed to:', value);
          }}
          options={[
            { value: 'low', label: '低质量（性能优先）' },
            { value: 'medium', label: '中等质量' },
            { value: 'high', label: '高质量（质量优先）' },
          ]}
        />
      </SettingsSection>
    </div>
  );
}
