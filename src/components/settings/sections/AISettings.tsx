import { updateAISettings, useSettingsStore } from '@/stores/settingsStore';
import { SettingsSection } from '../SettingsSection';
import { InputField } from '../fields/InputField';
import { SelectField } from '../fields/SelectField';
import { SliderField } from '../fields/SliderField';
import { ToggleField } from '../fields/ToggleField';

export function AISettings() {
  const { ai } = useSettingsStore();

  const providerOptions = [
    { value: 'local-model', label: '本地模型' },
    { value: 'openai', label: 'OpenAI GPT' },
    { value: 'claude', label: 'Anthropic Claude' },
    { value: 'gemini', label: 'Google Gemini' },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">AI 设置</h2>
        <p className="text-muted-foreground">
          配置 AI 功能、模型选择和智能助手行为
        </p>
      </div>

      <SettingsSection title="AI 提供商" description="选择和配置 AI 服务提供商">
        <SelectField
          label="默认 AI 提供商"
          description="选择默认使用的 AI 服务"
          value={ai.defaultProvider}
          onChange={value => updateAISettings({ defaultProvider: value })}
          options={providerOptions}
        />

        {ai.defaultProvider === 'openai' && (
          <InputField
            label="OpenAI API 密钥"
            description="输入您的 OpenAI API 密钥"
            value=""
            onChange={_value => {
              console.log('OpenAI API key changed');
            }}
            type="password"
            placeholder="sk-..."
          />
        )}

        {ai.defaultProvider === 'claude' && (
          <InputField
            label="Claude API 密钥"
            description="输入您的 Anthropic Claude API 密钥"
            value=""
            onChange={_value => {
              console.log('Claude API key changed');
            }}
            type="password"
            placeholder="sk-ant-..."
          />
        )}

        {ai.defaultProvider === 'local-model' && (
          <div className="space-y-4">
            <InputField
              label="本地模型路径"
              description="本地 AI 模型文件的路径"
              value="/usr/local/models/llama-model.bin"
              onChange={value => {
                console.log('Local model path changed to:', value);
              }}
              placeholder="/path/to/model.bin"
            />

            <div className="text-sm text-muted-foreground">
              支持的模型格式: GGUF, GGML, SafeTensors
            </div>
          </div>
        )}
      </SettingsSection>

      <SettingsSection title="AI 功能" description="配置各种 AI 辅助功能">
        <ToggleField
          label="@command 自然语言转命令"
          description="启用使用 @command 将自然语言转换为终端命令"
          value={true} // TODO: 添加到 AI 设置中
          onChange={value => {
            console.log('Command translation changed to:', value);
          }}
        />

        <ToggleField
          label="智能建议"
          description="根据上下文自动建议相关命令"
          value={ai.autoSuggest}
          onChange={value => updateAISettings({ autoSuggest: value })}
        />

        <ToggleField
          label="危险命令确认"
          description="AI 检测到危险命令时要求确认"
          value={ai.confirmDangerousCommands}
          onChange={value =>
            updateAISettings({ confirmDangerousCommands: value })
          }
        />

        <ToggleField
          label="@model AI 对话"
          description="启用 @model 命令进行 AI 对话"
          value={true} // TODO: 添加到 AI 设置中
          onChange={value => {
            console.log('AI chat changed to:', value);
          }}
        />
      </SettingsSection>

      <SettingsSection title="历史记录" description="AI 交互历史的管理设置">
        <SliderField
          label="最大历史长度"
          description="保留的 AI 交互历史记录数量"
          value={ai.maxHistoryLength}
          onChange={value => updateAISettings({ maxHistoryLength: value })}
          min={10}
          max={1000}
          step={10}
          unit=" 条"
        />

        <ToggleField
          label="自动清理历史"
          description="自动清理超过限制的旧历史记录"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Auto cleanup history changed to:', value);
          }}
        />
      </SettingsSection>

      <SettingsSection title="模型参数" description="调整 AI 模型的行为参数">
        <SliderField
          label="创造性 (Temperature)"
          description="控制 AI 回复的创造性程度"
          value={0.7} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Temperature changed to:', value);
          }}
          min={0.0}
          max={2.0}
          step={0.1}
          unit=""
        />

        <SliderField
          label="最大输出长度"
          description="AI 回复的最大 token 数量"
          value={2048} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Max tokens changed to:', value);
          }}
          min={100}
          max={8192}
          step={100}
          unit=" tokens"
        />
      </SettingsSection>

      <SettingsSection
        title="隐私设置"
        description="AI 功能相关的隐私和安全设置"
      >
        <ToggleField
          label="本地处理优先"
          description="优先使用本地模型处理敏感内容"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Local processing priority changed to:', value);
          }}
        />

        <ToggleField
          label="匿名化数据"
          description="向云端 AI 发送数据前移除个人信息"
          value={true} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Anonymize data changed to:', value);
          }}
        />

        <ToggleField
          label="记录 AI 交互"
          description="将 AI 交互记录保存到本地日志"
          value={false} // TODO: 添加到设置结构中
          onChange={value => {
            console.log('Log AI interactions changed to:', value);
          }}
        />
      </SettingsSection>
    </div>
  );
}
