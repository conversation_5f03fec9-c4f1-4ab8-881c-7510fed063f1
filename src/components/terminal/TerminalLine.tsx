import React, { memo, useCallback } from 'react';
import { Position, TerminalLineProps } from '../../types';

export const TerminalLine: React.FC<TerminalLineProps> = memo(
  ({ line, lineNumber, theme, onSelect }) => {
    // 处理鼠标选择事件
    const handleMouseDown = useCallback(
      (event: React.MouseEvent) => {
        const target = event.currentTarget as HTMLElement;
        const rect = target.getBoundingClientRect();
        const x = event.clientX - rect.left;

        // 简单计算字符位置
        const charWidth = 8; // 大致估算，实际应该更精确
        const col = Math.floor(x / charWidth);

        const position: Position = { row: lineNumber, col };
        onSelect(position, position);
      },
      [lineNumber, onSelect]
    );

    const handleMouseMove = useCallback((_event: React.MouseEvent) => {
      // 处理拖拽选择
    }, []);

    const handleMouseUp = useCallback((_event: React.MouseEvent) => {
      // 处理选择结束
    }, []);

    // 渲染带样式的文本
    const renderStyledText = () => {
      if (!line.attributes.length) {
        return <span>{line.text || '\u00A0'}</span>; // 空行显示不间断空格
      }

      const elements = [];
      let currentIndex = 0;

      line.attributes.forEach((attr, index) => {
        // 渲染属性前的普通文本
        if (attr.start > currentIndex) {
          elements.push(
            <span key={`text-${index}`}>
              {line.text.slice(currentIndex, attr.start)}
            </span>
          );
        }

        // 渲染带样式的文本
        const styledText = line.text.slice(
          attr.start,
          attr.start + attr.length
        );
        const style: React.CSSProperties = {
          color: attr.foreground || theme.foreground,
          backgroundColor: attr.background || 'transparent',
          fontWeight: attr.bold ? 'bold' : 'normal',
          fontStyle: attr.italic ? 'italic' : 'normal',
          textDecoration: attr.underline ? 'underline' : 'none',
        };

        elements.push(
          <span key={`styled-${index}`} style={style}>
            {styledText}
          </span>
        );

        currentIndex = attr.start + attr.length;
      });

      // 渲染剩余的普通文本
      if (currentIndex < line.text.length) {
        elements.push(
          <span key="text-end">{line.text.slice(currentIndex)}</span>
        );
      }

      return elements.length > 0 ? (
        elements
      ) : (
        <span>{line.text || '\u00A0'}</span>
      );
    };

    return (
      <div
        className="terminal-line"
        data-line={lineNumber}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{
          minHeight: '1.2em',
          lineHeight: '1.2em',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all',
          userSelect: 'text',
          cursor: 'text',
          paddingLeft: '2px',
          paddingRight: '2px',
        }}
      >
        {renderStyledText()}
      </div>
    );
  }
);
