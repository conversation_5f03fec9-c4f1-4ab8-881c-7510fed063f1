import React, { memo, useEffect, useState } from 'react';
import { TerminalCursorProps } from '../../types';

export const TerminalCursor: React.FC<TerminalCursorProps> = memo(
  ({ position, theme, visible }) => {
    const [shouldBlink, setShouldBlink] = useState(true);

    // 光标闪烁效果
    useEffect(() => {
      if (!visible) {
        setShouldBlink(false);
        return;
      }

      const interval = setInterval(() => {
        setShouldBlink(prev => !prev);
      }, 500); // 500ms 闪烁间隔

      return () => clearInterval(interval);
    }, [visible]);

    if (!visible) {
      return null;
    }

    const cursorStyle: React.CSSProperties = {
      position: 'absolute',
      left: `${position.col * 8}px`, // 简单估算字符宽度
      top: `${position.row * 19.2}px`, // 简单估算行高 (fontSize * 1.2)
      width: '8px',
      height: '16px',
      backgroundColor: shouldBlink ? theme.cursor : 'transparent',
      border: shouldBlink ? 'none' : `1px solid ${theme.cursor}`,
      pointerEvents: 'none',
      zIndex: 10,
      transition: 'opacity 0.1s ease',
    };

    return <div className="terminal-cursor" style={cursorStyle} />;
  }
);
