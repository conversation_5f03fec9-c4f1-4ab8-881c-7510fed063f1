import React, { memo } from 'react';
import { TerminalBufferProps } from '../../types';
import { TerminalLine } from './TerminalLine';

export const TerminalBuffer: React.FC<TerminalBufferProps> = memo(
  ({ buffer, theme, onTextSelect }) => {
    return (
      <div className="terminal-buffer" style={{ fontFamily: 'inherit' }}>
        {buffer.lines.map((line, index) => (
          <TerminalLine
            key={`${line.timestamp}-${index}`}
            line={line}
            lineNumber={index}
            theme={theme}
            onSelect={onTextSelect}
          />
        ))}
      </div>
    );
  }
);
