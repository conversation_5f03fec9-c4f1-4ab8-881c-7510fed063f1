import { useRealTerminal } from '@/hooks';
import { useAppStore } from '@/stores';
import { ParsedSegment } from '@/utils/ansiParser';
import { cn } from '@/utils/helpers';
import React, {
  KeyboardEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';

// 单个文本段落组件
const TerminalSegment: React.FC<{ segment: ParsedSegment }> = ({ segment }) => {
  const style: React.CSSProperties = {
    color: segment.style.color,
    backgroundColor: segment.style.backgroundColor,
    fontWeight: segment.style.bold ? 'bold' : 'normal',
    fontStyle: segment.style.italic ? 'italic' : 'normal',
    textDecoration:
      [
        segment.style.underline ? 'underline' : '',
        segment.style.strikethrough ? 'line-through' : '',
      ]
        .filter(Boolean)
        .join(' ') || 'none',
    opacity: segment.style.dim ? 0.6 : 1,
    animation: segment.style.blink ? 'blink 1s linear infinite' : 'none',
  };

  // 处理反色显示
  if (segment.style.reverse) {
    style.color = segment.style.backgroundColor || '#ffffff';
    style.backgroundColor = segment.style.color || '#000000';
  }

  return (
    <span style={style} className="whitespace-pre">
      {segment.text}
    </span>
  );
};

// 终端行组件
interface TerminalLineProps {
  segments: ParsedSegment[];
  type?: 'input' | 'output' | 'error' | 'system';
  rawText?: string;
}

const TerminalLineComponent: React.FC<TerminalLineProps> = ({
  segments,
  type = 'output',
  rawText,
}) => {
  const lineClassName = cn('font-mono text-sm leading-relaxed', {
    'text-green-400': type === 'input',
    'text-red-400': type === 'error',
    'text-blue-400': type === 'system',
    'text-gray-100': type === 'output',
  });

  return (
    <div className={lineClassName} title={rawText}>
      {segments.map((segment, index) => (
        <TerminalSegment key={index} segment={segment} />
      ))}
    </div>
  );
};

export interface TerminalDisplayProps {
  fontSize?: number;
  onResize?: (cols: number, rows: number) => void;
  tabId?: string;
  isActive?: boolean;
}

export const TerminalDisplay: React.FC<TerminalDisplayProps> = ({
  fontSize = 14,
  onResize,
  tabId = 'default',
  isActive = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { theme: appTheme } = useAppStore();

  const {
    terminalId,
    lines,
    isConnected,
    isLoading,
    error,
    createTerminal,
    destroyTerminal,
    handleKeyInput,
    resize,
  } = useRealTerminal({
    rows: 24,
    cols: 80,
    // 不指定shell，让后端自动检测最佳shell
  });

  // 使用tabId作为调试标识
  const debugId = `tab-${tabId}`;

  // 使用 ref 来跟踪是否已经初始化，避免 StrictMode 重复执行
  const terminalInitialized = useRef(false);
  const hasBeenActive = useRef(false);

  // 状态管理优化
  const [hasAttemptedCreation, setHasAttemptedCreation] = useState(false);
  const [connectionStartTime, setConnectionStartTime] = useState<number | null>(
    null
  );

  // IME 输入法状态
  const [isComposing, setIsComposing] = useState(false);
  const [compositionData, setCompositionData] = useState('');

  // 跟踪是否曾经活跃过
  useEffect(() => {
    if (isActive && !hasBeenActive.current) {
      hasBeenActive.current = true;
    }
  }, [isActive]);

  // 立即启动终端创建，一旦曾经活跃过就创建
  useEffect(() => {
    if (
      hasBeenActive.current &&
      !terminalId &&
      !hasAttemptedCreation &&
      !isLoading &&
      !terminalInitialized.current
    ) {
      console.log(
        `[TerminalDisplay:${debugId}] 开始创建终端 (曾经活跃过)`
      );
      terminalInitialized.current = true;
      setHasAttemptedCreation(true);
      setConnectionStartTime(Date.now());

      try {
        createTerminal();
      } catch (error) {
        console.error(`[TerminalDisplay:${debugId}] 创建终端失败:`, error);
        // 重置标志，允许重试
        terminalInitialized.current = false;
      }
    } else {
      console.log(`[TerminalDisplay:${debugId}] 跳过终端创建:`, {
        hasBeenActive: hasBeenActive.current,
        terminalId: !!terminalId,
        hasAttemptedCreation,
        isLoading,
        initialized: terminalInitialized.current,
      });
    }
  }, [
    isActive,
    terminalId,
    hasAttemptedCreation,
    isLoading,
    createTerminal,
    debugId,
  ]);

  // 在组件卸载时清理终端
  useEffect(() => {
    return () => {
      if (terminalId) {
        console.log(
          `[TerminalDisplay:${debugId}] 组件卸载，清理终端:`,
          terminalId
        );
        destroyTerminal();
      }
    };
  }, [terminalId, destroyTerminal, debugId]);

  // 自动滚动到底部
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [lines]);

  // 处理键盘输入
  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      // 如果正在进行输入法输入，跳过处理
      if (isComposing) {
        return;
      }

      e.preventDefault();

      const key = e.key;
      const ctrlKey = e.ctrlKey;
      const metaKey = e.metaKey;

      // 直接调用handleKeyInput处理所有输入，不区分普通字符和特殊键
      // 真实终端应该每个字符都立即发送到PTY
      handleKeyInput(key, ctrlKey, metaKey);
    },
    [handleKeyInput, isComposing]
  );

  // 处理输入法开始
  const handleCompositionStart = useCallback(() => {
    setIsComposing(true);
    setCompositionData('');
  }, []);

  // 处理输入法更新
  const handleCompositionUpdate = useCallback(
    (e: React.CompositionEvent<HTMLInputElement>) => {
      setCompositionData(e.data);
    },
    []
  );

  // 处理输入法结束
  const handleCompositionEnd = useCallback(
    (e: React.CompositionEvent<HTMLInputElement>) => {
      setIsComposing(false);
      const inputData = e.data;
      setCompositionData('');

      // 发送组合输入的内容到终端
      if (inputData && inputData.length > 0) {
        handleKeyInput(inputData);
      }
    },
    [handleKeyInput]
  );

  // 处理容器大小变化 - 添加防抖处理
  useEffect(() => {
    if (!containerRef.current || !isConnected) return;

    let resizeTimeout: ReturnType<typeof setTimeout>;
    let lastSize = { cols: 0, rows: 0 };

    const resizeObserver = new ResizeObserver(() => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        const cols = Math.floor(clientWidth / (fontSize * 0.6));
        const rows = Math.floor(clientHeight / (fontSize * 1.2));

        // 防抖处理，避免频繁调整
        if (resizeTimeout) {
          clearTimeout(resizeTimeout);
        }

        resizeTimeout = setTimeout(() => {
          // 只有当大小真正改变时才调整
          if (cols > 0 && rows > 0 && (cols !== lastSize.cols || rows !== lastSize.rows)) {
            lastSize = { cols, rows };
            resize(rows, cols);
            onResize?.(cols, rows);
            console.log('[TerminalDisplay] 终端大小已调整:', { rows, cols });
          }
        }, 150); // 150ms防抖延迟
      }
    });

    resizeObserver.observe(containerRef.current);
    return () => {
      resizeObserver.disconnect();
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }
    };
  }, [fontSize, isConnected, resize, onResize]);

  // 聚焦输入框
  useEffect(() => {
    if (isActive && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isActive]);

  // 计算连接时间
  const connectionTime = connectionStartTime
    ? ((Date.now() - connectionStartTime) / 1000).toFixed(1)
    : '0.0';

  // 渲染状态信息
  const renderStatus = () => {
    if (error) {
      return (
        <div className="flex items-center justify-center h-full text-red-400">
          <div className="text-center">
            <div className="text-lg font-semibold mb-2">连接失败</div>
            <div className="text-sm opacity-75 mb-4">{error}</div>
            <div className="space-x-2">
              <button
                onClick={() => {
                  setHasAttemptedCreation(false);
                  setConnectionStartTime(Date.now());
                  createTerminal();
                }}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-white text-sm"
              >
                重新连接
              </button>
            </div>
          </div>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full text-blue-400">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
            <div className="text-lg font-semibold mb-2">正在连接终端...</div>
            <div className="text-sm opacity-75">
              连接时间: {connectionTime}s
            </div>
            {connectionTime > '5.0' && (
              <div className="text-xs opacity-50 mt-2">
                连接时间较长，请检查后端服务
              </div>
            )}
          </div>
        </div>
      );
    }

    return null;
  };

  const statusComponent = renderStatus();
  if (statusComponent) {
    return (
      <div
        className={cn(
          'h-full w-full',
          appTheme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-black'
        )}
      >
        {statusComponent}
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col font-mono text-sm bg-black text-green-400 relative">
      {/* 终端内容 */}
      <div
        ref={containerRef}
        className="flex-1 overflow-auto p-4 cursor-text"
        onClick={() => inputRef.current?.focus()}
        style={{ fontSize: `${fontSize}px` }}
      >
        {/* 渲染所有终端行 */}
        {lines.map((line, index) => (
          <TerminalLineComponent
            key={`${line.timestamp}-${index}`}
            segments={line.segments}
            type={line.type}
            rawText={line.rawText}
          />
        ))}

        {/* 错误状态显示 */}
        {error && (
          <div className="text-red-400 bg-red-900/20 p-2 rounded mt-2">
            错误: {error}
          </div>
        )}

        {/* 加载状态 */}
        {isLoading && (
          <div className="text-yellow-400">
            <span className="animate-spin mr-2">⚡</span>
            正在连接终端...
          </div>
        )}
      </div>

      {/* 隐藏的输入框用于捕获键盘事件和IME输入 */}
      <input
        ref={inputRef}
        className="absolute -left-full opacity-0"
        value={compositionData}
        onChange={() => {}} // 受控组件，但不使用value
        onKeyDown={handleKeyDown}
        onCompositionStart={handleCompositionStart}
        onCompositionUpdate={handleCompositionUpdate}
        onCompositionEnd={handleCompositionEnd}
        autoFocus={isActive}
      />
    </div>
  );
};
