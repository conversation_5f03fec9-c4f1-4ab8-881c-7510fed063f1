import React, { memo } from 'react';
import { TerminalSelectionProps } from '../../types';

export const TerminalSelection: React.FC<TerminalSelectionProps> = memo(
  ({ selection, theme }) => {
    if (!selection.active || !selection.start || !selection.end) {
      return null;
    }

    const { start, end } = selection;

    // 确保开始位置在结束位置之前
    const actualStart = {
      row: Math.min(start.row, end.row),
      col:
        start.row === Math.min(start.row, end.row)
          ? Math.min(start.col, end.col)
          : start.row < end.row
            ? start.col
            : end.col,
    };

    const actualEnd = {
      row: Math.max(start.row, end.row),
      col:
        end.row === Math.max(start.row, end.row)
          ? Math.max(start.col, end.col)
          : end.row > start.row
            ? end.col
            : start.col,
    };

    const selections = [];

    // 为每一行创建选择区域
    for (let row = actualStart.row; row <= actualEnd.row; row++) {
      let startCol = 0;
      let endCol = 80; // 假设最大列数，实际应该动态计算

      if (row === actualStart.row) {
        startCol = actualStart.col;
      }

      if (row === actualEnd.row) {
        endCol = actualEnd.col;
      }

      const selectionStyle: React.CSSProperties = {
        position: 'absolute',
        left: `${startCol * 8}px`, // 简单估算字符宽度
        top: `${row * 19.2}px`, // 简单估算行高
        width: `${(endCol - startCol) * 8}px`,
        height: '19.2px',
        backgroundColor: theme.selection,
        pointerEvents: 'none',
        zIndex: 5,
        opacity: 0.3,
      };

      selections.push(
        <div
          key={`selection-${row}`}
          className="terminal-selection-line"
          style={selectionStyle}
        />
      );
    }

    return <div className="terminal-selection">{selections}</div>;
  }
);
