import { useTabShortcuts } from '@/hooks';
import { useTerminalStore } from '@/stores';
import { cn } from '@/utils/helpers';
import React, { useEffect, useRef } from 'react';
import { TabBar } from './TabBar';
import { TabContent } from './TabContent';

interface TabManagerProps {
  className?: string;
}

export const TabManager: React.FC<TabManagerProps> = ({ className }) => {
  const { tabs, activeTabId, createTab, closeTab, setActiveTab } =
    useTerminalStore();

  // 使用 ref 来跟踪是否已经初始化，避免 StrictMode 重复执行
  const initialized = useRef(false);

  // 启用快捷键支持
  useTabShortcuts();

  // 初始化时创建第一个Tab
  useEffect(() => {
    if (tabs.length === 0 && !initialized.current) {
      console.log('[TabManager] 检测到无Tab，开始创建初始Tab');
      initialized.current = true;

      try {
        const newTabId = createTab();
        setActiveTab(newTabId);
        console.log('[TabManager] 初始Tab创建成功，ID:', newTabId);
      } catch (error) {
        console.error('[TabManager] 创建初始Tab失败:', error);
        // 重置初始化标志，允许重试
        initialized.current = false;
      }
    } else if (tabs.length > 0) {
      console.log('[TabManager] 已存在', tabs.length, '个Tab，跳过初始化');
    }
  }, [tabs.length, createTab, setActiveTab]);

  // 确保有活跃的Tab
  useEffect(() => {
    if (tabs.length > 0 && !activeTabId) {
      console.log('[TabManager] 设置默认活跃Tab:', tabs[0].id);
      setActiveTab(tabs[0].id);
    }
  }, [tabs, activeTabId, setActiveTab]);

  const handleTabSelect = (tabId: string) => {
    console.log('[TabManager] 切换到Tab:', tabId);
    setActiveTab(tabId);
  };

  const handleTabClose = (tabId: string) => {
    if (tabs.length > 1) {
      console.log('[TabManager] 关闭Tab:', tabId);
      closeTab(tabId);
    } else {
      console.warn('[TabManager] 无法关闭最后一个Tab');
    }
  };

  const handleNewTab = () => {
    console.log('[TabManager] 手动创建新Tab');
    const newTabId = createTab();
    setActiveTab(newTabId);
  };

  // 如果没有Tab，显示加载状态
  if (tabs.length === 0) {
    return (
      <div className={cn('flex flex-col h-full', className)}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
            <div className="text-lg font-semibold mb-2">正在初始化终端...</div>
            <div className="text-sm text-gray-500">
              正在创建第一个终端标签页
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Tab栏 */}
      <TabBar
        tabs={tabs}
        activeTabId={activeTabId || ''}
        onTabSelect={handleTabSelect}
        onTabClose={handleTabClose}
        onNewTab={handleNewTab}
      />

      {/* Tab内容区域 - 渲染所有Tab但只显示活跃的Tab */}
      <div className="flex-1 overflow-hidden relative">
        {tabs.map(tab => (
          <div
            key={tab.id}
            className={cn(
              'absolute inset-0',
              tab.id === activeTabId ? 'block' : 'hidden'
            )}
          >
            <TabContent tab={tab} isActive={tab.id === activeTabId} />
          </div>
        ))}
      </div>
    </div>
  );
};
