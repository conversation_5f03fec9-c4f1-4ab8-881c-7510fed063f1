import { useAppStore } from '@/stores';
import { TerminalTab } from '@/types';
import { cn } from '@/utils/helpers';
import React from 'react';
import { TerminalDisplay } from '../terminal/TerminalDisplay';

interface TabContentProps {
  tab: TerminalTab;
  isActive?: boolean;
}

export const TabContent: React.FC<TabContentProps> = ({
  tab,
  isActive = false,
}) => {
  const { theme } = useAppStore();

  const handleResize = (cols: number, rows: number) => {
    // TODO: 调整终端大小
    console.log(`Tab ${tab.id} resize:`, { cols, rows });
  };

  return (
    <div
      className={cn(
        'h-full flex flex-col',
        theme === 'dark' ? 'bg-gray-900' : 'bg-white'
      )}
    >
      {/* 终端工作目录信息 */}
      <div
        className={cn(
          'px-4 py-2 text-xs border-b',
          theme === 'dark'
            ? 'bg-gray-800 border-gray-700 text-gray-400'
            : 'bg-gray-50 border-gray-200 text-gray-600'
        )}
      >
        <span className="font-mono">
          {tab.shell} - {tab.currentDirectory}
        </span>
      </div>

      {/* 终端显示区域 - 始终渲染以保持状态 */}
      <div className="flex-1 overflow-hidden">
        <TerminalDisplay
          tabId={tab.id}
          isActive={isActive}
          fontSize={14}
          onResize={handleResize}
        />
      </div>
    </div>
  );
};
