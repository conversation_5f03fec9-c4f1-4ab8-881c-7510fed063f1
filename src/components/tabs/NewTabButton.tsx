import { useAppStore } from '@/stores';
import { cn } from '@/utils/helpers';
import React from 'react';

interface NewTabButtonProps {
  onClick: () => void;
}

export const NewTabButton: React.FC<NewTabButtonProps> = ({ onClick }) => {
  const { theme } = useAppStore();

  return (
    <button
      onClick={onClick}
      className={cn(
        'w-6 h-6 rounded flex items-center justify-center',
        'transition-colors duration-200 border',
        theme === 'dark'
          ? 'text-gray-500 hover:text-green-400 hover:bg-gray-800 border-gray-600 hover:border-green-400'
          : 'text-gray-400 hover:text-blue-600 hover:bg-gray-50 border-gray-300 hover:border-blue-600'
      )}
      title="新建标签页 (Ctrl+T)"
    >
      <svg width="10" height="10" viewBox="0 0 12 12" fill="currentColor">
        <path d="M6 1a.5.5 0 0 1 .5.5V5.5H11a.5.5 0 0 1 0 1H6.5V11a.5.5 0 0 1-1 0V6.5H1a.5.5 0 0 1 0-1h4.5V1.5A.5.5 0 0 1 6 1z" />
      </svg>
    </button>
  );
};
