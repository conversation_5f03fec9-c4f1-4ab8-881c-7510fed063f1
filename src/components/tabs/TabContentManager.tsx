import { useTerminalStore } from '@/stores';
import { cn } from '@/utils/helpers';
import React from 'react';
import { TabContent } from './TabContent';

interface TabContentManagerProps {
  className?: string;
}

export const TabContentManager: React.FC<TabContentManagerProps> = ({ className }) => {
  const { tabs, activeTabId } = useTerminalStore();

  // 如果没有tab，显示欢迎界面
  if (tabs.length === 0) {
    return (
      <div className={cn('flex flex-col items-center justify-center h-full', className)}>
        <div className="text-center space-y-4">
          <div className="text-6xl mb-4">🚀</div>
          <h2 className="text-2xl font-semibold text-foreground">欢迎使用 TAgent</h2>
          <p className="text-muted-foreground max-w-md">
            AI 增强的终端应用，让命令行操作更智能、更高效
          </p>
          <div className="text-sm text-muted-foreground mt-6">
            <p>快捷键：</p>
            <ul className="mt-2 space-y-1">
              <li>Ctrl+T - 新建终端</li>
              <li>Ctrl+W - 关闭当前终端</li>
              <li>Ctrl+Shift+C - 打开AI助手</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex-1 overflow-hidden relative', className)}>
      {/* Tab内容区域 - 渲染所有Tab但只显示活跃的Tab */}
      {tabs.map(tab => (
        <div
          key={tab.id}
          className={cn(
            'absolute inset-0',
            tab.id === activeTabId ? 'block' : 'hidden'
          )}
        >
          <TabContent tab={tab} isActive={tab.id === activeTabId} />
        </div>
      ))}
    </div>
  );
};

TabContentManager.displayName = 'TabContentManager';
