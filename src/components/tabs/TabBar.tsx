import { useAppStore } from '@/stores';
import { TabBarProps } from '@/types';
import { cn } from '@/utils/helpers';
import React from 'react';
import { NewTabButton } from './NewTabButton';
import { TabItem } from './TabItem';

export const TabBar: React.FC<TabBarProps & { showBorder?: boolean }> = ({
  tabs,
  activeTabId,
  onTabSelect,
  onTabClose,
  onNewTab,
  showBorder = true,
}) => {
  const { theme } = useAppStore();

  return (
    <div
      className={cn(
        'flex items-center min-h-[40px] px-3',
        showBorder && 'border-b',
        theme === 'dark'
          ? 'bg-gray-900 border-gray-700'
          : 'bg-gray-100 border-gray-300'
      )}
    >
      {/* Tab列表容器 - 支持滚动，确保不会溢出 */}
      <div className="flex items-center overflow-x-auto flex-1 space-x-0 min-w-0 scrollbar-hide">
        <div className="flex items-center space-x-0 min-w-max">
          {tabs.map(tab => (
            <TabItem
              key={tab.id}
              tab={tab}
              isActive={tab.id === activeTabId}
              onSelect={() => onTabSelect(tab.id)}
              onClose={() => onTabClose(tab.id)}
              canClose={tabs.length > 1}
            />
          ))}
        </div>
      </div>

      {/* 新建Tab按钮 - 标识2 */}
      <div className="ml-2 flex-shrink-0">
        <NewTabButton onClick={onNewTab} />
      </div>
    </div>
  );
};
