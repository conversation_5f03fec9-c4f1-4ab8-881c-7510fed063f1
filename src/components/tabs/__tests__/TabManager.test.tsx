import { useTerminalStore } from '@/stores';
import { act, fireEvent, render, screen, within } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TabManager } from '../TabManager';

// Mock stores
vi.mock('@/stores', () => ({
  useTerminalStore: vi.fn(),
  useAppStore: vi.fn(() => ({
    theme: 'dark',
  })),
}));

// Mock hooks
vi.mock('@/hooks', () => ({
  useTabShortcuts: vi.fn(),
}));

describe('TabManager', () => {
  const mockTerminalStore = {
    tabs: [] as any[],
    activeTabId: null as string | null,
    createTab: vi.fn(),
    closeTab: vi.fn(),
    setActiveTab: vi.fn(),
    getActiveTab: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useTerminalStore as any).mockReturnValue(mockTerminalStore);
  });

  describe('初始化', () => {
    it('应该在没有Tab时自动创建第一个Tab', () => {
      mockTerminalStore.tabs = [];
      mockTerminalStore.createTab.mockReturnValue('tab-1');

      render(<TabManager />);

      expect(mockTerminalStore.createTab).toHaveBeenCalledTimes(1);
    });

    it('应该在已有Tab时不创建新Tab', () => {
      mockTerminalStore.tabs = [
        {
          id: 'tab-1',
          title: 'Terminal 1',
          shell: 'zsh',
          currentDirectory: '/Users/<USER>',
          isActive: true,
          createdAt: new Date(),
          lastActivity: new Date(),
        },
      ];
      mockTerminalStore.activeTabId = 'tab-1';
      mockTerminalStore.getActiveTab.mockReturnValue(mockTerminalStore.tabs[0]);

      render(<TabManager />);

      expect(mockTerminalStore.createTab).not.toHaveBeenCalled();
    });
  });

  describe('Tab操作', () => {
    beforeEach(() => {
      mockTerminalStore.tabs = [
        {
          id: 'tab-1',
          title: 'Terminal 1',
          shell: 'zsh',
          currentDirectory: '/Users/<USER>',
          isActive: true,
          createdAt: new Date(),
          lastActivity: new Date(),
        },
        {
          id: 'tab-2',
          title: 'Terminal 2',
          shell: 'zsh',
          currentDirectory: '/Users/<USER>',
          isActive: false,
          createdAt: new Date(),
          lastActivity: new Date(),
        },
      ];
      mockTerminalStore.activeTabId = 'tab-1';
      mockTerminalStore.getActiveTab.mockReturnValue(mockTerminalStore.tabs[0]);
    });

    it('应该能够切换Tab', () => {
      render(<TabManager />);

      const tabButton = screen.getByText('Terminal 2');

      act(() => {
        fireEvent.click(tabButton);
      });

      expect(mockTerminalStore.setActiveTab).toHaveBeenCalledWith('tab-2');
    });

    it('应该能够创建新Tab', () => {
      mockTerminalStore.createTab.mockReturnValue('tab-3');

      render(<TabManager />);

      const newTabButton = screen.getByTitle('新建标签页 (Ctrl+T)');

      act(() => {
        fireEvent.click(newTabButton);
      });

      expect(mockTerminalStore.createTab).toHaveBeenCalled();
      expect(mockTerminalStore.setActiveTab).toHaveBeenCalledWith('tab-3');
    });

    it('应该能够关闭Tab（当有多个Tab时）', () => {
      render(<TabManager />);

      // 直接查找所有关闭按钮（即使是隐藏的）
      const allCloseButtons = screen.getAllByTitle('关闭标签页');
      expect(allCloseButtons.length).toBeGreaterThan(0);

      // 找到Terminal 1对应的关闭按钮
      // 由于关闭按钮在tab item内部，我们可以通过DOM结构找到正确的按钮
      const terminal1Text = screen.getByText('Terminal 1');
      const terminal1Tab = terminal1Text.closest('div');

      if (terminal1Tab) {
        const closeButtonInTab =
          within(terminal1Tab).queryByTitle('关闭标签页');

        if (closeButtonInTab) {
          // 触发鼠标悬停和点击
          act(() => {
            fireEvent.mouseEnter(terminal1Tab);
            fireEvent.click(closeButtonInTab);
          });

          expect(mockTerminalStore.closeTab).toHaveBeenCalledWith('tab-1');
        } else {
          // 如果在tab内找不到，就点击第一个关闭按钮
          act(() => {
            fireEvent.click(allCloseButtons[0]);
          });

          expect(mockTerminalStore.closeTab).toHaveBeenCalled();
        }
      } else {
        // 备用方案：直接点击第一个关闭按钮
        act(() => {
          fireEvent.click(allCloseButtons[0]);
        });

        expect(mockTerminalStore.closeTab).toHaveBeenCalled();
      }
    });

    it('应该防止关闭最后一个Tab', () => {
      mockTerminalStore.tabs = [
        {
          id: 'tab-1',
          title: 'Terminal 1',
          shell: 'zsh',
          currentDirectory: '/Users/<USER>',
          isActive: true,
          createdAt: new Date(),
          lastActivity: new Date(),
        },
      ];

      render(<TabManager />);

      // 尝试关闭唯一的Tab，应该被阻止
      // 在只有一个Tab时，关闭按钮应该不可见或禁用
      const closeButtons = screen.queryAllByTitle('关闭标签页');
      expect(closeButtons).toHaveLength(0);
    });
  });

  describe('渲染', () => {
    it('应该渲染Tab栏和Tab内容', () => {
      mockTerminalStore.tabs = [
        {
          id: 'tab-1',
          title: 'Terminal 1',
          shell: 'zsh',
          currentDirectory: '/Users/<USER>',
          isActive: true,
          createdAt: new Date(),
          lastActivity: new Date(),
        },
      ];
      mockTerminalStore.activeTabId = 'tab-1';
      mockTerminalStore.getActiveTab.mockReturnValue(mockTerminalStore.tabs[0]);

      render(<TabManager />);

      // 应该显示Tab标题
      expect(screen.getByText('Terminal 1')).toBeDefined();

      // 应该显示新建Tab按钮
      expect(screen.getByTitle('新建标签页 (Ctrl+T)')).toBeDefined();

      // 应该显示当前活动Tab的内容
      expect(screen.getByText('zsh - /Users/<USER>')).toBeDefined();
    });

    it('应该在没有活动Tab时正确处理', () => {
      mockTerminalStore.tabs = [];
      mockTerminalStore.activeTabId = null;
      mockTerminalStore.getActiveTab.mockReturnValue(null);

      render(<TabManager />);

      // 应该显示新建Tab按钮
      expect(screen.getByTitle('新建标签页 (Ctrl+T)')).toBeDefined();
    });
  });
});
