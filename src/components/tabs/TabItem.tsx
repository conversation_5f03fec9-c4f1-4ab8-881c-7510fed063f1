import { useAppStore, useTerminalStore } from '@/stores';
import { TerminalTab } from '@/types';
import { cn } from '@/utils/helpers';
import React, { useState } from 'react';

interface TabItemProps {
  tab: TerminalTab;
  isActive: boolean;
  onSelect: () => void;
  onClose: () => void;
  canClose: boolean;
}

export const TabItem: React.FC<TabItemProps> = ({
  tab,
  isActive,
  onSelect,
  onClose,
  canClose,
}) => {
  const { theme } = useAppStore();
  const { updateTab } = useTerminalStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(tab.title);

  const handleDoubleClick = () => {
    setIsEditing(true);
    setEditTitle(tab.title);
  };

  const handleTitleSubmit = () => {
    if (editTitle.trim() && editTitle !== tab.title) {
      updateTab(tab.id, { title: editTitle.trim() });
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSubmit();
    } else if (e.key === 'Escape') {
      setEditTitle(tab.title);
      setIsEditing(false);
    }
  };

  return (
    <div
      className={cn(
        'flex items-center group min-w-[100px] max-w-[180px] h-8 px-3',
        'cursor-pointer transition-all duration-200 relative',
        'border-r border-gray-600',
        isActive
          ? theme === 'dark'
            ? 'bg-gray-800 text-green-400 border-b-2 border-b-green-400'
            : 'bg-white text-blue-600 border-b-2 border-b-blue-600'
          : theme === 'dark'
            ? 'bg-gray-900 text-gray-400 hover:bg-gray-800 hover:text-gray-300'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-50 hover:text-gray-800'
      )}
      onClick={onSelect}
      onDoubleClick={handleDoubleClick}
    >
      {/* Tab内容 */}
      <div className="flex items-center flex-1 min-w-0">
        {/* Tab标题 */}
        {isEditing ? (
          <input
            type="text"
            value={editTitle}
            onChange={e => setEditTitle(e.target.value)}
            onBlur={handleTitleSubmit}
            onKeyDown={handleKeyDown}
            className={cn(
              'flex-1 bg-transparent border-none outline-none text-xs font-mono',
              'focus:ring-1 focus:ring-blue-500 rounded px-1',
              theme === 'dark' ? 'text-green-400' : 'text-blue-600'
            )}
            autoFocus
            onClick={e => e.stopPropagation()}
          />
        ) : (
          <span className="flex-1 text-xs font-mono truncate" title={tab.title}>
            {tab.title}
          </span>
        )}
      </div>

      {/* 关闭按钮 */}
      {canClose && (
        <button
          onClick={e => {
            e.stopPropagation();
            onClose();
          }}
          className={cn(
            'ml-2 w-3 h-3 rounded flex items-center justify-center',
            'transition-colors duration-200 flex-shrink-0',
            'opacity-0 group-hover:opacity-100',
            theme === 'dark'
              ? 'hover:bg-red-600 text-gray-500 hover:text-white'
              : 'hover:bg-red-500 text-gray-400 hover:text-white'
          )}
          title="关闭标签页"
        >
          <svg width="6" height="6" viewBox="0 0 8 8" fill="currentColor">
            <path d="M0.146 0.146a0.5 0.5 0 0 1 0.708 0L4 3.293l3.146-3.147a0.5 0.5 0 0 1 0.708 0.708L4.707 4l3.147 3.146a0.5 0.5 0 0 1-0.708 0.708L4 4.707 0.854 7.854a0.5 0.5 0 0 1-0.708-0.708L3.293 4 0.146 0.854a0.5 0.5 0 0 1 0-0.708z" />
          </svg>
        </button>
      )}
    </div>
  );
};
