// History Panel Component - Main container for command history functionality
// Integrates search, list, and navigation features

import React, { useCallback, useEffect } from 'react';
import { useHistoryStore } from '../../stores/historyStore';
import { HistoryEntry } from '../../types/history';
import { Button } from '../ui/Button';
import HistoryItem from './HistoryItem';
import HistorySearch from './HistorySearch';

interface HistoryPanelProps {
  terminalId: string;
  isVisible?: boolean;
  maxHeight?: string;
  onCommandExecute?: (command: string) => void;
  onClose?: () => void;
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({
  terminalId,
  isVisible = true,
  maxHeight = '400px',
  onCommandExecute,
  onClose,
}) => {
  const {
    filteredEntries,
    searchQuery,
    isLoading,
    error,
    selectedEntry,
    loadHistory,
    searchHistory,
    setSearchQuery,
    clearSearch,
    selectEntry,
    executeHistoryCommand,
    resetState,
  } = useHistoryStore();

  // Load history on mount and terminal change
  useEffect(() => {
    if (terminalId && isVisible) {
      loadHistory(terminalId);
    }
  }, [terminalId, isVisible, loadHistory]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      resetState();
    };
  }, [resetState]);

  // Handle search
  const handleSearch = useCallback(
    (query: string) => {
      if (query.trim()) {
        searchHistory(terminalId, query);
      } else {
        clearSearch();
      }
    },
    [terminalId, searchHistory, clearSearch]
  );

  // Handle command execution
  const handleExecute = useCallback(
    (entry: HistoryEntry) => {
      const command = entry.command.raw_input;
      onCommandExecute?.(command);
      executeHistoryCommand(entry);
    },
    [onCommandExecute, executeHistoryCommand]
  );

  // Handle copy to clipboard
  const handleCopy = useCallback(async (command: string) => {
    try {
      await navigator.clipboard.writeText(command);
      // Show a brief success indication (could be enhanced with toast notifications)
      console.log('Command copied to clipboard:', command);
    } catch (error) {
      console.error('Failed to copy command:', error);
    }
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVisible) return;

      // Only handle navigation when the history panel has focus
      const historyPanel = document.querySelector('[data-history-panel]');
      if (!historyPanel?.contains(document.activeElement)) return;

      if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        e.preventDefault();

        const direction = e.key === 'ArrowUp' ? 'up' : 'down';
        const store = useHistoryStore.getState();
        store.navigateHistory(direction);
      } else if (e.key === 'Enter' && selectedEntry) {
        e.preventDefault();
        handleExecute(selectedEntry);
      } else if (e.key === 'Escape') {
        e.preventDefault();
        onClose?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, selectedEntry, handleExecute, onClose]);

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg"
      data-history-panel
      style={{ maxHeight }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <svg
            className="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            命令历史
          </h3>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            ({filteredEntries.length} 条记录)
          </span>
        </div>

        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose} className="p-1">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        )}
      </div>

      {/* Search */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <HistorySearch
          value={searchQuery}
          onChange={setSearchQuery}
          onSearch={handleSearch}
          onClear={clearSearch}
          disabled={isLoading}
        />
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {error && (
          <div className="p-4 text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-950/20">
            <div className="flex items-center gap-2">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}

        {isLoading && (
          <div className="p-8 text-center">
            <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2" />
            <span className="text-gray-500 dark:text-gray-400">
              加载历史记录...
            </span>
          </div>
        )}

        {!isLoading && !error && filteredEntries.length === 0 && (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <svg
              className="w-12 h-12 mx-auto mb-4 opacity-50"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <p className="text-lg mb-2">没有找到历史记录</p>
            <p className="text-sm">
              {searchQuery
                ? '尝试修改搜索条件'
                : '开始使用终端时会自动记录命令历史'}
            </p>
          </div>
        )}

        {!isLoading && !error && filteredEntries.length > 0 && (
          <div
            className="overflow-y-auto"
            style={{ maxHeight: `calc(${maxHeight} - 200px)` }}
          >
            <div className="p-2 space-y-1">
              {filteredEntries.map(entry => (
                <HistoryItem
                  key={entry.id}
                  entry={entry}
                  isSelected={selectedEntry?.id === entry.id}
                  onSelect={selectEntry}
                  onExecute={handleExecute}
                  onCopy={handleCopy}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer with tips */}
      {!isLoading && !error && filteredEntries.length > 0 && (
        <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-4">
              <span>💡 双击执行命令</span>
              <span>⌨️ ↑↓ 导航</span>
              <span>📋 点击复制按钮</span>
            </div>
            <span>Esc 关闭</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoryPanel;
