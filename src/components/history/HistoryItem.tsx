// History Item Component - Displays a single command history entry
// Provides command information, execution status, and interaction capabilities

import React from 'react';
import { historyUtils } from '../../stores/historyStore';
import { HistoryEntry } from '../../types/history';

interface HistoryItemProps {
  entry: HistoryEntry;
  isSelected?: boolean;
  onSelect?: (entry: HistoryEntry) => void;
  onExecute?: (entry: HistoryEntry) => void;
  onCopy?: (command: string) => void;
  showWorkingDirectory?: boolean;
  showTimestamp?: boolean;
  showDuration?: boolean;
}

const HistoryItem: React.FC<HistoryItemProps> = ({
  entry,
  isSelected = false,
  onSelect,
  onExecute,
  onCopy,
  showWorkingDirectory = true,
  showTimestamp = true,
  showDuration = true,
}) => {
  const command = historyUtils.formatCommand(entry);
  const timestamp = historyUtils.formatTimestamp(entry.timestamp);
  const duration = historyUtils.formatDuration(entry.duration);
  const exitStatus = historyUtils.getExitCodeStatus(entry.exit_code);
  const isSensitive = historyUtils.isSensitiveCommand(command);

  const handleClick = () => {
    onSelect?.(entry);
  };

  const handleDoubleClick = () => {
    onExecute?.(entry);
  };

  const handleCopyClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onCopy?.(command);
  };

  return (
    <div
      className={`
        group flex items-center gap-3 p-3 rounded-lg cursor-pointer
        transition-all duration-200 border
        ${
          isSelected
            ? 'bg-blue-50 dark:bg-blue-950/50 border-blue-200 dark:border-blue-800'
            : 'hover:bg-gray-50 dark:hover:bg-gray-800/50 border-transparent'
        }
        ${exitStatus === 'error' ? 'border-l-4 border-l-red-500' : ''}
        ${exitStatus === 'success' ? 'border-l-4 border-l-green-500' : ''}
      `}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      title="单击选择，双击执行"
    >
      {/* Status indicator */}
      <div className="flex-shrink-0">
        <div
          className={`
          w-2 h-2 rounded-full
          ${exitStatus === 'success' ? 'bg-green-500' : ''}
          ${exitStatus === 'error' ? 'bg-red-500' : ''}
          ${exitStatus === 'unknown' ? 'bg-gray-400' : ''}
        `}
        />
      </div>

      {/* Command content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <code
            className={`
            text-sm font-mono flex-1 truncate
            ${isSensitive ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-900 dark:text-gray-100'}
          `}
          >
            {isSensitive ? '*** 敏感命令 ***' : command}
          </code>

          {entry.exit_code !== null && (
            <span
              className={`
              text-xs px-2 py-1 rounded
              ${
                exitStatus === 'success'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              }
            `}
            >
              {entry.exit_code}
            </span>
          )}
        </div>

        {/* Metadata */}
        <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
          {showWorkingDirectory && (
            <span className="truncate max-w-48">
              📁 {entry.working_directory}
            </span>
          )}

          {showTimestamp && <span>🕒 {timestamp}</span>}

          {showDuration && <span>⏱️ {duration}</span>}

          {entry.output_size > 0 && <span>📄 {entry.output_size} bytes</span>}
        </div>
      </div>

      {/* Actions */}
      <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={handleCopyClick}
          className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          title="复制命令"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default HistoryItem;
