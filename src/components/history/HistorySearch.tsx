// History Search Component - Provides search input and filtering capabilities
// Supports real-time search with debouncing and keyboard shortcuts

import React, { useCallback, useEffect, useState } from 'react';
import { Input } from '../ui/Input';

interface HistorySearchProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (query: string) => void;
  onClear: () => void;
  placeholder?: string;
  disabled?: boolean;
  debounceMs?: number;
}

const HistorySearch: React.FC<HistorySearchProps> = ({
  value,
  onChange,
  onSearch,
  onClear,
  placeholder = '搜索命令历史... (Ctrl+R)',
  disabled = false,
  debounceMs = 300,
}) => {
  const [searchValue, setSearchValue] = useState(value);

  // Debounced search function
  const debouncedSearch = useCallback(
    (() => {
      let timeoutId: ReturnType<typeof setTimeout>;
      return (query: string) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          onSearch(query);
        }, debounceMs);
      };
    })(),
    [onSearch, debounceMs]
  );

  // Handle input change
  const handleChange = (newValue: string) => {
    setSearchValue(newValue);
    onChange(newValue);

    if (newValue.trim()) {
      debouncedSearch(newValue.trim());
    } else {
      onClear();
    }
  };

  // Handle clear
  const handleClear = () => {
    setSearchValue('');
    onChange('');
    onClear();
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClear();
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (searchValue.trim()) {
        onSearch(searchValue.trim());
      }
    }
  };

  // Sync with external value changes
  useEffect(() => {
    setSearchValue(value);
  }, [value]);

  // Global keyboard shortcut for Ctrl+R
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        // Focus search input
        const searchInput = document.querySelector(
          '[data-history-search]'
        ) as HTMLInputElement;
        searchInput?.focus();
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, []);

  return (
    <div className="relative">
      <div className="relative">
        {/* Search icon */}
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg
            className="h-4 w-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        {/* Input field */}
        <Input
          type="text"
          value={searchValue}
          onChange={e => handleChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className="pl-10 pr-10"
          data-history-search
        />

        {/* Clear button */}
        {searchValue && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              title="清除搜索"
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Search tips */}
      {searchValue && (
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-4">
            <span>💡 支持模糊搜索</span>
            <span>⌨️ Enter 确认搜索</span>
            <span>🚫 Esc 清除</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistorySearch;
