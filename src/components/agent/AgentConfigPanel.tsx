import { useAppStore } from '@/stores';
import { cn } from '@/utils/helpers';
import React, { useEffect, useRef, useState } from 'react';

interface AgentConfigPanelProps {
  isOpen: boolean;
  onClose: () => void;
  anchorRef: React.RefObject<HTMLElement>;
}

export const AgentConfigPanel: React.FC<AgentConfigPanelProps> = ({
  isOpen,
  onClose,
  anchorRef,
}) => {
  const { theme } = useAppStore();
  const panelRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: 0, right: 0 });

  // 计算面板位置
  useEffect(() => {
    if (isOpen && anchorRef.current) {
      const anchorRect = anchorRef.current.getBoundingClientRect();
      setPosition({
        top: anchorRect.bottom + 8,
        right: window.innerWidth - anchorRect.right,
      });
    }
  }, [isOpen, anchorRef]);

  // 点击外部关闭面板
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        panelRef.current &&
        !panelRef.current.contains(event.target as Node) &&
        anchorRef.current &&
        !anchorRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose, anchorRef]);

  // ESC键关闭面板
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      ref={panelRef}
      className={cn(
        'fixed z-50 w-80 rounded-lg border shadow-lg',
        theme === 'dark'
          ? 'bg-gray-800 border-gray-700'
          : 'bg-white border-gray-200'
      )}
      style={{
        top: position.top,
        right: position.right,
      }}
    >
      {/* 面板头部 */}
      <div
        className={cn(
          'px-4 py-3 border-b',
          theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
        )}
      >
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Agent 配置</h3>
          <button
            onClick={onClose}
            className={cn(
              'p-1 rounded-lg transition-colors',
              theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
            )}
          >
            <span className="text-lg">✕</span>
          </button>
        </div>
      </div>

      {/* 面板内容 */}
      <div className="p-4 space-y-4">
        {/* AI 模型选择 */}
        <div>
          <label
            className={cn(
              'block text-sm font-medium mb-2',
              theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            )}
          >
            AI 模型
          </label>
          <select
            className={cn(
              'w-full px-3 py-2 rounded-lg border text-sm',
              theme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            )}
            defaultValue="deepseek"
          >
            <option value="deepseek">DeepSeek Chat</option>
            <option value="openai">OpenAI GPT-4</option>
            <option value="claude">Claude 3</option>
            <option value="local">本地模型</option>
          </select>
        </div>

        {/* 快捷键设置 */}
        <div>
          <label
            className={cn(
              'block text-sm font-medium mb-2',
              theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            )}
          >
            快捷键
          </label>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">AI 对话</span>
              <kbd
                className={cn(
                  'px-2 py-1 text-xs rounded border',
                  theme === 'dark'
                    ? 'bg-gray-700 border-gray-600 text-gray-300'
                    : 'bg-gray-100 border-gray-300 text-gray-700'
                )}
              >
                Ctrl+Shift+C
              </kbd>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">命令转换</span>
              <kbd
                className={cn(
                  'px-2 py-1 text-xs rounded border',
                  theme === 'dark'
                    ? 'bg-gray-700 border-gray-600 text-gray-300'
                    : 'bg-gray-100 border-gray-300 text-gray-700'
                )}
              >
                @command
              </kbd>
            </div>
          </div>
        </div>

        {/* 终端偏好 */}
        <div>
          <label
            className={cn(
              'block text-sm font-medium mb-2',
              theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            )}
          >
            终端偏好
          </label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                defaultChecked
                className="mr-2 rounded"
              />
              <span className="text-sm">危险命令确认</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                defaultChecked
                className="mr-2 rounded"
              />
              <span className="text-sm">自动建议</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-2 rounded"
              />
              <span className="text-sm">命令历史记录</span>
            </label>
          </div>
        </div>

        {/* 状态信息 */}
        <div
          className={cn(
            'p-3 rounded-lg',
            theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'
          )}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">连接状态</span>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-green-500">已连接</span>
            </div>
          </div>
          <div className="text-xs text-gray-500">
            模型: DeepSeek Chat v2.5 | 延迟: 120ms
          </div>
        </div>
      </div>

      {/* 面板底部 */}
      <div
        className={cn(
          'px-4 py-3 border-t',
          theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
        )}
      >
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className={cn(
              'px-3 py-1.5 text-sm rounded-lg transition-colors',
              theme === 'dark'
                ? 'text-gray-300 hover:bg-gray-700'
                : 'text-gray-700 hover:bg-gray-100'
            )}
          >
            取消
          </button>
          <button
            className={cn(
              'px-3 py-1.5 text-sm rounded-lg transition-colors',
              'bg-blue-600 text-white hover:bg-blue-700'
            )}
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};
