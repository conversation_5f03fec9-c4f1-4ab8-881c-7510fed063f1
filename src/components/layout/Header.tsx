import { Button } from '@/components/ui';
import { useAppStore } from '@/stores';
import { cn } from '@/utils/helpers';
import { Maximize, Menu, Minimize, Settings, Terminal, X } from 'lucide-react';
import React from 'react';
import { ThemeToggle } from '../ui';

interface HeaderProps {
  className?: string;
}

export const Header: React.FC<HeaderProps> = ({ className }) => {
  const { toggleSidebar, currentView, setCurrentView } = useAppStore();

  return (
    <header
      className={cn(
        'flex items-center justify-between h-14 px-4 border-b border-border',
        'bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
        'sticky top-0 z-40',
        className
      )}
    >
      {/* Left section */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="md:hidden"
        >
          <Menu className="h-4 w-4" />
          <span className="sr-only">切换侧边栏</span>
        </Button>

        <div className="flex items-center space-x-2">
          <Terminal className="h-5 w-5 text-primary" />
          <h1 className="text-lg font-semibold text-foreground">TAgent</h1>
        </div>
      </div>

      {/* Center section - Navigation */}
      <nav className="hidden md:flex items-center space-x-1">
        <Button
          variant={currentView === 'terminal' ? 'secondary' : 'ghost'}
          size="sm"
          onClick={() => setCurrentView('terminal')}
        >
          <Terminal className="h-4 w-4 mr-2" />
          终端
        </Button>
        <Button
          variant={currentView === 'ai-chat' ? 'secondary' : 'ghost'}
          size="sm"
          onClick={() => setCurrentView('ai-chat')}
        >
          🤖
          <span className="ml-2">AI 助手</span>
        </Button>
        <Button
          variant={currentView === 'settings' ? 'secondary' : 'ghost'}
          size="sm"
          onClick={() => setCurrentView('settings')}
        >
          <Settings className="h-4 w-4 mr-2" />
          设置
        </Button>
      </nav>

      {/* Right section */}
      <div className="flex items-center space-x-2">
        {/* Theme toggle */}
        <ThemeToggle className="btn-ghost p-2" />

        {/* Window controls (for desktop app) */}
        <div className="hidden md:flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            title="最小化"
          >
            <Minimize className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            title="最大化"
          >
            <Maximize className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-destructive hover:text-destructive-foreground"
            title="关闭"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </header>
  );
};

Header.displayName = 'Header';
