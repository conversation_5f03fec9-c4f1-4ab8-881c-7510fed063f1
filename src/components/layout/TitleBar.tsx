import { TabBar } from '@/components/tabs';
import { useAppStore, useTerminalStore } from '@/stores';
import { cn } from '@/utils/helpers';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { Menu, Minus, Square, X } from 'lucide-react';
import React, { useCallback } from 'react';

interface TitleBarProps {
  className?: string;
}

export const TitleBar: React.FC<TitleBarProps> = ({ className }) => {
  const { theme, toggleSidebar } = useAppStore();
  const { tabs, activeTabId, createTab, closeTab, setActiveTab } = useTerminalStore();
  const appWindow = getCurrentWindow();

  // 窗口控制函数
  const handleMinimize = useCallback(async () => {
    try {
      await appWindow.minimize();
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  }, [appWindow]);

  const handleMaximize = useCallback(async () => {
    try {
      await appWindow.toggleMaximize();
    } catch (error) {
      console.error('Failed to toggle maximize window:', error);
    }
  }, [appWindow]);

  const handleClose = useCallback(async () => {
    try {
      await appWindow.close();
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  }, [appWindow]);

  // Tab管理函数
  const handleTabSelect = useCallback((tabId: string) => {
    setActiveTab(tabId);
  }, [setActiveTab]);

  const handleTabClose = useCallback((tabId: string) => {
    if (tabs.length > 1) {
      closeTab(tabId);
    }
  }, [tabs.length, closeTab]);

  const handleNewTab = useCallback(() => {
    const newTabId = createTab();
    setActiveTab(newTabId);
  }, [createTab, setActiveTab]);

  return (
    <div
      className={cn(
        'flex items-center h-8 select-none border-b min-w-0',
        theme === 'dark'
          ? 'bg-gray-900 border-gray-700'
          : 'bg-gray-100 border-gray-300',
        className
      )}
    >
      {/* 左侧：macOS风格的窗口控制按钮 */}
      <div className="flex items-center space-x-2 px-3 flex-shrink-0 min-w-0">
        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className={cn(
            'w-3 h-3 rounded-full transition-colors',
            'hover:bg-red-500 bg-red-400',
            'flex items-center justify-center group'
          )}
          title="关闭"
        >
          <X className="w-2 h-2 text-red-800 opacity-0 group-hover:opacity-100 transition-opacity" />
        </button>

        {/* 最小化按钮 */}
        <button
          onClick={handleMinimize}
          className={cn(
            'w-3 h-3 rounded-full transition-colors',
            'hover:bg-yellow-500 bg-yellow-400',
            'flex items-center justify-center group'
          )}
          title="最小化"
        >
          <Minus className="w-2 h-2 text-yellow-800 opacity-0 group-hover:opacity-100 transition-opacity" />
        </button>

        {/* 最大化按钮 */}
        <button
          onClick={handleMaximize}
          className={cn(
            'w-3 h-3 rounded-full transition-colors',
            'hover:bg-green-500 bg-green-400',
            'flex items-center justify-center group'
          )}
          title="最大化"
        >
          <Square className="w-2 h-2 text-green-800 opacity-0 group-hover:opacity-100 transition-opacity" />
        </button>

        {/* 侧边栏切换按钮 */}
        <div className="ml-2 flex-shrink-0">
          <button
            onClick={toggleSidebar}
            className={cn(
              'p-1 rounded transition-colors',
              theme === 'dark'
                ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200'
                : 'hover:bg-gray-200 text-gray-600 hover:text-gray-800'
            )}
            title="切换侧边栏"
          >
            <Menu className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* 中间：终端Tab页 - 占据剩余空间，确保不会溢出 */}
      <div className="flex-1 min-w-0 flex items-center overflow-hidden">
        <div className="flex-1 h-full min-w-0">
          <TabBar
            tabs={tabs}
            activeTabId={activeTabId || ''}
            onTabSelect={handleTabSelect}
            onTabClose={handleTabClose}
            onNewTab={handleNewTab}
            showBorder={false}
          />
        </div>
      </div>

      {/* 右侧：可拖拽区域 */}
      <div
        data-tauri-drag-region
        className="flex-shrink-0 w-16 h-full min-w-0"
        title="拖拽窗口"
      />
    </div>
  );
};

TitleBar.displayName = 'TitleBar';
