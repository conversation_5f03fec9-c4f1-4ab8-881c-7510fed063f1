import { AgentConfigPanel } from '@/components/agent';
import { useAppStore } from '@/stores';
import { cn } from '@/utils/helpers';
import { X } from 'lucide-react';
import React, { useRef, useState } from 'react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  onClose,
  className,
}) => {
  const { theme, setTheme } = useAppStore();
  const [isAgentConfigOpen, setIsAgentConfigOpen] = useState(false);
  const agentButtonRef = useRef<HTMLButtonElement>(null);

  const handleThemeToggle = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const handleAgentConfigToggle = () => {
    setIsAgentConfigOpen(!isAgentConfigOpen);
  };

  // 如果侧边栏未打开，不渲染任何内容
  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* 遮罩层 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />

      {/* 侧边栏内容 */}
      <div
        className={cn(
          'fixed left-0 top-8 bottom-0 w-64 z-50',
          'transform transition-transform duration-300 ease-in-out',
          theme === 'dark'
            ? 'bg-gray-800 border-gray-700'
            : 'bg-white border-gray-200',
          'border-r shadow-lg',
          className
        )}
      >
        {/* 侧边栏头部 */}
        <div className="p-4 border-b border-inherit">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold">TAgent</h1>
              <p
                className={cn(
                  'text-sm',
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                )}
              >
                AI 增强终端
              </p>
            </div>

            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className={cn(
                'p-1 rounded-lg transition-colors',
                theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
              )}
              title="关闭侧边栏"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* 侧边栏内容 */}
        <div className="flex-1 p-4 space-y-4">
          {/* 功能按钮区域 */}
          <div className="space-y-2">
            {/* Agent配置按钮 */}
            <button
              ref={agentButtonRef}
              onClick={handleAgentConfigToggle}
              className={cn(
                'w-full p-3 rounded-lg transition-colors text-left',
                'flex items-center space-x-3',
                theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100',
                isAgentConfigOpen && (theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100')
              )}
              title="Agent 配置"
            >
              <span className="text-lg">🤖</span>
              <div>
                <div className="font-medium">Agent 配置</div>
                <div
                  className={cn(
                    'text-sm',
                    theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                  )}
                >
                  配置AI助手设置
                </div>
              </div>
            </button>

            {/* 主题切换按钮 */}
            <button
              onClick={handleThemeToggle}
              className={cn(
                'w-full p-3 rounded-lg transition-colors text-left',
                'flex items-center space-x-3',
                theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
              )}
              title={`切换到${theme === 'dark' ? '亮色' : '暗色'}主题`}
            >
              <span className="text-lg">{theme === 'dark' ? '☀️' : '🌙'}</span>
              <div>
                <div className="font-medium">主题切换</div>
                <div
                  className={cn(
                    'text-sm',
                    theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                  )}
                >
                  {theme === 'dark' ? '切换到亮色主题' : '切换到暗色主题'}
                </div>
              </div>
            </button>
          </div>

          {/* 其他功能区域可以在这里添加 */}
          <div className="pt-4 border-t border-inherit">
            <div
              className={cn(
                'text-sm',
                theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
              )}
            >
              更多功能即将推出...
            </div>
          </div>
        </div>

      </div>

      {/* Agent配置面板 */}
      <AgentConfigPanel
        isOpen={isAgentConfigOpen}
        onClose={() => setIsAgentConfigOpen(false)}
        anchorRef={agentButtonRef}
      />
    </>
  );
};

Sidebar.displayName = 'Sidebar';
