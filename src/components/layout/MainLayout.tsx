import { useAppStore } from '@/stores';
import { cn } from '@/utils/helpers';
import React from 'react';
import { Sidebar } from './Sidebar';
import { TitleBar } from './TitleBar';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  className = '',
}) => {
  const { theme, sidebarVisible, setSidebarVisible } = useAppStore();

  return (
    <div
      className={cn(
        'flex flex-col h-screen overflow-hidden',
        theme === 'dark'
          ? 'bg-gray-900 text-white'
          : 'bg-gray-100 text-gray-900',
        className
      )}
    >
      {/* 顶部标题栏 */}
      <TitleBar />

      {/* 主内容区域 */}
      <main className="flex-1 overflow-hidden relative">
        <div
          className={cn(
            'h-full w-full',
            theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'
          )}
        >
          {children}
        </div>
      </main>

      {/* 侧边栏 */}
      <Sidebar
        isOpen={sidebarVisible}
        onClose={() => setSidebarVisible(false)}
      />
    </div>
  );
};

MainLayout.displayName = 'MainLayout';
