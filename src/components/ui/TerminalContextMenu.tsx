import { useContextMenuStore } from '@/stores/contextMenuStore';
import { useTerminalStore } from '@/stores/terminalStore';
import { invoke } from '@tauri-apps/api/core';
import { Clipboard, Copy, Square, Trash2 } from 'lucide-react';
import React from 'react';
import { ContextMenu, MenuItem } from './ContextMenu';

interface TerminalContextMenuProps {
  position: { x: number; y: number };
  visible: boolean;
  onClose: () => void;
  hasSelection: boolean;
}

export const TerminalContextMenu: React.FC<TerminalContextMenuProps> = ({
  position,
  visible,
  onClose,
  hasSelection,
}) => {
  const { getActiveTab } = useTerminalStore();
  const { canPaste } = useContextMenuStore();

  // 复制选中文本
  const handleCopy = async () => {
    if (!hasSelection) return;

    try {
      // TODO: 获取实际选中的文本，这里暂时使用模拟数据
      const selectedText = window.getSelection()?.toString() || '';
      if (selectedText) {
        await invoke('write_clipboard', { text: selectedText });
        console.log('文本已复制到剪贴板');
      }
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 粘贴文本
  const handlePaste = async () => {
    try {
      const clipboardText = await invoke<string>('read_clipboard');
      if (clipboardText) {
        // TODO: 将文本发送到终端
        console.log('粘贴文本:', clipboardText);
        // 这里应该调用终端的输入方法
        // await invoke('write_to_terminal', { text: clipboardText });
      }
    } catch (error) {
      console.error('粘贴失败:', error);
    }
  };

  // 全选终端内容
  const handleSelectAll = () => {
    try {
      // TODO: 实现终端内容全选
      const terminalElement = document.querySelector('.terminal-content');
      if (terminalElement) {
        const range = document.createRange();
        range.selectNodeContents(terminalElement);
        const selection = window.getSelection();
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
    } catch (error) {
      console.error('全选失败:', error);
    }
  };

  // 清空终端
  const handleClear = async () => {
    try {
      const activeTab = getActiveTab();
      if (activeTab) {
        // TODO: 实现清空终端的具体逻辑
        console.log('清空终端:', activeTab.id);
        // 这里应该调用清空终端的命令
        // await invoke('clear_terminal', { terminalId: activeTab.id });
      }
    } catch (error) {
      console.error('清空终端失败:', error);
    }
  };

  const menuItems: MenuItem[] = [
    {
      id: 'copy',
      label: '复制',
      icon: Copy,
      shortcut: '⌘C',
      action: handleCopy,
      disabled: !hasSelection,
    },
    {
      id: 'paste',
      label: '粘贴',
      icon: Clipboard,
      shortcut: '⌘V',
      action: handlePaste,
      disabled: !canPaste,
    },
    {
      id: 'separator1',
      label: '',
      action: () => {},
      separator: true,
    },
    {
      id: 'selectAll',
      label: '全选',
      icon: Square,
      shortcut: '⌘A',
      action: handleSelectAll,
    },
    {
      id: 'separator2',
      label: '',
      action: () => {},
      separator: true,
    },
    {
      id: 'clear',
      label: '清空终端',
      icon: Trash2,
      shortcut: '⌘K',
      action: handleClear,
    },
  ];

  return (
    <ContextMenu
      items={menuItems}
      position={position}
      visible={visible}
      onClose={onClose}
    />
  );
};
