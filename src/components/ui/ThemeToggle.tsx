import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

interface ThemeToggleProps {
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { currentTheme, availableThemes, setTheme } = useThemeStore();

  const toggleTheme = () => {
    const currentThemeType = currentTheme.type;

    if (currentThemeType === 'dark') {
      const lightTheme = availableThemes.find(t => t.type === 'light');
      if (lightTheme) setTheme(lightTheme.id);
    } else {
      const darkTheme = availableThemes.find(t => t.type === 'dark');
      if (darkTheme) setTheme(darkTheme.id);
    }
  };

  const isDark = currentTheme.type === 'dark';

  return (
    <button
      onClick={toggleTheme}
      className={`theme-toggle-btn ${className}`}
      title={`切换到${isDark ? '亮色' : '暗色'}主题 (Cmd/Ctrl+Shift+T)`}
      aria-label="切换主题"
    >
      <div className="theme-toggle-icon">
        {isDark ? (
          // 太阳图标 (切换到亮色主题)
          <svg
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="w-5 h-5"
          >
            <circle cx="12" cy="12" r="5" />
            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
          </svg>
        ) : (
          // 月亮图标 (切换到暗色主题)
          <svg
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="w-5 h-5"
          >
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
          </svg>
        )}
      </div>
    </button>
  );
};

export default ThemeToggle;
