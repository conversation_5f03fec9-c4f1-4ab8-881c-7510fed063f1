import { useTerminalStore } from '@/stores/terminalStore';
import { ArrowR<PERSON>, Copy, Edit3, X, XCircle } from 'lucide-react';
import React from 'react';
import { ContextMenu, MenuItem } from './ContextMenu';

interface TabContextMenuProps {
  position: { x: number; y: number };
  visible: boolean;
  onClose: () => void;
  tabId: string;
  tabIndex: number;
  tabCount: number;
}

export const TabContextMenu: React.FC<TabContextMenuProps> = ({
  position,
  visible,
  onClose,
  tabId,
  tabIndex,
  tabCount,
}) => {
  const { createTab, closeTab, updateTab, getTabById } = useTerminalStore();

  // 重命名Tab
  const handleRename = () => {
    const tab = getTabById(tabId);
    if (!tab) return;

    const newTitle = prompt('请输入新的Tab名称:', tab.title);
    if (newTitle && newTitle.trim()) {
      updateTab(tabId, { title: newTitle.trim() });
    }
  };

  // 复制Tab
  const handleDuplicate = () => {
    const tab = getTabById(tabId);
    if (!tab) return;

    createTab(tab.shell, tab.currentDirectory);
  };

  // 关闭Tab
  const handleClose = () => {
    if (tabCount > 1) {
      closeTab(tabId);
    }
  };

  // 关闭其他Tab
  const handleCloseOthers = () => {
    // TODO: 实现关闭其他Tab的逻辑
    console.log('关闭其他Tab:', tabId);
  };

  // 关闭右侧Tab
  const handleCloseToRight = () => {
    // TODO: 实现关闭右侧Tab的逻辑
    console.log('关闭右侧Tab:', tabIndex);
  };

  const menuItems: MenuItem[] = [
    {
      id: 'rename',
      label: '重命名Tab',
      icon: Edit3,
      action: handleRename,
    },
    {
      id: 'duplicate',
      label: '复制Tab',
      icon: Copy,
      action: handleDuplicate,
    },
    {
      id: 'separator1',
      label: '',
      action: () => {},
      separator: true,
    },
    {
      id: 'close',
      label: '关闭Tab',
      icon: X,
      shortcut: '⌘W',
      action: handleClose,
      disabled: tabCount <= 1,
    },
    {
      id: 'closeOthers',
      label: '关闭其他Tab',
      icon: XCircle,
      action: handleCloseOthers,
      disabled: tabCount <= 1,
    },
    {
      id: 'closeToRight',
      label: '关闭右侧Tab',
      icon: ArrowRight,
      action: handleCloseToRight,
      disabled: tabIndex >= tabCount - 1,
    },
  ];

  return (
    <ContextMenu
      items={menuItems}
      position={position}
      visible={visible}
      onClose={onClose}
    />
  );
};
