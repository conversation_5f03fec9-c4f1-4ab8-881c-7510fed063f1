import React from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { ThemeSelector } from './ThemeSelector';

export const ThemeDemo: React.FC = () => {
  const { currentTheme } = useThemeStore();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-primary mb-2">主题系统演示</h1>
        <p className="text-secondary">
          当前主题:{' '}
          <span className="text-primary font-medium">{currentTheme.name}</span>
        </p>
      </div>

      {/* 主题选择器 */}
      <div className="surface p-6 mb-6">
        <ThemeSelector />
      </div>

      {/* 颜色演示 */}
      <div className="surface p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">颜色系统</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary rounded-md mx-auto mb-2"></div>
            <span className="text-sm">Primary</span>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-surface rounded-md mx-auto mb-2 border border-border"></div>
            <span className="text-sm">Surface</span>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-success rounded-md mx-auto mb-2"></div>
            <span className="text-sm text-success">Success</span>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-error rounded-md mx-auto mb-2"></div>
            <span className="text-sm text-error">Error</span>
          </div>
        </div>
      </div>

      {/* 终端颜色演示 */}
      <div className="terminal surface p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">终端颜色</h2>
        <div className="font-mono text-sm space-y-1">
          <div>
            $ <span className="text-primary">echo</span>{' '}
            <span className="ansi-2">"Hello World"</span>
          </div>
          <div className="ansi-2">Hello World</div>
          <div>
            $ <span className="text-primary">ls</span>{' '}
            <span className="ansi-4">-la</span>
          </div>
          <div>
            <span className="ansi-4">drwxr-xr-x</span>{' '}
            <span className="ansi-6">user</span>{' '}
            <span className="ansi-6">group</span>{' '}
            <span className="ansi-3">1024</span>{' '}
            <span className="ansi-5">Dec 10 10:30</span>{' '}
            <span className="ansi-1">file.txt</span>
          </div>
          <div>
            $ <span className="terminal-cursor">_</span>
          </div>
        </div>
      </div>

      {/* 按钮演示 */}
      <div className="surface p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">按钮组件</h2>
        <div className="flex flex-wrap gap-4">
          <button className="btn">Primary Button</button>
          <button className="btn-secondary">Secondary Button</button>
          <button className="btn-ghost">Ghost Button</button>
          <button className="btn" disabled>
            Disabled Button
          </button>
        </div>
      </div>

      {/* 输入框演示 */}
      <div className="surface p-6">
        <h2 className="text-lg font-semibold mb-4">输入组件</h2>
        <div className="space-y-4">
          <input
            type="text"
            className="input w-full"
            placeholder="请输入文本..."
          />
          <div className="flex gap-2">
            <input
              type="text"
              className="input flex-1"
              value="聚焦状态"
              readOnly
            />
            <button className="btn">提交</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeDemo;
