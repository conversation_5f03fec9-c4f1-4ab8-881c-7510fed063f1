import React, { useEffect } from 'react';
import { useThemeStore } from '../../stores/themeStore';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { currentTheme, detectSystemTheme, autoSwitchEnabled } =
    useThemeStore();

  useEffect(() => {
    // 初始化系统主题检测
    detectSystemTheme();

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = () => {
      if (autoSwitchEnabled) {
        detectSystemTheme();
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    // 监听快捷键切换主题 (Cmd/Ctrl + Shift + T)
    const handleKeydown = (event: KeyboardEvent) => {
      if (
        (event.metaKey || event.ctrlKey) &&
        event.shiftKey &&
        event.key === 'T'
      ) {
        event.preventDefault();

        // 在暗色和亮色主题之间快速切换
        const { availableThemes, setTheme } = useThemeStore.getState();
        const currentThemeType = currentTheme.type;

        if (currentThemeType === 'dark') {
          const lightTheme = availableThemes.find(t => t.type === 'light');
          if (lightTheme) setTheme(lightTheme.id);
        } else {
          const darkTheme = availableThemes.find(t => t.type === 'dark');
          if (darkTheme) setTheme(darkTheme.id);
        }
      }
    };

    document.addEventListener('keydown', handleKeydown);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
      document.removeEventListener('keydown', handleKeydown);
    };
  }, [detectSystemTheme, autoSwitchEnabled, currentTheme]);

  return (
    <div
      className={`theme-container theme-${currentTheme.id}`}
      data-theme={currentTheme.type}
    >
      {children}
    </div>
  );
};
