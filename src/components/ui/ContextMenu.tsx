import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

export interface MenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  action: () => void;
  disabled?: boolean;
  separator?: boolean;
}

export interface MenuPosition {
  x: number;
  y: number;
}

export interface ContextMenuProps {
  items: MenuItem[];
  position: MenuPosition;
  visible: boolean;
  onClose: () => void;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  items,
  position,
  visible,
  onClose,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [visible, onClose]);

  // 智能定位：确保菜单不超出屏幕边界
  const getAdjustedPosition = () => {
    if (!menuRef.current) return position;

    const rect = menuRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let { x, y } = position;

    // 右边界检查
    if (x + rect.width > viewportWidth) {
      x = viewportWidth - rect.width - 10;
    }

    // 下边界检查
    if (y + rect.height > viewportHeight) {
      y = viewportHeight - rect.height - 10;
    }

    // 左边界检查
    if (x < 0) {
      x = 10;
    }

    // 上边界检查
    if (y < 0) {
      y = 10;
    }

    return { x, y };
  };

  if (!visible) return null;

  const adjustedPosition = getAdjustedPosition();

  return createPortal(
    <div
      ref={menuRef}
      className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 min-w-48 animate-in fade-in duration-150"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
      }}
    >
      {items.map(item => (
        <div key={item.id}>
          {item.separator ? (
            <div className="h-px bg-gray-200 dark:bg-gray-700 my-1 mx-2" />
          ) : (
            <button
              className={`
                w-full px-3 py-2 text-left text-sm flex items-center justify-between
                hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors
                ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                focus:bg-gray-100 dark:focus:bg-gray-700 focus:outline-none
              `}
              onClick={() => {
                if (!item.disabled) {
                  item.action();
                  onClose();
                }
              }}
              disabled={item.disabled}
            >
              <span className="flex items-center gap-2">
                {item.icon && <item.icon className="w-4 h-4" />}
                <span className="text-gray-900 dark:text-gray-100">
                  {item.label}
                </span>
              </span>
              {item.shortcut && (
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">
                  {item.shortcut}
                </span>
              )}
            </button>
          )}
        </div>
      ))}
    </div>,
    document.body
  );
};
