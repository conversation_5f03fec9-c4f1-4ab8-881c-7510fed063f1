import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新state以显示错误UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('[ErrorBoundary] 捕获到错误:', error);
    console.error('[ErrorBoundary] 错误信息:', errorInfo);

    this.setState({
      error,
      errorInfo,
    });

    // 特殊处理 transformCallback 错误 - 这通常是 Tauri API 初始化问题
    if (error.message.includes('transformCallback')) {
      console.error('[ErrorBoundary] 检测到 Tauri API 初始化错误');
      console.log('[ErrorBoundary] 将在3秒后自动重新加载应用');

      // 延迟重新加载，给用户时间看到错误信息
      setTimeout(() => {
        console.log('[ErrorBoundary] 自动重新加载应用...');
        window.location.reload();
      }, 3000);
    }

    // 这里可以上报错误到监控服务
    // reportError(error, errorInfo);
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 自定义错误UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const isTransformCallbackError =
        this.state.error?.message?.includes('transformCallback');

      return (
        <div
          style={{
            padding: '20px',
            margin: '20px',
            border: isTransformCallbackError
              ? '1px solid #ffa502'
              : '1px solid #ff6b6b',
            borderRadius: '8px',
            backgroundColor: isTransformCallbackError ? '#fff8e1' : '#fff5f5',
            color: isTransformCallbackError ? '#e65100' : '#c92a2a',
            fontFamily: 'Monaco, "Lucida Console", monospace',
          }}
        >
          <h2>
            {isTransformCallbackError
              ? '🔄 应用正在重新启动...'
              : '❌ 应用出现错误'}
          </h2>

          {isTransformCallbackError && (
            <div
              style={{
                marginBottom: '15px',
                padding: '10px',
                backgroundColor: '#fff3c4',
                borderRadius: '4px',
                border: '1px solid #ffc107',
              }}
            >
              <p style={{ margin: '0 0 8px 0', fontWeight: 'bold' }}>
                检测到 Tauri API 初始化问题
              </p>
              <p style={{ margin: '0', fontSize: '14px' }}>
                这通常是由于应用启动时序问题引起的。应用将在 3
                秒后自动重新加载。
              </p>
            </div>
          )}
          <details style={{ whiteSpace: 'pre-wrap', marginTop: '10px' }}>
            <summary>错误详情</summary>
            <p>
              <strong>错误:</strong> {this.state.error?.message}
            </p>
            <p>
              <strong>堆栈:</strong>
            </p>
            <pre style={{ fontSize: '12px', overflow: 'auto' }}>
              {this.state.error?.stack}
            </pre>
            {this.state.errorInfo && (
              <>
                <p>
                  <strong>组件堆栈:</strong>
                </p>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {this.state.errorInfo.componentStack}
                </pre>
              </>
            )}
          </details>
          <div style={{ marginTop: '15px' }}>
            <button
              onClick={this.handleReset}
              style={{
                padding: '8px 16px',
                marginRight: '10px',
                backgroundColor: '#228be6',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              重试
            </button>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '8px 16px',
                backgroundColor: '#fd7e14',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              刷新页面
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
