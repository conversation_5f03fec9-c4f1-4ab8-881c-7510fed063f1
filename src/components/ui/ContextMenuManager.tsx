import { useContextMenuStore } from '@/stores/contextMenuStore';
import React from 'react';
import { TabContextMenu } from './TabContextMenu';
import { TerminalContextMenu } from './TerminalContextMenu';

export const ContextMenuManager: React.FC = () => {
  const { visible, position, menuType, contextData, hideMenu, hasSelection } =
    useContextMenuStore();

  if (!visible || !menuType) {
    return null;
  }

  const handleClose = () => {
    hideMenu();
  };

  // 根据菜单类型渲染对应的右键菜单
  switch (menuType) {
    case 'terminal':
      return (
        <TerminalContextMenu
          position={position}
          visible={visible}
          onClose={handleClose}
          hasSelection={hasSelection}
        />
      );

    case 'tab':
      return (
        <TabContextMenu
          position={position}
          visible={visible}
          onClose={handleClose}
          tabId={contextData?.tabId || ''}
          tabIndex={contextData?.tabIndex || 0}
          tabCount={contextData?.tabCount || 1}
        />
      );

    default:
      return null;
  }
};
