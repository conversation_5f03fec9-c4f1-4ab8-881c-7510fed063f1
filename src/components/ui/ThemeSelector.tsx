import React from 'react';
import { useThemeSelector } from '../../stores/themeStore';

export const ThemeSelector: React.FC = () => {
  const {
    currentTheme,
    availableThemes,
    setTheme,
    autoSwitchEnabled,
    setAutoSwitch,
  } = useThemeSelector();

  return (
    <div className="theme-selector">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-primary mb-2">主题设置</h3>
        <p className="text-secondary text-sm">
          选择你喜欢的界面主题，或设置跟随系统主题自动切换
        </p>
      </div>

      <div className="theme-options">
        {availableThemes.map(theme => (
          <button
            key={theme.id}
            onClick={() => setTheme(theme.id)}
            className={`theme-option ${currentTheme.id === theme.id ? 'active' : ''}`}
            title={`切换到${theme.name}`}
          >
            <div
              className="theme-preview"
              style={{ backgroundColor: theme.colors.background }}
            >
              <div
                className="preview-terminal"
                style={{
                  backgroundColor: theme.colors.terminal.background,
                  color: theme.colors.terminal.foreground,
                }}
              >
                <span>$ echo "Hello"</span>
                <div
                  style={{
                    width: '6px',
                    height: '12px',
                    backgroundColor: theme.colors.terminal.cursor,
                    display: 'inline-block',
                    marginLeft: '2px',
                  }}
                />
              </div>
            </div>
            <span className="text-sm font-medium">{theme.name}</span>
          </button>
        ))}
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-border">
        <label className="auto-switch-toggle flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={autoSwitchEnabled}
            onChange={e => setAutoSwitch(e.target.checked)}
            className="theme-checkbox"
          />
          <span className="text-sm">跟随系统主题</span>
        </label>

        <div className="text-xs text-secondary">
          快捷键: Cmd/Ctrl + Shift + T
        </div>
      </div>

      {autoSwitchEnabled && (
        <div className="mt-3 p-3 bg-surface rounded-md border border-border">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-info rounded-full animate-pulse" />
            <span className="text-sm text-info">
              已启用自动切换，将根据系统设置自动调整主题
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThemeSelector;
