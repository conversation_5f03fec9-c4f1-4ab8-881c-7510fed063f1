import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { Button } from '../Button';

describe('Button组件', () => {
  describe('基础渲染', () => {
    it('应该正确渲染按钮文本', () => {
      render(<Button>测试按钮</Button>);
      expect(
        screen.getByRole('button', { name: '测试按钮' })
      ).toBeInTheDocument();
    });

    it('应该传递自定义className', () => {
      render(<Button className="custom-class">按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });

    it('应该传递HTML button属性', () => {
      render(
        <Button type="submit" id="test-button">
          提交
        </Button>
      );
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('type', 'submit');
      expect(button).toHaveAttribute('id', 'test-button');
    });
  });

  describe('变体样式', () => {
    it('应该应用默认变体样式', () => {
      render(<Button>默认按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-primary', 'text-primary-foreground');
    });

    it('应该应用destructive变体样式', () => {
      render(<Button variant="destructive">删除</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'bg-destructive',
        'text-destructive-foreground'
      );
    });

    it('应该应用outline变体样式', () => {
      render(<Button variant="outline">轮廓按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('border', 'border-input', 'bg-background');
    });

    it('应该应用secondary变体样式', () => {
      render(<Button variant="secondary">次要按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-secondary', 'text-secondary-foreground');
    });

    it('应该应用ghost变体样式', () => {
      render(<Button variant="ghost">幽灵按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'hover:bg-accent',
        'hover:text-accent-foreground'
      );
    });

    it('应该应用link变体样式', () => {
      render(<Button variant="link">链接按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'text-primary',
        'underline-offset-4',
        'hover:underline'
      );
    });
  });

  describe('尺寸样式', () => {
    it('应该应用默认尺寸样式', () => {
      render(<Button>默认尺寸</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-10', 'px-4', 'py-2');
    });

    it('应该应用小尺寸样式', () => {
      render(<Button size="sm">小按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-9', 'px-3');
    });

    it('应该应用大尺寸样式', () => {
      render(<Button size="lg">大按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-11', 'px-8');
    });

    it('应该应用图标尺寸样式', () => {
      render(<Button size="icon">🔍</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-10', 'w-10', 'p-0');
    });
  });

  describe('禁用状态', () => {
    it('应该在disabled为true时禁用按钮', () => {
      render(<Button disabled>禁用按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass(
        'disabled:opacity-50',
        'disabled:pointer-events-none'
      );
    });

    it('禁用的按钮不应该响应点击事件', () => {
      const handleClick = vi.fn();
      render(
        <Button disabled onClick={handleClick}>
          禁用按钮
        </Button>
      );
      const button = screen.getByRole('button');

      fireEvent.click(button);
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('加载状态', () => {
    it('应该在loading为true时显示加载图标', () => {
      render(<Button loading>加载中</Button>);
      const button = screen.getByRole('button');

      // 检查是否有加载图标
      const loadingIcon = button.querySelector('svg');
      expect(loadingIcon).toBeInTheDocument();
      expect(loadingIcon).toHaveClass('animate-spin');
    });

    it('加载状态下按钮应该被禁用', () => {
      render(<Button loading>加载中</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('图标按钮加载时应该没有margin', () => {
      render(
        <Button loading size="icon">
          🔍
        </Button>
      );
      const button = screen.getByRole('button');
      const loadingIcon = button.querySelector('svg');

      expect(loadingIcon).toHaveClass('h-4', 'w-4');
      expect(loadingIcon).not.toHaveClass('mr-2');
    });

    it('非图标按钮加载时应该有margin', () => {
      render(<Button loading>加载中</Button>);
      const button = screen.getByRole('button');
      const loadingIcon = button.querySelector('svg');

      expect(loadingIcon).toHaveClass('mr-2', 'h-4', 'w-4');
    });

    it('loading和disabled同时为true时仍应该禁用', () => {
      const handleClick = vi.fn();
      render(
        <Button loading disabled onClick={handleClick}>
          加载禁用
        </Button>
      );
      const button = screen.getByRole('button');

      expect(button).toBeDisabled();
      fireEvent.click(button);
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('事件处理', () => {
    it('应该正确触发onClick事件', () => {
      const handleClick = vi.fn();
      render(<Button onClick={handleClick}>点击按钮</Button>);
      const button = screen.getByRole('button');

      fireEvent.click(button);
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('应该正确触发onMouseEnter事件', () => {
      const handleMouseEnter = vi.fn();
      render(<Button onMouseEnter={handleMouseEnter}>悬停按钮</Button>);
      const button = screen.getByRole('button');

      fireEvent.mouseEnter(button);
      expect(handleMouseEnter).toHaveBeenCalledTimes(1);
    });

    it('应该正确触发onFocus事件', () => {
      const handleFocus = vi.fn();
      render(<Button onFocus={handleFocus}>聚焦按钮</Button>);
      const button = screen.getByRole('button');

      fireEvent.focus(button);
      expect(handleFocus).toHaveBeenCalledTimes(1);
    });
  });

  describe('复杂内容', () => {
    it('应该渲染包含JSX的复杂内容', () => {
      render(
        <Button>
          <span>图标</span>
          <span>文本</span>
        </Button>
      );

      expect(screen.getByText('图标')).toBeInTheDocument();
      expect(screen.getByText('文本')).toBeInTheDocument();
    });

    it('应该与加载状态一起正确渲染复杂内容', () => {
      render(
        <Button loading>
          <span>保存</span>
          <span>文档</span>
        </Button>
      );

      const button = screen.getByRole('button');
      const loadingIcon = button.querySelector('svg');

      expect(loadingIcon).toBeInTheDocument();
      expect(screen.getByText('保存')).toBeInTheDocument();
      expect(screen.getByText('文档')).toBeInTheDocument();
    });
  });

  describe('可访问性', () => {
    it('应该有正确的默认角色', () => {
      render(<Button>可访问按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('应该支持aria-label', () => {
      render(<Button aria-label="关闭对话框">×</Button>);
      const button = screen.getByRole('button', { name: '关闭对话框' });
      expect(button).toBeInTheDocument();
    });

    it('应该支持aria-describedby', () => {
      render(
        <>
          <Button aria-describedby="help-text">帮助</Button>
          <div id="help-text">这是帮助信息</div>
        </>
      );
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-describedby', 'help-text');
    });

    it('应该有正确的焦点样式类', () => {
      render(<Button>聚焦测试</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'focus-visible:outline-none',
        'focus-visible:ring-2'
      );
    });
  });

  describe('displayName', () => {
    it('应该有正确的displayName', () => {
      expect(Button.displayName).toBe('Button');
    });
  });
});
