import { X } from 'lucide-react';
import React, { useEffect, useRef } from 'react';
import { useChatStore } from '../../stores/chatStore';
import { Button } from '../ui/Button';
import { ChatInput } from './ChatInput';
import { ChatMessage } from './ChatMessage';
import type { ChatPanelProps } from './types';

export const ChatPanel: React.FC<ChatPanelProps> = ({
  isVisible,
  onClose,
  onCommandSelect,
}) => {
  const { messages, isLoading, sendMessage, clearHistory, isActive } =
    useChatStore();

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage(message);
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  const handleCommandClick = (command: string) => {
    onCommandSelect(command);
    // 可选：关闭聊天面板
    // onClose();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl w-full max-w-4xl h-3/4 flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              🤖 AI助手
            </h2>
            {isActive && (
              <span className="text-sm text-green-600 dark:text-green-400">
                对话模式已激活
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={clearHistory}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              清空
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
              <div className="text-4xl mb-2">💬</div>
              <p>有什么可以帮助您的吗？</p>
              <p className="text-sm mt-2">
                您可以询问技术问题、请求命令建议或寻求编程帮助
              </p>
            </div>
          ) : (
            messages.map(message => (
              <ChatMessage
                key={message.id}
                message={message}
                onCommandClick={handleCommandClick}
              />
            ))
          )}

          {isLoading && (
            <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span>AI正在思考...</span>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4">
          <ChatInput
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            placeholder="输入消息...（输入 'exit' 退出对话模式）"
          />
        </div>
      </div>
    </div>
  );
};
