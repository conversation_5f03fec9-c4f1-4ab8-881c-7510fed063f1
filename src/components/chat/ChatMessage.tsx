import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Play } from 'lucide-react';
import React from 'react';
import { Button } from '../ui/Button';
import type { ChatMessageProps } from './types';

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onCommandClick,
}) => {
  const isUser = message.type === 'user';
  const timestamp = new Date(message.timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  });

  // 解析消息中的命令建议
  const parseCommandSuggestions = (content: string) => {
    const commandRegex = /```(?:bash|shell|cmd)?\n(.*?)\n```/gs;
    const commands = [];
    let match;

    while ((match = commandRegex.exec(content)) !== null) {
      commands.push(match[1].trim());
    }

    return commands;
  };

  const commands = parseCommandSuggestions(message.content);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getRiskLevelColor = (riskLevel?: string) => {
    switch (riskLevel) {
      case 'High':
      case 'Critical':
        return 'text-red-600 dark:text-red-400';
      case 'Medium':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return 'text-green-600 dark:text-green-400';
    }
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-3xl ${isUser ? 'order-2' : 'order-1'}`}>
        {/* 用户头像和时间 */}
        <div
          className={`flex items-center mb-1 ${isUser ? 'justify-end' : 'justify-start'}`}
        >
          <span className="text-sm text-gray-500 dark:text-gray-400 mx-2">
            {isUser ? '👤 您' : '🤖 AI助手'} • {timestamp}
          </span>
        </div>

        {/* 消息内容 */}
        <div
          className={`rounded-lg px-4 py-3 ${
            isUser
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
          }`}
        >
          {/* 渲染消息内容 */}
          <div className="whitespace-pre-wrap break-words">
            {message.content
              .split(/```[\w]*\n(.*?)\n```/gs)
              .map((part, index) => {
                if (index % 2 === 0) {
                  // 普通文本
                  return <span key={index}>{part}</span>;
                } else {
                  // 代码块
                  return (
                    <div
                      key={index}
                      className="bg-gray-800 text-green-400 p-2 rounded mt-2 mb-2 font-mono text-sm"
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-gray-400">命令</span>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(part.trim())}
                            className="text-gray-400 hover:text-white p-1 h-6"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          {onCommandClick && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onCommandClick(part.trim())}
                              className="text-gray-400 hover:text-white p-1 h-6"
                            >
                              <Play className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                      <code>{part.trim()}</code>
                    </div>
                  );
                }
              })}
          </div>

          {/* 风险提示 */}
          {message.metadata?.riskLevel &&
            message.metadata.riskLevel !== 'Low' && (
              <div
                className={`flex items-center mt-2 text-sm ${getRiskLevelColor(message.metadata.riskLevel)}`}
              >
                <AlertTriangle className="w-4 h-4 mr-1" />
                <span>
                  {message.metadata.riskLevel === 'Critical' &&
                    '⚠️ 危险命令，请谨慎执行'}
                  {message.metadata.riskLevel === 'High' &&
                    '⚠️ 高风险命令，建议确认后执行'}
                  {message.metadata.riskLevel === 'Medium' &&
                    '⚠️ 中等风险，请确认后执行'}
                </span>
              </div>
            )}

          {/* 命令建议按钮 */}
          {commands.length > 0 && onCommandClick && !isUser && (
            <div className="mt-3 space-y-2">
              {commands.map((command, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onCommandClick(command)}
                    className="text-sm"
                  >
                    执行此命令
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(command)}
                    className="text-sm"
                  >
                    复制
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
