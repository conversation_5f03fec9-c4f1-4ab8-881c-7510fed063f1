export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  metadata?: {
    isCommand?: boolean;
    commandSuggestion?: string;
    riskLevel?: 'Low' | 'Medium' | 'High' | 'Critical';
  };
}

export interface ChatPanelProps {
  isVisible: boolean;
  onClose: () => void;
  onCommandSelect: (command: string) => void;
}

export interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  placeholder?: string;
}

export interface ChatMessageProps {
  message: ChatMessage;
  onCommandClick?: (command: string) => void;
}
