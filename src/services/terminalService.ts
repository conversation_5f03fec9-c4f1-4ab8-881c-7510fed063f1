import { invoke } from '@tauri-apps/api/core';
import { listen, UnlistenFn } from '@tauri-apps/api/event';

export interface CreateTerminalRequest {
  shell?: string;
  cwd?: string;
  env?: Record<string, string>;
  rows?: number;
  cols?: number;
}

export interface TerminalResponse {
  id: string;
  success: boolean;
  message?: string;
}

export interface ResizeRequest {
  rows: number;
  cols: number;
}

export interface TerminalOutputEvent {
  terminal_id: string;
  data: string;
}

export class TerminalService {
  private outputListeners: Map<string, (data: string) => void> = new Map();
  private initialized = false;
  private initializing = false;
  private unlistenFn: UnlistenFn | null = null;

  /**
   * 等待 Tauri 环境就绪（带超时保护，最大 5 秒）
   */
  private async waitForTauriReady(timeout = 5000): Promise<void> {
    try {
      await Promise.race([
        // 尝试简单调用，验证后端是否已准备好
        invoke('list_terminals'),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('tauri_wait_timeout')), timeout)
        ),
      ]);
    } catch (error) {
      // 如果超时或调用失败，不阻塞后续流程，只记录警告
      console.warn(
        '[TerminalService] 等待 Tauri 就绪失败或超时，继续初始化:',
        error
      );
    }
  }

  /**
   * 初始化服务，确保只初始化一次
   */
  async initialize(): Promise<void> {
    // 如果已经初始化，直接返回
    if (this.initialized) return;

    // 如果正在初始化，等待完成
    if (this.initializing) {
      while (this.initializing && !this.initialized) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      return;
    }

    this.initializing = true;

    try {
      console.log('[TerminalService] 初始化：等待 Tauri 就绪...');
      await this.waitForTauriReady();

      console.log('[TerminalService] 开始初始化事件监听器...');

      // 监听终端输出事件，存储unlisten函数
      this.unlistenFn = await listen<TerminalOutputEvent>(
        'terminal-output',
        event => {
          try {
            const { terminal_id, data } = event.payload;
            console.log('[TerminalService] 终端输出事件:', terminal_id, data);
            const listener = this.outputListeners.get(terminal_id);
            if (listener && typeof listener === 'function') {
              listener(data);
            }
          } catch (error) {
            console.error('[TerminalService] 处理终端输出事件失败:', error);
          }
        }
      );

      this.initialized = true;
      console.log('[TerminalService] 初始化完成');
    } catch (error) {
      console.error('[TerminalService] 初始化失败:', error);
      throw error;
    } finally {
      this.initializing = false;
    }
  }

  /**
   * 清理事件监听器
   */
  async cleanup(): Promise<void> {
    if (this.unlistenFn) {
      this.unlistenFn();
      this.unlistenFn = null;
    }
    this.outputListeners.clear();
    this.initialized = false;
    console.log('[TerminalService] 清理完成');
  }

  /**
   * 创建新的终端实例
   */
  async createTerminal(
    request: CreateTerminalRequest = {}
  ): Promise<TerminalResponse> {
    try {
      // 确保服务已初始化
      await this.initialize();

      console.log('[TerminalService] 发送终端创建请求:', request);
      const response = await invoke<TerminalResponse>('create_terminal', {
        request: {
          shell: request.shell,
          cwd: request.cwd,
          env: request.env,
          rows: request.rows || 24,
          cols: request.cols || 80,
        },
      });
      console.log('[TerminalService] 终端创建响应:', response);
      return response;
    } catch (error) {
      console.error('[TerminalService] 创建终端失败:', error);
      throw error;
    }
  }

  /**
   * 向终端写入数据
   */
  async writeToTerminal(terminalId: string, data: string): Promise<boolean> {
    try {
      const result = await invoke<boolean>('write_to_terminal', {
        terminal_id: terminalId,
        data,
      });
      return result;
    } catch (error) {
      console.error('[TerminalService] 写入终端失败:', error);
      throw error;
    }
  }

  /**
   * 调整终端大小
   */
  async resizeTerminal(
    terminalId: string,
    request: ResizeRequest
  ): Promise<boolean> {
    try {
      const result = await invoke<boolean>('resize_terminal', {
        terminal_id: terminalId,
        request: {
          rows: request.rows,
          cols: request.cols,
        },
      });
      return result;
    } catch (error) {
      console.error('[TerminalService] 调整终端大小失败:', error);
      throw error;
    }
  }

  /**
   * 关闭终端
   */
  async killTerminal(terminalId: string): Promise<boolean> {
    try {
      const result = await invoke<boolean>('kill_terminal', {
        terminal_id: terminalId,
      });

      // 清理监听器
      this.outputListeners.delete(terminalId);

      console.log('[TerminalService] 终端已关闭:', terminalId);
      return result;
    } catch (error) {
      console.error('[TerminalService] 关闭终端失败:', error);
      throw error;
    }
  }

  /**
   * 获取终端列表
   */
  async listTerminals(): Promise<string[]> {
    try {
      const terminals = await invoke<string[]>('list_terminals');
      return terminals;
    } catch (error) {
      console.error('[TerminalService] 获取终端列表失败:', error);
      throw error;
    }
  }

  /**
   * 检查终端是否存活
   */
  async isTerminalAlive(terminalId: string): Promise<boolean> {
    try {
      const isAlive = await invoke<boolean>('is_terminal_alive', {
        terminal_id: terminalId,
      });
      return isAlive;
    } catch (error) {
      console.error('[TerminalService] 检查终端状态失败:', error);
      return false;
    }
  }

  /**
   * 注册终端输出监听器
   */
  addOutputListener(
    terminalId: string,
    listener: (data: string) => void
  ): void {
    if (!terminalId || typeof listener !== 'function') {
      console.warn('[TerminalService] 无效的监听器参数');
      return;
    }
    this.outputListeners.set(terminalId, listener);
  }

  /**
   * 移除终端输出监听器
   */
  removeOutputListener(terminalId: string): void {
    if (!terminalId) {
      console.warn('[TerminalService] 无效的终端ID');
      return;
    }
    this.outputListeners.delete(terminalId);
  }

  /**
   * 执行高级命令（支持AI功能）
   */
  async executeAdvancedCommand(
    terminalId: string,
    input: string
  ): Promise<any> {
    try {
      const response = await invoke('execute_advanced_command', {
        request: {
          terminal_id: terminalId,
          input: input,
        },
      });
      return response;
    } catch (error) {
      console.error('[TerminalService] 执行高级命令失败:', error);
      throw error;
    }
  }
}

// 单例实例
export const terminalService = new TerminalService();
