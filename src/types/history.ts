// History types for command history management
// Types correspond to the Rust backend HistoryEntry and related structures

export interface HistoryEntry {
  id: string;
  session_id: string;
  command: ShellCommand;
  timestamp: string; // ISO string format from backend
  working_directory: string;
  exit_code: number | null;
  duration: number | null; // Duration in milliseconds
  output_size: number;
}

export interface ShellCommand {
  executable: string;
  args: string[];
  raw_input: string;
}

export interface SearchCriteria {
  query?: string;
  command_name?: string;
  working_directory?: string;
  session_id?: string;
  date_range?: [string, string]; // ISO string dates
  exit_code?: number;
  limit?: number;
}

export interface HistoryStats {
  total_commands: number;
  unique_commands: number;
  most_used_commands: Array<[string, number]>;
  commands_by_session: Record<string, number>;
  average_command_duration: number | null;
  success_rate: number;
}

export interface SearchHistoryRequest {
  terminal_id: string;
  query?: string;
  limit?: number;
}

export interface HistoryState {
  entries: HistoryEntry[];
  filteredEntries: HistoryEntry[];
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  stats: HistoryStats | null;
  selectedEntry: HistoryEntry | null;
  currentIndex: number; // For navigation
}

export interface HistoryActions {
  loadHistory: (terminalId: string, limit?: number) => Promise<void>;
  searchHistory: (
    terminalId: string,
    query: string,
    limit?: number
  ) => Promise<void>;
  setSearchQuery: (query: string) => void;
  clearSearch: () => void;
  selectEntry: (entry: HistoryEntry | null) => void;
  navigateHistory: (direction: 'up' | 'down') => HistoryEntry | null;
  executeHistoryCommand: (entry: HistoryEntry) => void;
  clearHistory: () => void;
  resetState: () => void;
}

export type HistoryStore = HistoryState & HistoryActions;
