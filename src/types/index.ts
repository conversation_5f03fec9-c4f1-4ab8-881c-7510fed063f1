// Global type definitions for TAgent

export interface SystemInfo {
  os: string;
  arch: string;
  family: string;
  shell: string;
  home: string;
}

export interface Terminal {
  id: string;
  title: string;
  workingDirectory: string;
  isActive: boolean;
}

export interface AICommand {
  type: 'command' | 'model' | 'explain';
  input: string;
  output?: string;
  timestamp: number;
}

export interface AppConfig {
  theme: 'dark' | 'light';
  fontSize: number;
  fontFamily: string;
  shell: string;
}

// Core Types for TAgent Application
// This file defines all interfaces and types used across the application

// ============================================================================
// Terminal Types
// ============================================================================

export interface TerminalTab {
  id: string;
  title: string;
  shell: string;
  currentDirectory: string;
  isActive: boolean;
  pid?: number;
  createdAt: Date;
  lastActivity: Date;
}

export interface CommandHistory {
  id: string;
  terminalId: string;
  command: string;
  output: string;
  exitCode: number;
  timestamp: Date;
  executionTime: number;
}

export interface TerminalState {
  tabs: TerminalTab[];
  activeTabId: string | null;
  history: CommandHistory[];
  isLoading: boolean;
  error: string | null;
}

export interface TerminalOutput {
  id: string;
  terminalId: string;
  content: string;
  type: 'stdout' | 'stderr' | 'stdin';
  timestamp: Date;
}

// ============================================================================
// AI Types
// ============================================================================

export interface AIProvider {
  id: string;
  name: string;
  type: 'local' | 'cloud';
  status: 'available' | 'unavailable' | 'error';
  config: Record<string, any>;
}

export interface AIRequest {
  id: string;
  type: 'command' | 'chat' | 'explain';
  input: string;
  context?: string;
  timestamp: Date;
}

export interface AIResponse {
  id: string;
  requestId: string;
  content: string;
  confidence: number;
  suggestions?: string[];
  timestamp: Date;
}

export interface AIState {
  providers: AIProvider[];
  activeProviderId: string | null;
  isProcessing: boolean;
  history: Array<{ request: AIRequest; response: AIResponse }>;
  error: string | null;
}

// ============================================================================
// Application Settings Types
// ============================================================================

export interface AppSettings {
  terminal: TerminalSettings;
  appearance: AppearanceSettings;
  ai: AISettings;
  security: SecuritySettings;
  shortcuts: ShortcutSettings;
}

export interface TerminalSettings {
  defaultShell: string;
  fontSize: number;
  fontFamily: string;
  cursorStyle: 'block' | 'underline' | 'bar';
  scrollback: number;
  enableBell: boolean;
}

export interface AppearanceSettings {
  theme: 'dark' | 'light' | 'auto';
  colorScheme: string;
  opacity: number;
  blur: boolean;
}

export interface AISettings {
  defaultProvider: string;
  autoSuggest: boolean;
  confirmDangerousCommands: boolean;
  maxHistoryLength: number;
}

export interface SecuritySettings {
  enableSafeMode: boolean;
  dangerousCommands: string[];
  requireConfirmation: boolean;
  auditLog: boolean;
}

export interface ShortcutSettings {
  newTab: string;
  closeTab: string;
  nextTab: string;
  previousTab: string;
  toggleSettings: string;
}

// ============================================================================
// Component Props Types
// ============================================================================

export interface TerminalDisplayProps {
  terminalId: string;
  fontSize: number;
  theme: TerminalTheme;
  onInput: (input: string) => void;
  onResize: (cols: number, rows: number) => void;
}

export interface TabBarProps {
  tabs: TerminalTab[];
  activeTabId: string;
  onTabSelect: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onNewTab: () => void;
}

export interface SettingsModalProps {
  isOpen: boolean;
  settings: AppSettings;
  onClose: () => void;
  onSave: (settings: AppSettings) => void;
}

// ============================================================================
// API Types
// ============================================================================

export interface TauriCommand<T = any> {
  command: string;
  payload?: T;
}

export interface TauriResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface TerminalCreateRequest {
  shell?: string;
  workingDirectory?: string;
}

export interface CommandExecuteRequest {
  terminalId: string;
  command: string;
}

export interface AIProcessRequest {
  input: string;
  type: 'command' | 'chat' | 'explain';
  context?: string;
}

// ============================================================================
// Event Types
// ============================================================================

export interface AppEvent<T = any> {
  type: string;
  payload: T;
  timestamp: Date;
}

export interface TerminalEvent extends AppEvent {
  terminalId: string;
}

export interface TerminalOutputEvent extends TerminalEvent {
  payload: {
    content: string;
    type: 'stdout' | 'stderr';
  };
}

export interface TerminalClosedEvent extends TerminalEvent {
  payload: {
    exitCode: number;
  };
}

export interface AIResponseEvent extends AppEvent {
  payload: AIResponse;
}

// ============================================================================
// Store Types
// ============================================================================

export interface RootState {
  terminal: TerminalState;
  ai: AIState;
  settings: AppSettings;
  app: AppState;
}

export interface AppState {
  isReady: boolean;
  version: string;
  platform: string;
  errors: AppError[];
}

export interface AppError {
  id: string;
  message: string;
  type: 'error' | 'warning' | 'info';
  timestamp: Date;
  dismissed: boolean;
}

// ============================================================================
// Utility Types
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequireOnly<T, K extends keyof T> = Partial<T> & Pick<T, K>;

export type AsyncResult<T> = Promise<
  { success: true; data: T } | { success: false; error: string }
>;

// ============================================================================
// Plugin System Types (for future extensibility)
// ============================================================================

export interface Plugin {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  enabled: boolean;
}

export interface PluginAPI {
  registerCommand: (command: string, handler: Function) => void;
  registerTheme: (theme: any) => void;
  onTerminalOutput: (callback: (output: TerminalOutput) => void) => void;
}

// ============================================================================
// Terminal Display Types
// ============================================================================

export interface TerminalTheme {
  name: string;
  background: string;
  foreground: string;
  cursor: string;
  selection: string;

  // ANSI 颜色
  black: string;
  red: string;
  green: string;
  yellow: string;
  blue: string;
  magenta: string;
  cyan: string;
  white: string;

  // 亮色版本
  brightBlack: string;
  brightRed: string;
  brightGreen: string;
  brightYellow: string;
  brightBlue: string;
  brightMagenta: string;
  brightCyan: string;
  brightWhite: string;
}

export interface TextAttribute {
  start: number;
  length: number;
  foreground?: string;
  background?: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
}

export interface TerminalLine {
  text: string;
  attributes: TextAttribute[];
  timestamp: number;
}

export interface TerminalBuffer {
  lines: TerminalLine[];
  maxLines: number;
  currentLine: number;
}

export interface CursorPosition {
  row: number;
  col: number;
}

export interface CursorState {
  position: CursorPosition;
  visible: boolean;
  style: 'block' | 'underline' | 'bar';
}

export interface Position {
  row: number;
  col: number;
}

export interface SelectionState {
  start?: Position;
  end?: Position;
  active: boolean;
}

export interface TerminalDisplayState {
  buffer: TerminalBuffer;
  cursor: CursorState;
  selection: SelectionState;
  scrollOffset: number;
  isActive: boolean;
  theme: TerminalTheme;
}

export interface TerminalBufferProps {
  buffer: TerminalBuffer;
  theme: TerminalTheme;
  onTextSelect: (start: Position, end: Position) => void;
}

export interface TerminalLineProps {
  line: TerminalLine;
  lineNumber: number;
  theme: TerminalTheme;
  onSelect: (start: Position, end: Position) => void;
}

export interface TerminalCursorProps {
  position: CursorPosition;
  theme: TerminalTheme;
  visible: boolean;
}

export interface TerminalSelectionProps {
  selection: SelectionState;
  theme: TerminalTheme;
}

// Default Terminal Theme
export const darkTheme: TerminalTheme = {
  name: 'Dark Default',
  background: '#1a1b26',
  foreground: '#c0caf5',
  cursor: '#c0caf5',
  selection: '#364a82',

  // ANSI 颜色
  black: '#15161e',
  red: '#f7768e',
  green: '#9ece6a',
  yellow: '#e0af68',
  blue: '#7aa2f7',
  magenta: '#bb9af7',
  cyan: '#7dcfff',
  white: '#a9b1d6',

  // 亮色版本
  brightBlack: '#414868',
  brightRed: '#f7768e',
  brightGreen: '#9ece6a',
  brightYellow: '#e0af68',
  brightBlue: '#7aa2f7',
  brightMagenta: '#bb9af7',
  brightCyan: '#7dcfff',
  brightWhite: '#c0caf5',
};

export const lightTheme: TerminalTheme = {
  name: 'Light Default',
  background: '#ffffff',
  foreground: '#383a42',
  cursor: '#383a42',
  selection: '#d0d0d0',

  // ANSI 颜色
  black: '#383a42',
  red: '#e45649',
  green: '#50a14f',
  yellow: '#c18401',
  blue: '#4078f2',
  magenta: '#a626a4',
  cyan: '#0184bc',
  white: '#a0a1a7',

  // 亮色版本
  brightBlack: '#4f525e',
  brightRed: '#e45649',
  brightGreen: '#50a14f',
  brightYellow: '#c18401',
  brightBlue: '#4078f2',
  brightMagenta: '#a626a4',
  brightCyan: '#0184bc',
  brightWhite: '#383a42',
};

// ============================================================================
// Keyboard Shortcuts Types
// ============================================================================

export interface ShortcutConfig {
  action: string; // 动作名称
  keys: string[]; // 按键组合 ['cmd', 't'] 或 ['ctrl', 'shift', 'c']
  description: string; // 描述
  category: ShortcutCategory;
  global: boolean; // 是否为全局快捷键
  enabled: boolean; // 是否启用
  platform?: Platform; // 平台限制
}

export enum ShortcutCategory {
  TAB_MANAGEMENT = 'tab_management',
  TERMINAL_OPERATION = 'terminal_operation',
  AI_FUNCTION = 'ai_function',
  APPLICATION = 'application',
  CUSTOM = 'custom',
}

export enum Platform {
  MACOS = 'macos',
  WINDOWS = 'windows',
  LINUX = 'linux',
}

export interface ShortcutState {
  // 快捷键配置
  shortcuts: Record<string, ShortcutConfig>;
  // 激活状态
  isEnabled: boolean;
  // 配置方法
  setShortcut: (action: string, config: ShortcutConfig) => void;
  resetShortcuts: () => void;
  enableShortcuts: (enabled: boolean) => void;
  checkConflict: (keys: string[]) => string[];
  getCurrentPlatform: () => Platform;
}

export interface UseShortcutsReturn {
  registerShortcut: (config: ShortcutConfig) => void;
  unregisterShortcut: (action: string) => void;
  executeAction: (action: string) => void;
  isShortcutPressed: (keys: string[]) => boolean;
}

// 快捷键回调函数类型
export type ShortcutCallback = () => void;

// 快捷键动作映射
export interface ShortcutActionMap {
  [action: string]: ShortcutCallback;
}

// Re-export history types
export * from './history';
