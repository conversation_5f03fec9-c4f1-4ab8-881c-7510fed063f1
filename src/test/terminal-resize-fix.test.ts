/**
 * 测试终端大小调整时的换行符处理
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock ResizeObserver
class MockResizeObserver {
  private callback: ResizeObserverCallback;

  constructor(callback: ResizeObserverCallback) {
    this.callback = callback;
  }

  observe(_target: Element) {
    // 模拟观察元素
  }

  unobserve(_target: Element) {
    // 模拟取消观察元素
  }

  disconnect() {
    // 模拟断开连接
  }

  // 模拟触发resize事件
  trigger(entries: ResizeObserverEntry[]) {
    this.callback(entries, this as any);
  }
}

// Mock setTimeout and clearTimeout
const mockSetTimeout = vi.fn();
const mockClearTimeout = vi.fn();

describe('Terminal Resize Fix', () => {
  beforeEach(() => {
    // Mock global objects
    global.ResizeObserver = MockResizeObserver as any;
    (global as any).setTimeout = mockSetTimeout;
    (global as any).clearTimeout = mockClearTimeout;

    // Reset mocks
    mockSetTimeout.mockClear();
    mockClearTimeout.mockClear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should debounce resize calls', () => {
    // 模拟创建ResizeObserver
    const callback = vi.fn();
    const observer = new MockResizeObserver(callback);

    // 模拟快速连续的resize事件
    const mockEntry = {
      target: {
        clientWidth: 800,
        clientHeight: 600,
      },
    } as ResizeObserverEntry;

    // 触发多次resize
    observer.trigger([mockEntry]);
    observer.trigger([mockEntry]);
    observer.trigger([mockEntry]);

    // 验证防抖逻辑
    expect(callback).toHaveBeenCalledTimes(3);
  });

  it('should filter excessive newlines', () => {
    // 测试换行符过滤逻辑
    const testData = 'line1\n\n\n\n\nline2\n\n\nline3';
    const expected = 'line1\n\nline2\n\nline3';

    // 应用过滤规则
    const filtered = testData.replace(/\n{3,}/g, '\n\n');

    expect(filtered).toBe(expected);
  });

  it('should handle carriage return and newline combinations', () => {
    // 测试回车符和换行符的处理
    const testData = 'line1\r\nline2\rline3\nline4';

    // 应用处理规则
    let processed = testData.replace(/\r\n/g, '\n');
    processed = processed.replace(/\r(?!\n)/g, '\n');

    const expected = 'line1\nline2\nline3\nline4';
    expect(processed).toBe(expected);
  });

  it('should only resize when dimensions actually change', () => {
    const resizeCallback = vi.fn();
    let lastSize = { cols: 0, rows: 0 };

    // 模拟resize逻辑
    const handleResize = (cols: number, rows: number) => {
      if (cols > 0 && rows > 0 && (cols !== lastSize.cols || rows !== lastSize.rows)) {
        lastSize = { cols, rows };
        resizeCallback(rows, cols);
      }
    };

    // 测试相同尺寸不会触发resize
    handleResize(80, 24);
    handleResize(80, 24); // 相同尺寸，不应该触发
    handleResize(90, 30); // 不同尺寸，应该触发

    expect(resizeCallback).toHaveBeenCalledTimes(2);
    expect(resizeCallback).toHaveBeenNthCalledWith(1, 24, 80);
    expect(resizeCallback).toHaveBeenNthCalledWith(2, 30, 90);
  });

  it('should remove cursor control sequences', () => {
    // 测试光标控制序列的移除
    const testData = '\u001b[A\u001b[A\u001b[Btext\u001b[C\u001b[D';

    // 应用过滤规则 - 注意B也需要被过滤
    let processed = testData.replace(/(\u001b\[[0-9]*A)+/g, '');
    processed = processed.replace(/\u001b\[[0-9]*[BCDEF]/g, '');

    expect(processed).toBe('text');
  });

  it('should remove clear screen and line sequences', () => {
    // 测试清屏和清行序列的移除
    const testData = 'before\u001b[2Jmiddle\u001b[Kafter';

    // 应用过滤规则
    const ESC = '\u001b';
    let processed = testData.replace(new RegExp(`${ESC}\\[[0-9]*J`, 'g'), '');
    processed = processed.replace(new RegExp(`${ESC}\\[[0-9]*K`, 'g'), '');

    expect(processed).toBe('beforemiddleafter');
  });

  it('should detect and skip terminal resize output', () => {
    // 测试终端大小调整输出的检测
    const isResizeOutput = (input: string): boolean => {
      const ESC = '\u001b';
      const patterns = [
        /^\s*\n+\s*$/,                                                    // 只有换行符和空白
        new RegExp(`^${ESC}\\[[0-9;]*[Hf]\\s*\\n*\\s*$`),              // 光标定位序列
        new RegExp(`^${ESC}\\[[0-9]*[JK]\\s*\\n*\\s*$`),               // 清屏/清行序列
        new RegExp(`^${ESC}\\[[0-9]*[ABCD]\\s*\\n*\\s*$`),             // 光标移动序列
      ];

      return patterns.some(pattern => pattern.test(input));
    };

    // 测试各种终端大小调整输出模式
    expect(isResizeOutput('\n')).toBe(true);                    // 单个换行符
    expect(isResizeOutput('  \n  ')).toBe(true);               // 空白和换行符
    expect(isResizeOutput('\u001b[H')).toBe(true);             // 光标定位
    expect(isResizeOutput('\u001b[2J')).toBe(true);            // 清屏
    expect(isResizeOutput('\u001b[A')).toBe(true);             // 光标上移
    expect(isResizeOutput('normal text')).toBe(false);         // 正常文本
    expect(isResizeOutput('ls -la')).toBe(false);              // 命令输出
  });
});
