{"desktop-capability": {"identifier": "desktop-capability", "description": "", "local": true, "windows": ["main"], "permissions": ["global-shortcut:default"], "platforms": ["macOS", "windows", "linux"]}, "migrated": {"identifier": "migrated", "description": "permissions that were migrated from v1", "local": true, "windows": ["main"], "permissions": ["core:default", "core:event:default", "core:window:default", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-close", "core:window:allow-toggle-maximize", "core:window:allow-start-dragging", "fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-copy-file", "fs:allow-mkdir", "fs:allow-remove", "fs:allow-rename", "fs:allow-exists", "shell:allow-execute", "shell:allow-open", "shell:allow-spawn", "os:allow-platform", "os:allow-version", "os:allow-os-type", "os:allow-family", "os:allow-arch", "os:allow-exe-extension", "os:allow-locale", "os:allow-hostname", "fs:default", "os:default", "shell:default"]}}