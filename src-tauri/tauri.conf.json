{"$schema": "https://schema.tauri.app/config/2.0", "productName": "TAgent", "version": "0.1.0", "identifier": "com.tagent.terminal", "build": {"beforeBuildCommand": "npm run build", "beforeDevCommand": "npm run dev", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "app": {"windows": [{"label": "main", "fullscreen": false, "resizable": true, "title": "TAgent", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "decorations": true, "transparent": false}], "security": {"csp": null, "capabilities": ["migrated"]}, "withGlobalTauri": true}}