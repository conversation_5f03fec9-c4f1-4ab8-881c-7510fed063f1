[package]
name = "tagent"
version = "0.1.0"
description = "TAgent - AI Enhanced Terminal Agent"
authors = ["TAgent Team"]
license = "MIT"
repository = "https://github.com/yourusername/TAgent"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
portable-pty = "0.8"
uuid = { version = "1.0", features = ["v4"] }
thiserror = "1.0"
dirs = "5.0"
which = "4.0"
async-trait = "0.1"
futures = "0.3"
tokio-util = { version = "0.7", features = ["codec"] }
shell-words = "1.1"
regex = "1.0"
chrono = { version = "0.4", features = ["serde"] }
whoami = "1.4"
tauri-plugin-fs = "2"
tauri-plugin-os = "2"
tauri-plugin-shell = "2"
reqwest = { version = "0.11", features = ["json", "stream"] }
tokio-stream = "0.1"
arboard = "3.2"

# Unix-specific dependencies
[target.'cfg(unix)'.dependencies]
nix = { version = "0.27", features = ["process", "signal"] }

# Windows-specific dependencies
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["wincon", "processenv", "handleapi"] }

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-global-shortcut = "2"

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = ["custom-protocol"]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
