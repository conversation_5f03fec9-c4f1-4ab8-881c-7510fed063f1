// IO Handler Demo - 演示增强的输入输出处理功能
// 展示 ANSI 解析、输入验证、WebSocket 通信等功能

use std::time::Duration;
use tagent::terminal::io_handler::*;
use tokio::time::sleep;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 TAgent IO 处理器演示");
    println!("===========================\n");

    // 1. 演示 ANSI 颜色解析
    demo_ansi_parsing().await?;

    // 2. 演示输入处理
    demo_input_processing().await?;

    // 3. 演示会话状态管理
    demo_session_management().await?;

    // 4. 演示增强IO处理器
    demo_enhanced_io_handler().await?;

    println!("\n✅ 演示完成！所有功能正常工作。");
    Ok(())
}

async fn demo_ansi_parsing() -> Result<(), Box<dyn std::error::Error>> {
    println!("📝 1. ANSI 序列解析演示");
    println!("-----------------------");

    let config = IOConfig::default();
    let mut processor = OutputProcessor::new(config);

    // 测试各种 ANSI 序列
    let test_cases: Vec<(&[u8], &str)> = vec![
        ("\x1b[31mRed Text\x1b[0m".as_bytes(), "红色文本"),
        ("\x1b[1;32mBold Green\x1b[0m".as_bytes(), "粗体绿色"),
        ("\x1b[4mUnderlined\x1b[0m".as_bytes(), "下划线"),
        ("\x1b[2J".as_bytes(), "清屏命令"),
        ("\x1b[10;20H".as_bytes(), "光标移动"),
        ("Hello \x1b[33mYellow\x1b[0m World!".as_bytes(), "混合文本"),
    ];

    for (input, description) in test_cases {
        println!("测试: {}", description);
        let chunks = processor.process_output(input, OutputType::Stdout).await?;

        println!("  输入: {:?}", String::from_utf8_lossy(input));
        println!("  解析结果: {} 个块", chunks.len());

        for (i, chunk) in chunks.iter().enumerate() {
            if !chunk.text.is_empty() {
                println!(
                    "    块 {}: '{}' (类型: {:?})",
                    i, chunk.text, chunk.chunk_type
                );
                if chunk.style.fg_color.is_some() {
                    println!("      前景色: {:?}", chunk.style.fg_color);
                }
                if chunk.style.bold {
                    println!("      样式: 粗体");
                }
            } else {
                println!("    块 {}: 控制序列 ({:?})", i, chunk.chunk_type);
            }
        }
        println!();
    }

    println!("✅ ANSI 解析演示完成\n");
    Ok(())
}

async fn demo_input_processing() -> Result<(), Box<dyn std::error::Error>> {
    println!("⌨️  2. 输入处理演示");
    println!("---------------------");

    let config = IOConfig::default();
    let processor = InputProcessor::new(config);

    // 测试各种键盘输入
    let test_inputs = vec![
        (KeyEvent::Char('h'), "字符输入: 'h'"),
        (KeyEvent::Char('e'), "字符输入: 'e'"),
        (KeyEvent::Char('l'), "字符输入: 'l'"),
        (KeyEvent::Char('l'), "字符输入: 'l'"),
        (KeyEvent::Char('o'), "字符输入: 'o'"),
        (KeyEvent::Backspace, "退格键"),
        (KeyEvent::ArrowUp, "上箭头 (历史导航)"),
        (KeyEvent::Tab, "Tab 键 (自动补全)"),
        (KeyEvent::Enter, "回车键 (执行命令)"),
        (KeyEvent::CtrlC, "Ctrl+C (中断)"),
    ];

    for (key_event, description) in test_inputs {
        println!("测试: {}", description);
        match processor.process_key_event(key_event).await {
            Ok(action) => {
                println!("  处理结果: {:?}", action);
                match action {
                    InputAction::ExecuteCommand(cmd) => {
                        println!("  -> 执行命令: '{}'", cmd);
                    }
                    InputAction::UpdateDisplay => {
                        let current_line = processor.get_current_line();
                        println!("  -> 当前行: '{}'", current_line);
                    }
                    InputAction::Interrupt => {
                        println!("  -> 中断信号");
                    }
                    _ => {}
                }
            }
            Err(e) => {
                println!("  处理错误: {}", e);
            }
        }
        println!();
        sleep(Duration::from_millis(100)).await; // 小延迟便于观察
    }

    println!("✅ 输入处理演示完成\n");
    Ok(())
}

async fn demo_session_management() -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 3. 会话状态管理演示");
    println!("-------------------------");

    let session_id = "demo-session-001".to_string();
    let terminal_size = TerminalSize { rows: 24, cols: 80 };
    let mut session = SessionState::new(session_id.clone(), terminal_size);

    println!("会话ID: {}", session.session_id);
    println!(
        "终端大小: {}x{}",
        session.terminal_size.cols, session.terminal_size.rows
    );
    println!("当前模式: {:?}", session.current_mode);
    println!(
        "光标位置: ({}, {})",
        session.cursor_position.row, session.cursor_position.col
    );

    // 模拟一些操作
    session.input_history.push_back("ls -la".to_string());
    session.input_history.push_back("cd /home".to_string());
    session.input_history.push_back("git status".to_string());

    session.output_history.push_back(OutputLine {
        content: "drwxr-xr-x  5 <USER> <GROUP> 4096 Dec 19 12:00 .".to_string(),
        line_type: OutputType::Stdout,
        timestamp: std::time::SystemTime::now(),
        ansi_formatted: false,
    });

    println!("输入历史 ({} 条):", session.input_history.len());
    for (i, cmd) in session.input_history.iter().enumerate() {
        println!("  {}: {}", i + 1, cmd);
    }

    println!("输出历史 ({} 条):", session.output_history.len());
    for line in &session.output_history {
        println!("  [{:?}] {}", line.line_type, line.content);
    }

    // 测试终端大小调整
    let new_size = TerminalSize {
        rows: 30,
        cols: 100,
    };
    session.terminal_size = new_size;
    session.screen_buffer.resize(new_size);
    println!("调整终端大小为: {}x{}", new_size.cols, new_size.rows);

    println!("✅ 会话状态管理演示完成\n");
    Ok(())
}

async fn demo_enhanced_io_handler() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔧 4. 增强 IO 处理器演示");
    println!("---------------------------");

    let session_id = "enhanced-demo-001".to_string();
    let terminal_size = TerminalSize { rows: 24, cols: 80 };
    let config = IOConfig {
        ansi_colors_enabled: true,
        unicode_support: true,
        input_validation_enabled: true,
        ..IOConfig::default()
    };

    let mut handler = EnhancedIOHandler::new(session_id.clone(), terminal_size, config);

    println!("创建增强IO处理器:");
    println!("  会话ID: {}", session_id);
    println!("  ANSI 颜色: 启用");
    println!("  Unicode 支持: 启用");
    println!("  输入验证: 启用");

    // 测试输入处理
    println!("\n测试输入处理:");
    let test_input = KeyEvent::Char('A');
    match handler.process_input(test_input).await {
        Ok(action) => println!("  输入 'A' -> {:?}", action),
        Err(e) => println!("  输入处理错误: {}", e),
    }

    // 测试输出处理
    println!("\n测试输出处理:");
    let test_output = "\x1b[1;34mBlue Bold Text\x1b[0m Normal Text".as_bytes();
    match handler
        .process_output(test_output, OutputType::Stdout)
        .await
    {
        Ok(chunks) => {
            println!(
                "  处理 {} 字节输出，生成 {} 个块",
                test_output.len(),
                chunks.len()
            );
            for (i, chunk) in chunks.iter().enumerate() {
                if !chunk.text.is_empty() {
                    println!("    块 {}: '{}'", i, chunk.text);
                }
            }
        }
        Err(e) => println!("  输出处理错误: {}", e),
    }

    // 测试终端调整
    println!("\n测试终端大小调整:");
    match handler.resize_terminal(25, 90).await {
        Ok(()) => println!("  成功调整终端大小为 90x25"),
        Err(e) => println!("  调整失败: {}", e),
    }

    // 获取会话状态
    let state = handler.get_session_state();
    println!(
        "  新的终端大小: {}x{}",
        state.terminal_size.cols, state.terminal_size.rows
    );

    println!("✅ 增强 IO 处理器演示完成\n");
    Ok(())
}
