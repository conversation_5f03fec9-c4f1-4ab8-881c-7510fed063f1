// TAgent - AI Enhanced Terminal Agent
// Main application entry point

#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use log::info;
use tauri::{generate_context, generate_handler, Builder, Manager};

// Import our library
use tagent::{create_app_state, initialize_services};

// Import shortcut management
use tagent::system::shortcuts::{
    clear_all_global_shortcuts, get_global_shortcuts, init_global_shortcuts,
    is_global_shortcut_registered, register_global_shortcut, set_global_shortcut_enabled,
    unregister_global_shortcut, update_global_shortcut, GlobalShortcutManagerStateType,
};

#[tokio::main]
async fn main() {
    // Initialize logging
    env_logger::init();
    info!("Starting TAgent...");

    // Create application state
    let app_state = match create_app_state().await {
        Ok(state) => state,
        Err(e) => {
            eprintln!("Failed to create application state: {}", e);
            std::process::exit(1);
        }
    };

    // Initialize services
    if let Err(e) = initialize_services(&app_state).await {
        eprintln!("Failed to initialize services: {}", e);
        std::process::exit(1);
    }

    // Extract chat manager state for separate management
    let chat_manager_state = app_state.chat_manager_state.clone();
    let ai_service_manager = app_state.ai_service_manager.clone();

    // Build and run the Tauri application with plugins
    Builder::default()
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_shell::init())
        .manage(app_state)
        .manage(chat_manager_state)
        .manage(ai_service_manager)
        .invoke_handler(generate_handler![
            // Terminal commands
            tagent::create_terminal,
            tagent::write_to_terminal,
            tagent::resize_terminal,
            tagent::kill_terminal,
            tagent::list_terminals,
            tagent::is_terminal_alive,
            // Advanced command execution
            tagent::execute_advanced_command,
            tagent::confirm_dangerous_command,
            tagent::get_advanced_command_history,
            tagent::search_advanced_command_history,
            tagent::get_execution_context,
            tagent::update_working_directory,
            tagent::get_pending_confirmations,
            tagent::cleanup_terminal_executor,
            // Global shortcut commands (temporarily disabled for compilation)
            // register_global_shortcut,
            // unregister_global_shortcut,
            // get_global_shortcuts,
            // update_global_shortcut,
            // set_global_shortcut_enabled,
            // clear_all_global_shortcuts,
            // is_global_shortcut_registered,
            // New AI commands
            tagent::ai_process_natural_language,
            tagent::ai_explain_command,
            tagent::ai_chat,
            tagent::ai_chat_simple,
            tagent::ai_get_available_models,
            tagent::ai_switch_model,
            tagent::ai_update_config,
            tagent::ai_health_check,
            // Chat commands
            tagent::start_chat_session,
            tagent::send_chat_message,
            tagent::end_chat_session,
            tagent::ai_chat_with_context,
            tagent::get_chat_history,
            tagent::clear_chat_history,
            tagent::get_current_directory,
            tagent::get_shell_type,
            tagent::get_environment_variables,
            // Legacy AI commands (for backward compatibility)
            tagent::explain_command,
            tagent::process_natural_language,
            tagent::list_ai_services,
            // System commands
            tagent::get_system_info,
            tagent::get_app_version,
            tagent::get_settings,
            tagent::update_settings,
            // Clipboard commands
            tagent::read_clipboard,
            tagent::write_clipboard,
            tagent::has_clipboard_text,
            tagent::clear_clipboard
        ])
        .setup(|app| {
            info!("TAgent application setup completed");

            // Initialize global shortcut manager
            let shortcut_manager = init_global_shortcuts(app.handle().clone());
            let shortcut_state: GlobalShortcutManagerStateType =
                std::sync::Mutex::new(shortcut_manager);
            app.manage(shortcut_state);

            info!("Global shortcut manager initialized");
            Ok(())
        })
        .run(generate_context!())
        .expect("Error while running TAgent application");
}
