// Command parser module - Parses user input into executable commands
// Supports both regular shell commands and AI-enhanced commands with special prefixes

use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;

/// Command type enum - represents different types of commands
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandType {
    /// Regular shell command
    Shell(ShellCommand),
    /// AI-enhanced command with special prefixes
    AICommand(AICommandType),
    /// Built-in terminal commands
    Builtin(BuiltinCommand),
}

/// AI command types with different prefixes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AICommandType {
    /// @command - Natural language to command conversion
    NaturalLanguage { prompt: String },
    /// @model - AI chat mode
    ModelChat { query: String },
    /// @explain - Command explanation
    CommandExplain { command: String },
}

/// Shell command structure
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ShellCommand {
    pub executable: String,
    pub args: Vec<String>,
    pub working_dir: Option<String>,
    pub env_vars: HashMap<String, String>,
    pub stdin_input: Option<String>,
    pub use_pipes: bool,
    pub redirect_output: Option<String>,
}

/// Built-in terminal commands
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuiltinCommand {
    Clear,
    Exit,
    History { limit: Option<usize> },
    Help,
}

/// Parse errors
#[derive(Debug, Error)]
pub enum ParseError {
    #[error("Empty command")]
    EmptyCommand,

    #[error("Invalid syntax: {0}")]
    InvalidSyntax(String),

    #[error("Unknown AI command prefix: {0}")]
    UnknownAICommand(String),

    #[error("Missing required argument: {0}")]
    MissingArgument(String),

    #[error("Invalid builtin command: {0}")]
    InvalidBuiltin(String),
}

/// Main command parser
pub struct CommandParser {
    ai_prefixes: HashMap<String, String>,
    builtin_commands: HashMap<String, String>,
    pipe_regex: Regex,
    redirect_regex: Regex,
}

impl CommandParser {
    /// Create a new command parser
    pub fn new() -> Self {
        let mut ai_prefixes = HashMap::new();
        ai_prefixes.insert(
            "@command".to_string(),
            "Natural language to command".to_string(),
        );
        ai_prefixes.insert("@model".to_string(), "AI chat mode".to_string());
        ai_prefixes.insert("@explain".to_string(), "Command explanation".to_string());

        let mut builtin_commands = HashMap::new();
        builtin_commands.insert("clear".to_string(), "Clear terminal screen".to_string());
        builtin_commands.insert("exit".to_string(), "Exit terminal".to_string());
        builtin_commands.insert("history".to_string(), "Show command history".to_string());
        builtin_commands.insert("help".to_string(), "Show help information".to_string());

        let pipe_regex = Regex::new(r"\s*\|\s*").unwrap();
        let redirect_regex = Regex::new(r"\s*>\s*(\S+)").unwrap();

        Self {
            ai_prefixes,
            builtin_commands,
            pipe_regex,
            redirect_regex,
        }
    }

    /// Parse input string into CommandType
    pub fn parse(&self, input: &str) -> Result<CommandType, ParseError> {
        let trimmed = input.trim();

        if trimmed.is_empty() {
            return Err(ParseError::EmptyCommand);
        }

        // Check for AI command prefixes
        if let Some(ai_command) = self.parse_ai_command(trimmed)? {
            return Ok(CommandType::AICommand(ai_command));
        }

        // Check for built-in commands
        if let Some(builtin) = self.parse_builtin_command(trimmed)? {
            return Ok(CommandType::Builtin(builtin));
        }

        // Parse as shell command
        self.parse_shell_command(trimmed).map(CommandType::Shell)
    }

    /// Parse AI commands with special prefixes
    fn parse_ai_command(&self, input: &str) -> Result<Option<AICommandType>, ParseError> {
        for prefix in self.ai_prefixes.keys() {
            if input.starts_with(prefix) {
                let content = input[prefix.len()..].trim();

                if content.is_empty() {
                    return Err(ParseError::MissingArgument(prefix.clone()));
                }

                return Ok(Some(match prefix.as_str() {
                    "@command" => AICommandType::NaturalLanguage {
                        prompt: content.to_string(),
                    },
                    "@model" => AICommandType::ModelChat {
                        query: content.to_string(),
                    },
                    "@explain" => AICommandType::CommandExplain {
                        command: content.to_string(),
                    },
                    _ => return Err(ParseError::UnknownAICommand(prefix.clone())),
                }));
            }
        }
        Ok(None)
    }

    /// Parse built-in commands
    fn parse_builtin_command(&self, input: &str) -> Result<Option<BuiltinCommand>, ParseError> {
        let words: Vec<&str> = input.split_whitespace().collect();
        if words.is_empty() {
            return Ok(None);
        }

        match words[0] {
            "clear" => Ok(Some(BuiltinCommand::Clear)),
            "exit" => Ok(Some(BuiltinCommand::Exit)),
            "help" => Ok(Some(BuiltinCommand::Help)),
            "history" => {
                let limit = if words.len() > 1 {
                    words[1].parse::<usize>().ok()
                } else {
                    None
                };
                Ok(Some(BuiltinCommand::History { limit }))
            }
            _ => {
                if self.builtin_commands.contains_key(words[0]) {
                    Err(ParseError::InvalidBuiltin(words[0].to_string()))
                } else {
                    Ok(None)
                }
            }
        }
    }

    /// Parse shell commands
    fn parse_shell_command(&self, input: &str) -> Result<ShellCommand, ParseError> {
        // Check for output redirection
        let redirect_output = if let Some(captures) = self.redirect_regex.captures(input) {
            Some(captures.get(1).unwrap().as_str().to_string())
        } else {
            None
        };

        // Remove redirection from input for parsing
        let clean_input = if redirect_output.is_some() {
            self.redirect_regex.replace(input, "").to_string()
        } else {
            input.to_string()
        };

        // Check for pipes
        let use_pipes = self.pipe_regex.is_match(&clean_input);

        // For now, handle simple commands without pipes
        // TODO: Implement full pipe chain parsing
        let command_part = if use_pipes {
            clean_input.split('|').next().unwrap().trim()
        } else {
            clean_input.trim()
        };

        // Parse command and arguments using shell-words
        let words = shell_words::split(command_part)
            .map_err(|e| ParseError::InvalidSyntax(e.to_string()))?;

        if words.is_empty() {
            return Err(ParseError::EmptyCommand);
        }

        let executable = words[0].clone();
        let args = words[1..].to_vec();

        Ok(ShellCommand {
            executable,
            args,
            working_dir: None,
            env_vars: HashMap::new(),
            stdin_input: None,
            use_pipes,
            redirect_output,
        })
    }

    /// Get available AI command prefixes
    pub fn get_ai_prefixes(&self) -> &HashMap<String, String> {
        &self.ai_prefixes
    }

    /// Get available built-in commands
    pub fn get_builtin_commands(&self) -> &HashMap<String, String> {
        &self.builtin_commands
    }
}

impl Default for CommandParser {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_simple_command() {
        let parser = CommandParser::new();

        match parser.parse("ls -la").unwrap() {
            CommandType::Shell(cmd) => {
                assert_eq!(cmd.executable, "ls");
                assert_eq!(cmd.args, vec!["-la"]);
            }
            _ => panic!("Expected shell command"),
        }
    }

    #[test]
    fn test_parse_ai_commands() {
        let parser = CommandParser::new();

        match parser
            .parse("@command list all files in current directory")
            .unwrap()
        {
            CommandType::AICommand(AICommandType::NaturalLanguage { prompt }) => {
                assert_eq!(prompt, "list all files in current directory");
            }
            _ => panic!("Expected AI natural language command"),
        }

        match parser
            .parse("@model what is the best way to optimize git workflow?")
            .unwrap()
        {
            CommandType::AICommand(AICommandType::ModelChat { query }) => {
                assert_eq!(query, "what is the best way to optimize git workflow?");
            }
            _ => panic!("Expected AI model chat command"),
        }

        match parser.parse("@explain ps aux | grep python").unwrap() {
            CommandType::AICommand(AICommandType::CommandExplain { command }) => {
                assert_eq!(command, "ps aux | grep python");
            }
            _ => panic!("Expected AI command explanation"),
        }
    }

    #[test]
    fn test_parse_builtin_commands() {
        let parser = CommandParser::new();

        match parser.parse("clear").unwrap() {
            CommandType::Builtin(BuiltinCommand::Clear) => {}
            _ => panic!("Expected clear builtin command"),
        }

        match parser.parse("history 50").unwrap() {
            CommandType::Builtin(BuiltinCommand::History { limit: Some(50) }) => {}
            _ => panic!("Expected history builtin command"),
        }
    }

    #[test]
    fn test_parse_with_redirection() {
        let parser = CommandParser::new();

        match parser.parse("ls -la > output.txt").unwrap() {
            CommandType::Shell(cmd) => {
                assert_eq!(cmd.executable, "ls");
                assert_eq!(cmd.args, vec!["-la"]);
                assert_eq!(cmd.redirect_output, Some("output.txt".to_string()));
            }
            _ => panic!("Expected shell command with redirection"),
        }
    }

    #[test]
    fn test_parse_empty_command() {
        let parser = CommandParser::new();

        match parser.parse("") {
            Err(ParseError::EmptyCommand) => {}
            _ => panic!("Expected empty command error"),
        }
    }
}
