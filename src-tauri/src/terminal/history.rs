// Command history manager - <PERSON><PERSON> command history storage, retrieval, and search
// Provides persistent storage and efficient searching capabilities

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::fs;
use std::path::PathBuf;
use std::time::{Duration, SystemTime};
use thiserror::Error;
use uuid::Uuid;

use super::command_parser::ShellCommand;

/// History entry representing a single command execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistoryEntry {
    pub id: String,
    pub session_id: String,
    pub command: ShellCommand,
    pub timestamp: DateTime<Utc>,
    pub working_directory: String,
    pub exit_code: Option<i32>,
    pub duration: Option<Duration>,
    pub output_size: usize,
}

/// Command execution record with detailed information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionRecord {
    pub id: String,
    pub command: ShellCommand,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub duration: Option<Duration>,
    pub exit_code: Option<i32>,
    pub output: Option<String>,
    pub error_output: Option<String>,
    pub working_directory: String,
    pub session_id: String,
}

/// Search criteria for history queries
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchCriteria {
    pub query: Option<String>,
    pub command_name: Option<String>,
    pub working_directory: Option<String>,
    pub session_id: Option<String>,
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    pub exit_code: Option<i32>,
    pub limit: Option<usize>,
}

/// History statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistoryStats {
    pub total_commands: usize,
    pub unique_commands: usize,
    pub most_used_commands: Vec<(String, usize)>,
    pub commands_by_session: std::collections::HashMap<String, usize>,
    pub average_command_duration: Option<Duration>,
    pub success_rate: f64,
}

/// History errors
#[derive(Debug, Error)]
pub enum HistoryError {
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),

    #[error("Entry not found: {id}")]
    EntryNotFound { id: String },

    #[error("Invalid search criteria: {reason}")]
    InvalidSearchCriteria { reason: String },

    #[error("Storage error: {reason}")]
    StorageError { reason: String },
}

/// History storage trait for different storage backends
pub trait HistoryStorage: Send + Sync {
    fn add_entry(&mut self, entry: HistoryEntry) -> Result<(), HistoryError>;
    fn get_entry(&self, id: &str) -> Result<Option<HistoryEntry>, HistoryError>;
    fn search_entries(&self, criteria: &SearchCriteria) -> Result<Vec<HistoryEntry>, HistoryError>;
    fn get_recent_entries(
        &self,
        session_id: &str,
        limit: usize,
    ) -> Result<Vec<HistoryEntry>, HistoryError>;
    fn count_entries(&self, session_id: &str) -> Result<usize, HistoryError>;
    fn remove_oldest_entries(&mut self, session_id: &str, count: usize)
        -> Result<(), HistoryError>;
    fn clear_session(&mut self, session_id: &str) -> Result<(), HistoryError>;
    fn get_stats(&self, session_id: Option<&str>) -> Result<HistoryStats, HistoryError>;
}

/// In-memory history storage implementation
pub struct InMemoryHistoryStorage {
    entries: VecDeque<HistoryEntry>,
    max_entries: usize,
}

impl InMemoryHistoryStorage {
    pub fn new(max_entries: usize) -> Self {
        Self {
            entries: VecDeque::with_capacity(max_entries),
            max_entries,
        }
    }

    pub fn add_entry(&mut self, entry: HistoryEntry) -> Result<(), HistoryError> {
        if self.entries.len() >= self.max_entries {
            self.entries.pop_front();
        }
        self.entries.push_back(entry);
        Ok(())
    }

    pub fn get_entry(&self, id: &str) -> Result<Option<HistoryEntry>, HistoryError> {
        Ok(self.entries.iter().find(|e| e.id == id).cloned())
    }

    pub fn search_entries(
        &self,
        criteria: &SearchCriteria,
    ) -> Result<Vec<HistoryEntry>, HistoryError> {
        let mut results: Vec<HistoryEntry> = self
            .entries
            .iter()
            .filter(|entry| {
                if let Some(ref query) = criteria.query {
                    let command_text = format!(
                        "{} {}",
                        entry.command.executable,
                        entry.command.args.join(" ")
                    );
                    command_text.to_lowercase().contains(&query.to_lowercase())
                } else {
                    true
                }
            })
            .cloned()
            .collect();

        // Sort by timestamp (most recent first)
        results.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        // Apply limit
        if let Some(limit) = criteria.limit {
            results.truncate(limit);
        }

        Ok(results)
    }

    pub fn get_recent_entries(
        &self,
        session_id: &str,
        limit: usize,
    ) -> Result<Vec<HistoryEntry>, HistoryError> {
        let mut results: Vec<HistoryEntry> = self
            .entries
            .iter()
            .filter(|e| e.session_id == session_id)
            .cloned()
            .collect();

        results.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        results.truncate(limit);

        Ok(results)
    }

    pub fn count_entries(&self, session_id: &str) -> Result<usize, HistoryError> {
        Ok(self
            .entries
            .iter()
            .filter(|e| e.session_id == session_id)
            .count())
    }

    pub fn remove_oldest_entries(
        &mut self,
        session_id: &str,
        count: usize,
    ) -> Result<(), HistoryError> {
        let mut removed = 0;
        while removed < count && !self.entries.is_empty() {
            if let Some(front) = self.entries.front() {
                if front.session_id == session_id {
                    self.entries.pop_front();
                    removed += 1;
                } else {
                    break;
                }
            } else {
                break;
            }
        }
        Ok(())
    }

    pub fn clear_session(&mut self, session_id: &str) -> Result<(), HistoryError> {
        self.entries.retain(|entry| entry.session_id != session_id);
        Ok(())
    }

    pub fn get_stats(&self, session_id: Option<&str>) -> Result<HistoryStats, HistoryError> {
        let filtered_entries: Vec<&HistoryEntry> = match session_id {
            Some(sid) => self
                .entries
                .iter()
                .filter(|e| e.session_id == sid)
                .collect(),
            None => self.entries.iter().collect(),
        };

        let total_commands = filtered_entries.len();

        // Calculate unique commands
        let mut unique_commands = std::collections::HashSet::new();
        let mut command_counts = std::collections::HashMap::new();
        let mut session_counts = std::collections::HashMap::new();
        let mut total_duration = std::time::Duration::new(0, 0);
        let mut duration_count = 0;
        let mut successful_commands = 0;

        for entry in &filtered_entries {
            let command_text = format!(
                "{} {}",
                entry.command.executable,
                entry.command.args.join(" ")
            );
            unique_commands.insert(command_text.clone());

            *command_counts.entry(command_text).or_insert(0) += 1;
            *session_counts.entry(entry.session_id.clone()).or_insert(0) += 1;

            if let Some(duration) = entry.duration {
                total_duration += duration;
                duration_count += 1;
            }

            if let Some(exit_code) = entry.exit_code {
                if exit_code == 0 {
                    successful_commands += 1;
                }
            }
        }

        // Get most used commands (top 10)
        let mut most_used: Vec<(String, usize)> = command_counts.into_iter().collect();
        most_used.sort_by(|a, b| b.1.cmp(&a.1));
        most_used.truncate(10);

        let average_command_duration = if duration_count > 0 {
            Some(total_duration / duration_count as u32)
        } else {
            None
        };

        let success_rate = if total_commands > 0 {
            successful_commands as f64 / total_commands as f64
        } else {
            0.0
        };

        Ok(HistoryStats {
            total_commands,
            unique_commands: unique_commands.len(),
            most_used_commands: most_used,
            commands_by_session: session_counts,
            average_command_duration,
            success_rate,
        })
    }
}

// Implement HistoryStorage trait for InMemoryHistoryStorage
impl HistoryStorage for InMemoryHistoryStorage {
    fn add_entry(&mut self, entry: HistoryEntry) -> Result<(), HistoryError> {
        self.add_entry(entry)
    }

    fn get_entry(&self, id: &str) -> Result<Option<HistoryEntry>, HistoryError> {
        self.get_entry(id)
    }

    fn search_entries(&self, criteria: &SearchCriteria) -> Result<Vec<HistoryEntry>, HistoryError> {
        self.search_entries(criteria)
    }

    fn get_recent_entries(
        &self,
        session_id: &str,
        limit: usize,
    ) -> Result<Vec<HistoryEntry>, HistoryError> {
        self.get_recent_entries(session_id, limit)
    }

    fn count_entries(&self, session_id: &str) -> Result<usize, HistoryError> {
        self.count_entries(session_id)
    }

    fn remove_oldest_entries(
        &mut self,
        session_id: &str,
        count: usize,
    ) -> Result<(), HistoryError> {
        self.remove_oldest_entries(session_id, count)
    }

    fn clear_session(&mut self, session_id: &str) -> Result<(), HistoryError> {
        self.clear_session(session_id)
    }

    fn get_stats(&self, session_id: Option<&str>) -> Result<HistoryStats, HistoryError> {
        self.get_stats(session_id)
    }
}

/// File-based history storage implementation
pub struct FileHistoryStorage {
    file_path: PathBuf,
    entries: VecDeque<HistoryEntry>,
    max_entries: usize,
    auto_save: bool,
}

impl FileHistoryStorage {
    pub fn new(file_path: PathBuf, max_entries: usize) -> Result<Self, HistoryError> {
        let mut storage = Self {
            file_path,
            entries: VecDeque::with_capacity(max_entries),
            max_entries,
            auto_save: true,
        };

        storage.load_from_file()?;
        Ok(storage)
    }

    pub fn with_auto_save(mut self, auto_save: bool) -> Self {
        self.auto_save = auto_save;
        self
    }

    fn load_from_file(&mut self) -> Result<(), HistoryError> {
        if !self.file_path.exists() {
            return Ok(());
        }

        let content = fs::read_to_string(&self.file_path)?;
        if content.trim().is_empty() {
            return Ok(());
        }

        let entries: Vec<HistoryEntry> = serde_json::from_str(&content)?;
        self.entries = entries.into_iter().collect();

        // Limit entries after loading
        while self.entries.len() > self.max_entries {
            self.entries.pop_front();
        }

        Ok(())
    }

    fn save_to_file(&self) -> Result<(), HistoryError> {
        // Create parent directories if they don't exist
        if let Some(parent) = self.file_path.parent() {
            fs::create_dir_all(parent)?;
        }

        let entries: Vec<&HistoryEntry> = self.entries.iter().collect();
        let content = serde_json::to_string_pretty(&entries)?;
        fs::write(&self.file_path, content)?;
        Ok(())
    }

    pub fn force_save(&self) -> Result<(), HistoryError> {
        self.save_to_file()
    }
}

impl HistoryStorage for FileHistoryStorage {
    fn add_entry(&mut self, entry: HistoryEntry) -> Result<(), HistoryError> {
        if self.entries.len() >= self.max_entries {
            self.entries.pop_front();
        }
        self.entries.push_back(entry);

        if self.auto_save {
            self.save_to_file()?;
        }

        Ok(())
    }

    fn get_entry(&self, id: &str) -> Result<Option<HistoryEntry>, HistoryError> {
        Ok(self.entries.iter().find(|e| e.id == id).cloned())
    }

    fn search_entries(&self, criteria: &SearchCriteria) -> Result<Vec<HistoryEntry>, HistoryError> {
        // Delegate to in-memory implementation
        let mut in_memory = InMemoryHistoryStorage::new(self.max_entries);
        in_memory.entries = self.entries.clone();
        in_memory.search_entries(criteria)
    }

    fn get_recent_entries(
        &self,
        session_id: &str,
        limit: usize,
    ) -> Result<Vec<HistoryEntry>, HistoryError> {
        let mut in_memory = InMemoryHistoryStorage::new(self.max_entries);
        in_memory.entries = self.entries.clone();
        in_memory.get_recent_entries(session_id, limit)
    }

    fn count_entries(&self, session_id: &str) -> Result<usize, HistoryError> {
        Ok(self
            .entries
            .iter()
            .filter(|e| e.session_id == session_id)
            .count())
    }

    fn remove_oldest_entries(
        &mut self,
        session_id: &str,
        count: usize,
    ) -> Result<(), HistoryError> {
        let mut removed = 0;
        while removed < count && !self.entries.is_empty() {
            if let Some(front) = self.entries.front() {
                if front.session_id == session_id {
                    self.entries.pop_front();
                    removed += 1;
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        if self.auto_save {
            self.save_to_file()?;
        }

        Ok(())
    }

    fn clear_session(&mut self, session_id: &str) -> Result<(), HistoryError> {
        self.entries.retain(|e| e.session_id != session_id);

        if self.auto_save {
            self.save_to_file()?;
        }

        Ok(())
    }

    fn get_stats(&self, session_id: Option<&str>) -> Result<HistoryStats, HistoryError> {
        let mut in_memory = InMemoryHistoryStorage::new(self.max_entries);
        in_memory.entries = self.entries.clone();
        in_memory.get_stats(session_id)
    }
}

/// Command history manager
pub struct CommandHistoryManager {
    storage: Box<dyn HistoryStorage>,
    session_id: String,
    max_history_size: usize,
}

impl CommandHistoryManager {
    /// Create new history manager with custom storage
    pub fn new(storage: Box<dyn HistoryStorage>, session_id: String) -> Self {
        Self {
            storage,
            session_id,
            max_history_size: 10000,
        }
    }

    /// Create new history manager with in-memory storage
    pub fn with_memory_storage(session_id: String, max_entries: usize) -> Self {
        let storage = Box::new(InMemoryHistoryStorage::new(max_entries));
        Self::new(storage, session_id)
    }

    /// Create new history manager with file storage
    pub fn with_file_storage(
        session_id: String,
        file_path: PathBuf,
        max_entries: usize,
    ) -> Result<Self, HistoryError> {
        let storage = Box::new(FileHistoryStorage::new(file_path, max_entries)?);
        Ok(Self::new(storage, session_id))
    }

    /// Add a command to history
    pub fn add_command(
        &mut self,
        command: &ShellCommand,
        working_directory: String,
    ) -> Result<String, HistoryError> {
        let entry = HistoryEntry {
            id: Uuid::new_v4().to_string(),
            session_id: self.session_id.clone(),
            command: command.clone(),
            timestamp: Utc::now(),
            working_directory,
            exit_code: None,
            duration: None,
            output_size: 0,
        };

        let id = entry.id.clone();
        self.storage.add_entry(entry)?;
        self.cleanup_old_entries()?;

        Ok(id)
    }

    /// Add execution record with detailed information
    pub fn add_execution_record(&mut self, record: ExecutionRecord) -> Result<(), HistoryError> {
        // Convert execution record to history entry
        let entry = HistoryEntry {
            id: record.id,
            session_id: record.session_id,
            command: record.command,
            timestamp: record.start_time,
            working_directory: record.working_directory,
            exit_code: record.exit_code,
            duration: record.duration,
            output_size: record.output.as_ref().map(|s| s.len()).unwrap_or(0),
        };

        self.storage.add_entry(entry)?;
        self.cleanup_old_entries()?;

        Ok(())
    }

    /// Search command history
    pub fn search_history(
        &self,
        criteria: SearchCriteria,
    ) -> Result<Vec<HistoryEntry>, HistoryError> {
        self.storage.search_entries(&criteria)
    }

    /// Get recent commands
    pub fn get_recent_commands(&self, limit: usize) -> Result<Vec<HistoryEntry>, HistoryError> {
        self.storage.get_recent_entries(&self.session_id, limit)
    }

    /// Get command by ID
    pub fn get_command(&self, id: &str) -> Result<Option<HistoryEntry>, HistoryError> {
        self.storage.get_entry(id)
    }

    /// Get history statistics
    pub fn get_stats(&self) -> Result<HistoryStats, HistoryError> {
        self.storage.get_stats(Some(&self.session_id))
    }

    /// Clear current session history
    pub fn clear_session_history(&mut self) -> Result<(), HistoryError> {
        self.storage.clear_session(&self.session_id)
    }

    /// Get total command count for current session
    pub fn get_command_count(&self) -> Result<usize, HistoryError> {
        self.storage.count_entries(&self.session_id)
    }

    /// Clean up old entries if limit exceeded
    fn cleanup_old_entries(&mut self) -> Result<(), HistoryError> {
        let total_count = self.storage.count_entries(&self.session_id)?;

        if total_count > self.max_history_size {
            let excess = total_count - self.max_history_size;
            self.storage
                .remove_oldest_entries(&self.session_id, excess)?;
        }

        Ok(())
    }

    /// Set maximum history size
    pub fn set_max_history_size(&mut self, size: usize) {
        self.max_history_size = size;
    }

    /// Get current session ID
    pub fn get_session_id(&self) -> &str {
        &self.session_id
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_command() -> ShellCommand {
        ShellCommand {
            executable: "ls".to_string(),
            args: vec!["-la".to_string()],
            working_dir: None,
            env_vars: HashMap::new(),
            stdin_input: None,
            use_pipes: false,
            redirect_output: None,
        }
    }

    #[test]
    fn test_in_memory_storage() {
        let mut storage = InMemoryHistoryStorage::new(100);
        let entry = HistoryEntry {
            id: "test-1".to_string(),
            session_id: "session-1".to_string(),
            command: create_test_command(),
            timestamp: Utc::now(),
            working_directory: "/tmp".to_string(),
            exit_code: Some(0),
            duration: Some(Duration::from_millis(100)),
            output_size: 256,
        };

        storage.add_entry(entry.clone()).unwrap();

        let retrieved = storage.get_entry("test-1").unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().id, "test-1");
    }

    #[test]
    fn test_search_functionality() {
        let mut storage = InMemoryHistoryStorage::new(100);

        // Add test entries
        for i in 0..5 {
            let entry = HistoryEntry {
                id: format!("test-{}", i),
                session_id: "session-1".to_string(),
                command: create_test_command(),
                timestamp: Utc::now(),
                working_directory: "/tmp".to_string(),
                exit_code: Some(0),
                duration: Some(Duration::from_millis(100)),
                output_size: 256,
            };
            storage.add_entry(entry).unwrap();
        }

        let criteria = SearchCriteria {
            query: Some("ls".to_string()),
            command_name: None,
            working_directory: None,
            session_id: Some("session-1".to_string()),
            date_range: None,
            exit_code: None,
            limit: Some(3),
        };

        let results = storage.search_entries(&criteria).unwrap();
        assert_eq!(results.len(), 3);
    }

    #[test]
    fn test_history_manager() {
        let mut manager =
            CommandHistoryManager::with_memory_storage("test-session".to_string(), 100);
        let command = create_test_command();

        let id = manager.add_command(&command, "/tmp".to_string()).unwrap();
        assert!(!id.is_empty());

        let recent = manager.get_recent_commands(10).unwrap();
        assert_eq!(recent.len(), 1);
        assert_eq!(recent[0].command.executable, "ls");
    }

    #[test]
    fn test_stats_calculation() {
        let mut manager =
            CommandHistoryManager::with_memory_storage("test-session".to_string(), 100);

        // Add some test commands
        for _ in 0..3 {
            manager
                .add_command(&create_test_command(), "/tmp".to_string())
                .unwrap();
        }

        let stats = manager.get_stats().unwrap();
        assert_eq!(stats.total_commands, 3);
        assert_eq!(stats.unique_commands, 1);
        assert_eq!(stats.most_used_commands[0].0, "ls");
        assert_eq!(stats.most_used_commands[0].1, 3);
    }
}
