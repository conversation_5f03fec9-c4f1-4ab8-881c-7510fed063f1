// Security checker module - Identifies dangerous commands and provides safety checks
// Implements comprehensive security analysis for command execution

use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use thiserror::Error;

use super::command_parser::ShellCommand;

/// Risk levels for command safety assessment
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// Safety check result
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SafetyCheck {
    pub is_safe: bool,
    pub risk_level: RiskLevel,
    pub warnings: Vec<String>,
    pub recommendations: Vec<String>,
    pub requires_confirmation: bool,
}

/// Risk pattern for regex-based command analysis
#[derive(Debug, Clone)]
pub struct RiskPattern {
    pub pattern: Regex,
    pub risk_level: RiskLevel,
    pub description: String,
    pub category: RiskCategory,
}

/// Categories of security risks
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum RiskCategory {
    FileSystem,
    Network,
    System,
    Process,
    Privileges,
    Unknown,
}

/// Security errors
#[derive(Debug, Error)]
pub enum SecurityError {
    #[error("Critical security violation: {message}")]
    CriticalViolation { message: String },

    #[error("Security check failed: {reason}")]
    CheckFailed { reason: String },

    #[error("Unknown command risk: {command}")]
    UnknownRisk { command: String },
}

/// Main security checker
pub struct SecurityChecker {
    dangerous_commands: HashSet<String>,
    risk_patterns: Vec<RiskPattern>,
    privilege_commands: HashSet<String>,
    filesystem_patterns: Vec<Regex>,
    system_critical_paths: HashSet<String>,
}

impl SecurityChecker {
    /// Create new security checker with default rules
    pub fn new() -> Self {
        let mut dangerous_commands = HashSet::new();
        // File system operations
        dangerous_commands.insert("rm".to_string());
        dangerous_commands.insert("rmdir".to_string());
        dangerous_commands.insert("dd".to_string());
        dangerous_commands.insert("shred".to_string());
        dangerous_commands.insert("wipe".to_string());

        // System operations
        dangerous_commands.insert("mkfs".to_string());
        dangerous_commands.insert("fdisk".to_string());
        dangerous_commands.insert("parted".to_string());
        dangerous_commands.insert("gdisk".to_string());

        // System control
        dangerous_commands.insert("shutdown".to_string());
        dangerous_commands.insert("reboot".to_string());
        dangerous_commands.insert("halt".to_string());
        dangerous_commands.insert("poweroff".to_string());

        // Network operations
        dangerous_commands.insert("iptables".to_string());
        dangerous_commands.insert("ufw".to_string());
        dangerous_commands.insert("firewall-cmd".to_string());

        let mut privilege_commands = HashSet::new();
        privilege_commands.insert("sudo".to_string());
        privilege_commands.insert("su".to_string());
        privilege_commands.insert("doas".to_string());

        let risk_patterns = vec![
            // Recursive force deletion
            RiskPattern {
                pattern: Regex::new(r"rm\s+.*(-rf|-fr|--recursive.*--force|--force.*--recursive)")
                    .unwrap(),
                risk_level: RiskLevel::Critical,
                description: "Recursive force deletion - can permanently destroy data".to_string(),
                category: RiskCategory::FileSystem,
            },
            // Root directory operations
            RiskPattern {
                pattern: Regex::new(r"(rm|chmod|chown).*\s+/\s*$").unwrap(),
                risk_level: RiskLevel::Critical,
                description: "Operation on root directory".to_string(),
                category: RiskCategory::FileSystem,
            },
            // System directory operations
            RiskPattern {
                pattern: Regex::new(r"(rm|chmod|chown).*/(?:bin|sbin|etc|usr|var|sys|proc|dev)")
                    .unwrap(),
                risk_level: RiskLevel::High,
                description: "Operation on system directories".to_string(),
                category: RiskCategory::System,
            },
            // Device file operations
            RiskPattern {
                pattern: Regex::new(r">\s*/dev/").unwrap(),
                risk_level: RiskLevel::High,
                description: "Writing to device files".to_string(),
                category: RiskCategory::System,
            },
            // Privilege escalation
            RiskPattern {
                pattern: Regex::new(r"sudo\s+").unwrap(),
                risk_level: RiskLevel::Medium,
                description: "Elevated privileges required".to_string(),
                category: RiskCategory::Privileges,
            },
            // Network operations
            RiskPattern {
                pattern: Regex::new(r"(wget|curl).*\|\s*(bash|sh|zsh|fish)").unwrap(),
                risk_level: RiskLevel::High,
                description: "Downloading and executing remote scripts".to_string(),
                category: RiskCategory::Network,
            },
            // Process killing
            RiskPattern {
                pattern: Regex::new(r"kill\s+-9").unwrap(),
                risk_level: RiskLevel::Medium,
                description: "Force killing processes".to_string(),
                category: RiskCategory::Process,
            },
            // Disk operations
            RiskPattern {
                pattern: Regex::new(r"dd\s+.*of=/dev/").unwrap(),
                risk_level: RiskLevel::Critical,
                description: "Writing directly to disk devices".to_string(),
                category: RiskCategory::System,
            },
        ];

        let filesystem_patterns = vec![
            Regex::new(r"/(?:etc|usr|var|sys|proc|dev)").unwrap(),
            Regex::new(r"~/\.").unwrap(), // Hidden files in home
            Regex::new(r"/home/<USER>/]+/\.").unwrap(), // Hidden files in user directories
        ];

        let mut system_critical_paths = HashSet::new();
        system_critical_paths.insert("/".to_string());
        system_critical_paths.insert("/etc".to_string());
        system_critical_paths.insert("/usr".to_string());
        system_critical_paths.insert("/var".to_string());
        system_critical_paths.insert("/sys".to_string());
        system_critical_paths.insert("/proc".to_string());
        system_critical_paths.insert("/dev".to_string());
        system_critical_paths.insert("/bin".to_string());
        system_critical_paths.insert("/sbin".to_string());

        Self {
            dangerous_commands,
            risk_patterns,
            privilege_commands,
            filesystem_patterns,
            system_critical_paths,
        }
    }

    /// Perform comprehensive safety check on a command
    pub fn check_command_safety(&self, command: &ShellCommand) -> SafetyCheck {
        let mut warnings = Vec::new();
        let mut recommendations = Vec::new();
        let mut risk_level = RiskLevel::Low;

        // Build full command string for pattern matching
        let full_command = format!("{} {}", command.executable, command.args.join(" "));

        // Check if command is in dangerous list
        if self.dangerous_commands.contains(&command.executable) {
            warnings.push(format!(
                "'{}' is a potentially dangerous command",
                command.executable
            ));
            risk_level = risk_level.max(RiskLevel::Medium);
        }

        // Check privilege escalation
        if self.privilege_commands.contains(&command.executable)
            || command
                .args
                .iter()
                .any(|arg| self.privilege_commands.contains(arg))
        {
            warnings.push("This command requires elevated privileges".to_string());
            risk_level = risk_level.max(RiskLevel::Medium);
            recommendations.push(
                "Ensure you understand the implications of running with elevated privileges"
                    .to_string(),
            );
        }

        // Check against risk patterns
        for pattern in &self.risk_patterns {
            if pattern.pattern.is_match(&full_command) {
                warnings.push(format!(
                    "{}: {}",
                    self.category_to_string(&pattern.category),
                    pattern.description
                ));
                risk_level = risk_level.max(pattern.risk_level.clone());
            }
        }

        // Check for system critical paths
        for arg in &command.args {
            if self
                .system_critical_paths
                .iter()
                .any(|path| arg.starts_with(path))
            {
                warnings.push(format!("Operation involves system critical path: {}", arg));
                risk_level = risk_level.max(RiskLevel::High);
            }
        }

        // Check redirection to critical areas
        if let Some(redirect) = &command.redirect_output {
            if self
                .system_critical_paths
                .iter()
                .any(|path| redirect.starts_with(path))
            {
                warnings.push(format!(
                    "Output redirection to system critical path: {}",
                    redirect
                ));
                risk_level = risk_level.max(RiskLevel::High);
            }
        }

        // Generate recommendations based on risk level
        recommendations.extend(self.generate_recommendations(&full_command, &risk_level));

        let is_safe = risk_level < RiskLevel::High;
        let requires_confirmation = risk_level >= RiskLevel::Medium;

        SafetyCheck {
            is_safe,
            risk_level,
            warnings,
            recommendations,
            requires_confirmation,
        }
    }

    /// Check if a path is in system critical directories
    pub fn is_critical_path(&self, path: &str) -> bool {
        self.system_critical_paths
            .iter()
            .any(|critical| path.starts_with(critical))
    }

    /// Get risk level for a specific command
    pub fn get_command_risk_level(&self, command: &str) -> RiskLevel {
        if self.dangerous_commands.contains(command) {
            RiskLevel::Medium
        } else if self.privilege_commands.contains(command) {
            RiskLevel::Medium
        } else {
            RiskLevel::Low
        }
    }

    /// Generate contextual recommendations
    fn generate_recommendations(&self, command: &str, risk_level: &RiskLevel) -> Vec<String> {
        let mut recommendations = Vec::new();

        match risk_level {
            RiskLevel::Critical => {
                recommendations.push(
                    "⚠️  CRITICAL: This command can cause irreversible system damage".to_string(),
                );
                recommendations
                    .push("📋 Create a full system backup before proceeding".to_string());
                recommendations
                    .push("🧪 Test this command in a safe environment first".to_string());
                recommendations.push("👀 Double-check all parameters and paths".to_string());
            }
            RiskLevel::High => {
                recommendations
                    .push("⚠️  HIGH RISK: This command can cause significant damage".to_string());
                recommendations.push("💾 Back up important data before proceeding".to_string());
                recommendations.push("🔍 Verify all paths and parameters carefully".to_string());
                recommendations
                    .push("🚫 Consider using safer alternatives if available".to_string());
            }
            RiskLevel::Medium => {
                recommendations
                    .push("⚠️  MODERATE RISK: Review this command carefully".to_string());
                recommendations.push("🔒 Ensure you have necessary permissions".to_string());
                recommendations.push("📖 Understand what this command will do".to_string());
            }
            RiskLevel::Low => {
                // No additional recommendations for low risk
            }
        }

        // Command-specific recommendations
        if command.contains("rm") && command.contains("-r") {
            recommendations
                .push("💡 Consider using 'ls' first to see what will be deleted".to_string());
        }

        if command.contains("chmod") || command.contains("chown") {
            recommendations.push("💡 Use 'ls -la' to check current permissions first".to_string());
        }

        if command.contains("sudo") {
            recommendations.push("🔐 Only use sudo when absolutely necessary".to_string());
        }

        recommendations
    }

    /// Convert risk category to string
    fn category_to_string(&self, category: &RiskCategory) -> &'static str {
        match category {
            RiskCategory::FileSystem => "File System",
            RiskCategory::Network => "Network",
            RiskCategory::System => "System",
            RiskCategory::Process => "Process",
            RiskCategory::Privileges => "Privileges",
            RiskCategory::Unknown => "Unknown",
        }
    }

    /// Add custom dangerous command
    pub fn add_dangerous_command(&mut self, command: String) {
        self.dangerous_commands.insert(command);
    }

    /// Remove dangerous command from list
    pub fn remove_dangerous_command(&mut self, command: &str) {
        self.dangerous_commands.remove(command);
    }

    /// Add custom risk pattern
    pub fn add_risk_pattern(&mut self, pattern: RiskPattern) {
        self.risk_patterns.push(pattern);
    }

    /// Get all dangerous commands
    pub fn get_dangerous_commands(&self) -> &HashSet<String> {
        &self.dangerous_commands
    }
}

impl Default for SecurityChecker {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dangerous_command_detection() {
        let checker = SecurityChecker::new();
        let command = ShellCommand {
            executable: "rm".to_string(),
            args: vec!["-rf".to_string(), "/test".to_string()],
            working_dir: None,
            env_vars: std::collections::HashMap::new(),
            stdin_input: None,
            use_pipes: false,
            redirect_output: None,
        };

        let safety = checker.check_command_safety(&command);
        assert!(!safety.is_safe);
        assert!(safety.risk_level >= RiskLevel::Medium);
        assert!(!safety.warnings.is_empty());
    }

    #[test]
    fn test_privilege_escalation_detection() {
        let checker = SecurityChecker::new();
        let command = ShellCommand {
            executable: "sudo".to_string(),
            args: vec!["rm".to_string(), "test.txt".to_string()],
            working_dir: None,
            env_vars: std::collections::HashMap::new(),
            stdin_input: None,
            use_pipes: false,
            redirect_output: None,
        };

        let safety = checker.check_command_safety(&command);
        assert!(safety.requires_confirmation);
        assert!(safety
            .warnings
            .iter()
            .any(|w| w.contains("elevated privileges")));
    }

    #[test]
    fn test_safe_command() {
        let checker = SecurityChecker::new();
        let command = ShellCommand {
            executable: "ls".to_string(),
            args: vec!["-la".to_string()],
            working_dir: None,
            env_vars: std::collections::HashMap::new(),
            stdin_input: None,
            use_pipes: false,
            redirect_output: None,
        };

        let safety = checker.check_command_safety(&command);
        assert!(safety.is_safe);
        assert_eq!(safety.risk_level, RiskLevel::Low);
        assert!(!safety.requires_confirmation);
    }

    #[test]
    fn test_critical_path_detection() {
        let checker = SecurityChecker::new();
        assert!(checker.is_critical_path("/etc/passwd"));
        assert!(checker.is_critical_path("/usr/bin/ls"));
        assert!(!checker.is_critical_path("/home/<USER>/file.txt"));
    }

    #[test]
    fn test_device_file_redirection() {
        let checker = SecurityChecker::new();
        let command = ShellCommand {
            executable: "echo".to_string(),
            args: vec!["test".to_string()],
            working_dir: None,
            env_vars: std::collections::HashMap::new(),
            stdin_input: None,
            use_pipes: false,
            redirect_output: Some("/dev/sda".to_string()),
        };

        let safety = checker.check_command_safety(&command);
        assert!(!safety.is_safe);
        assert!(safety.risk_level >= RiskLevel::High);
    }
}
