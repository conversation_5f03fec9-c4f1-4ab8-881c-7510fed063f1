// Shell interface and utilities
// This module provides shell-specific functionality

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::process::Stdio;
use tokio::io::{AsyncBufReadExt, Async<PERSON><PERSON>Ext, <PERSON>uf<PERSON>eader, BufWriter};
use tokio::process::{Child as TokioChild, Command as TokioCommand};

/// Shell types supported by the terminal
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ShellType {
    Bash,
    Zsh,
    Fish,
    Sh,
    #[cfg(windows)]
    PowerShell,
    #[cfg(windows)]
    Cmd,
    #[cfg(windows)]
    Pwsh,
    Custom(String),
}

impl ShellType {
    /// Create a ShellType from a string
    pub fn from_string(shell: &str) -> Self {
        match shell.to_lowercase().as_str() {
            "bash" => Self::Bash,
            "zsh" => Self::Zsh,
            "fish" => Self::Fish,
            "sh" => Self::Sh,
            #[cfg(windows)]
            "powershell" | "powershell.exe" => Self::PowerShell,
            #[cfg(windows)]
            "cmd" | "cmd.exe" => Self::Cmd,
            #[cfg(windows)]
            "pwsh" | "pwsh.exe" => Self::Pwsh,
            _ => Self::Custom(shell.to_string()),
        }
    }

    /// Get the executable path for this shell
    pub fn get_executable(&self) -> String {
        match self {
            Self::Bash => "bash".to_string(),
            Self::Zsh => "zsh".to_string(),
            Self::Fish => "fish".to_string(),
            Self::Sh => "sh".to_string(),
            #[cfg(windows)]
            Self::PowerShell => "powershell.exe".to_string(),
            #[cfg(windows)]
            Self::Cmd => "cmd.exe".to_string(),
            #[cfg(windows)]
            Self::Pwsh => "pwsh.exe".to_string(),
            Self::Custom(shell) => shell.clone(),
        }
    }

    /// Get default arguments for this shell
    pub fn get_default_args(&self) -> Vec<String> {
        match self {
            Self::Bash => vec!["-l".to_string()], // Login shell
            Self::Zsh => vec!["-l".to_string()],  // Login shell
            Self::Fish => vec![],
            Self::Sh => vec!["-l".to_string()],
            #[cfg(windows)]
            Self::PowerShell => vec!["-NoExit".to_string()],
            #[cfg(windows)]
            Self::Cmd => vec![],
            #[cfg(windows)]
            Self::Pwsh => vec!["-NoExit".to_string()],
            Self::Custom(_) => vec![],
        }
    }

    /// Check if this shell supports login mode
    pub fn supports_login(&self) -> bool {
        matches!(self, Self::Bash | Self::Zsh | Self::Sh)
    }

    /// Get the name of the shell
    pub fn name(&self) -> &str {
        match self {
            Self::Bash => "Bash",
            Self::Zsh => "Zsh",
            Self::Fish => "Fish",
            Self::Sh => "Sh",
            #[cfg(windows)]
            Self::PowerShell => "PowerShell",
            #[cfg(windows)]
            Self::Cmd => "Command Prompt",
            #[cfg(windows)]
            Self::Pwsh => "PowerShell Core",
            Self::Custom(shell) => shell,
        }
    }
}

/// Shell configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellConfig {
    pub shell_type: ShellType,
    pub executable_path: String,
    pub args: Vec<String>,
    pub env_vars: HashMap<String, String>,
    pub working_dir: Option<String>,
}

impl ShellConfig {
    /// Create a new shell configuration
    pub fn new(shell_type: ShellType) -> Self {
        let executable_path = shell_type.get_executable();
        let args = shell_type.get_default_args();

        Self {
            shell_type,
            executable_path,
            args,
            env_vars: HashMap::new(),
            working_dir: None,
        }
    }

    /// Create a shell configuration from a shell string
    pub fn from_shell_string(shell: &str) -> Self {
        let shell_type = ShellType::from_string(shell);
        Self::new(shell_type)
    }

    /// Add an environment variable
    pub fn with_env(mut self, key: String, value: String) -> Self {
        self.env_vars.insert(key, value);
        self
    }

    /// Add multiple environment variables
    pub fn with_envs(mut self, envs: HashMap<String, String>) -> Self {
        self.env_vars.extend(envs);
        self
    }

    /// Set custom arguments
    pub fn with_args(mut self, args: Vec<String>) -> Self {
        self.args = args;
        self
    }

    /// Set working directory
    pub fn with_working_dir(mut self, dir: String) -> Self {
        self.working_dir = Some(dir);
        self
    }
}

/// Shell status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ShellStatus {
    Starting,
    Running,
    Terminated,
    Error(String),
}

/// Shell process wrapper
#[derive(Debug)]
pub struct ShellProcess {
    process: TokioChild,
    config: ShellConfig,
    status: ShellStatus,
    // Note: In a full implementation, these would be properly initialized
    // with the process stdin/stdout/stderr handles
}

impl ShellProcess {
    /// Create a new shell process
    pub async fn new(config: ShellConfig) -> Result<Self, ShellError> {
        let mut cmd = TokioCommand::new(&config.executable_path);

        // Add arguments
        if !config.args.is_empty() {
            cmd.args(&config.args);
        }

        // Set working directory
        if let Some(ref dir) = config.working_dir {
            cmd.current_dir(dir);
        }

        // Set environment variables
        let env = setup_shell_environment(&config);
        cmd.envs(&env);

        // Configure stdio
        cmd.stdin(Stdio::piped());
        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        // Spawn the process
        let process = cmd.spawn().map_err(|e| ShellError::StartupFailed {
            reason: e.to_string(),
        })?;

        Ok(Self {
            process,
            config,
            status: ShellStatus::Starting,
        })
    }

    /// Send command to the shell
    pub async fn send_command(&mut self, command: &str) -> Result<(), ShellError> {
        if let Some(stdin) = self.process.stdin.as_mut() {
            stdin.write_all(command.as_bytes()).await.map_err(|e| {
                ShellError::CommunicationError {
                    message: e.to_string(),
                }
            })?;
            stdin
                .write_all(b"\n")
                .await
                .map_err(|e| ShellError::CommunicationError {
                    message: e.to_string(),
                })?;
            stdin
                .flush()
                .await
                .map_err(|e| ShellError::CommunicationError {
                    message: e.to_string(),
                })?;
        }
        Ok(())
    }

    /// Get process ID
    pub fn pid(&self) -> Option<u32> {
        self.process.id()
    }

    /// Get shell status
    pub fn status(&self) -> &ShellStatus {
        &self.status
    }

    /// Update status
    pub fn update_status(&mut self, status: ShellStatus) {
        self.status = status;
    }

    /// Kill the shell process
    pub async fn kill(&mut self) -> Result<(), ShellError> {
        self.process
            .kill()
            .await
            .map_err(|e| ShellError::CommunicationError {
                message: e.to_string(),
            })?;
        self.status = ShellStatus::Terminated;
        Ok(())
    }
}

/// Shell manager for handling multiple shell instances
#[derive(Debug, Default)]
pub struct ShellManager {
    processes: HashMap<String, ShellProcess>,
    default_config: Option<ShellConfig>,
}

impl ShellManager {
    /// Create a new shell manager
    pub fn new() -> Self {
        Self {
            processes: HashMap::new(),
            default_config: None,
        }
    }

    /// Set default shell configuration
    pub fn set_default_config(&mut self, config: ShellConfig) {
        self.default_config = Some(config);
    }

    /// Create a new shell instance
    pub async fn create_shell(
        &mut self,
        id: String,
        config: Option<ShellConfig>,
    ) -> Result<(), ShellError> {
        if self.processes.contains_key(&id) {
            return Err(ShellError::InvalidConfiguration(
                "Shell instance already exists".to_string(),
            ));
        }

        let shell_config = config.unwrap_or_else(|| {
            self.default_config
                .clone()
                .unwrap_or_else(|| ShellConfig::new(ShellDetector::detect_default_shell()))
        });

        let shell_process = ShellProcess::new(shell_config).await?;
        self.processes.insert(id, shell_process);

        Ok(())
    }

    /// Terminate a shell instance
    pub async fn terminate_shell(&mut self, id: &str) -> Result<(), ShellError> {
        if let Some(mut shell) = self.processes.remove(id) {
            shell.kill().await?;
        }
        Ok(())
    }

    /// Get shell status
    pub fn get_shell_status(&self, id: &str) -> Option<ShellStatus> {
        self.processes.get(id).map(|shell| shell.status().clone())
    }

    /// Send input to a shell
    pub async fn send_input(&mut self, id: &str, input: &str) -> Result<(), ShellError> {
        if let Some(shell) = self.processes.get_mut(id) {
            shell.send_command(input).await?;
        } else {
            return Err(ShellError::NotAvailable(format!(
                "Shell instance '{}' not found",
                id
            )));
        }
        Ok(())
    }

    /// List all shell instances
    pub fn list_shells(&self) -> Vec<String> {
        self.processes.keys().cloned().collect()
    }

    /// Handle shell crash
    pub async fn handle_shell_crash(&mut self, id: &str) -> Result<(), ShellError> {
        // Remove the crashed shell
        if let Some(mut shell) = self.processes.remove(id) {
            let _ = shell.kill().await;
        }

        // Attempt to restart with same configuration
        if let Some(config) = &self.default_config {
            let shell_process = ShellProcess::new(config.clone()).await?;
            self.processes.insert(id.to_string(), shell_process);
        }

        Ok(())
    }

    /// Health check for all shells
    pub fn health_check(&self) -> Vec<(String, ShellStatus)> {
        self.processes
            .iter()
            .map(|(id, shell)| (id.clone(), shell.status().clone()))
            .collect()
    }
}

/// Setup shell environment variables
pub fn setup_shell_environment(config: &ShellConfig) -> HashMap<String, String> {
    let mut env = std::env::vars().collect::<HashMap<_, _>>();

    // Add custom environment variables
    env.extend(config.env_vars.clone());

    // Set shell-specific environment variables
    match config.shell_type {
        ShellType::Bash => {
            env.insert("BASH_ENV".to_string(), "~/.bashrc".to_string());
        }
        ShellType::Zsh => {
            env.insert("ZDOTDIR".to_string(), "~/.config/zsh".to_string());
        }
        _ => {}
    }

    env
}

/// Shell detector for finding available shells on the system
pub struct ShellDetector;

impl ShellDetector {
    /// Detect the default shell for the current user
    pub fn detect_default_shell() -> ShellType {
        #[cfg(unix)]
        {
            // Try to get shell from environment variable
            if let Ok(shell) = std::env::var("SHELL") {
                let shell_path = PathBuf::from(shell);
                let shell_name = shell_path
                    .file_name()
                    .and_then(|s| s.to_str())
                    .unwrap_or("sh");
                return ShellType::from_string(shell_name);
            }

            // Fallback to common shells
            ShellType::Bash
        }

        #[cfg(windows)]
        {
            // On Windows, prefer PowerShell if available, otherwise cmd
            if Self::is_shell_available(&ShellType::Pwsh) {
                ShellType::Pwsh
            } else if Self::is_shell_available(&ShellType::PowerShell) {
                ShellType::PowerShell
            } else {
                ShellType::Cmd
            }
        }
    }

    /// Check if a shell is available on the system
    pub fn is_shell_available(shell_type: &ShellType) -> bool {
        let executable = shell_type.get_executable();

        // Try to find the executable in PATH
        if let Ok(path) = which::which(&executable) {
            path.exists()
        } else {
            false
        }
    }

    /// Get all available shells on the system
    pub fn get_available_shells() -> Vec<ShellType> {
        let potential_shells = vec![
            ShellType::Bash,
            ShellType::Zsh,
            ShellType::Fish,
            ShellType::Sh,
            #[cfg(windows)]
            ShellType::PowerShell,
            #[cfg(windows)]
            ShellType::Cmd,
            #[cfg(windows)]
            ShellType::Pwsh,
        ];

        potential_shells
            .into_iter()
            .filter(|shell| Self::is_shell_available(shell))
            .collect()
    }

    /// Get detailed information about a shell
    pub fn get_shell_info(shell_type: &ShellType) -> Result<ShellInfo, ShellError> {
        let executable = shell_type.get_executable();
        let available = Self::is_shell_available(shell_type);

        let version = if available {
            Self::get_shell_version(&executable).unwrap_or_else(|_| "Unknown".to_string())
        } else {
            "N/A".to_string()
        };

        Ok(ShellInfo {
            shell_type: shell_type.clone(),
            executable,
            version,
            available,
        })
    }

    /// Get the version of a shell executable
    fn get_shell_version(executable: &str) -> Result<String, ShellError> {
        use std::process::Command;

        let output = Command::new(executable)
            .arg("--version")
            .output()
            .map_err(|e| ShellError::VersionCheckFailed(e.to_string()))?;

        if output.status.success() {
            let version_str = String::from_utf8_lossy(&output.stdout);
            let first_line = version_str.lines().next().unwrap_or("Unknown");
            Ok(first_line.to_string())
        } else {
            Err(ShellError::VersionCheckFailed("Command failed".to_string()))
        }
    }
}

/// Shell information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellInfo {
    pub shell_type: ShellType,
    pub executable: String,
    pub version: String,
    pub available: bool,
}

/// Shell error types
#[derive(Debug, thiserror::Error)]
pub enum ShellError {
    #[error("Shell executable not found: {path}")]
    ExecutableNotFound { path: String },

    #[error("Failed to start shell process: {reason}")]
    StartupFailed { reason: String },

    #[error("Shell process terminated unexpectedly")]
    UnexpectedTermination,

    #[error("Communication error: {message}")]
    CommunicationError { message: String },

    #[error("Permission denied: {details}")]
    PermissionDenied { details: String },

    #[error("Shell not available: {0}")]
    NotAvailable(String),

    #[error("Version check failed: {0}")]
    VersionCheckFailed(String),

    #[error("Invalid shell configuration: {0}")]
    InvalidConfiguration(String),
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_shell_type_from_string() {
        assert_eq!(ShellType::from_string("bash"), ShellType::Bash);
        assert_eq!(ShellType::from_string("zsh"), ShellType::Zsh);
        assert_eq!(ShellType::from_string("fish"), ShellType::Fish);

        #[cfg(windows)]
        {
            assert_eq!(ShellType::from_string("powershell"), ShellType::PowerShell);
            assert_eq!(ShellType::from_string("cmd"), ShellType::Cmd);
        }
    }

    #[test]
    fn test_shell_config_creation() {
        let config = ShellConfig::new(ShellType::Bash);
        assert_eq!(config.shell_type, ShellType::Bash);
        assert_eq!(config.executable_path, "bash");
        assert!(config.shell_type.supports_login());
    }
}
