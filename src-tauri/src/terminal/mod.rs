// Terminal module - Core terminal functionality
// This module handles PTY creation, process management, and IO operations

pub mod command_executor;
pub mod command_parser;
pub mod commands;
pub mod execution_commands;
pub mod history;
pub mod io_handler;
pub mod platform;
pub mod process;
pub mod pty;
pub mod security;
pub mod shell;
pub mod shell_commands;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use uuid::Uuid;

// Re-export public types
pub use command_executor::*;
pub use command_parser::*;
pub use commands::*;
pub use execution_commands::*;
pub use history::*;
pub use io_handler::*;
pub use process::*;
pub use pty::*;
pub use security::*;
pub use shell::*;
pub use shell_commands::*;

/// Terminal configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalConfig {
    pub shell: String,
    pub working_directory: String,
    pub environment: HashMap<String, String>,
    pub rows: u16,
    pub cols: u16,
}

impl Default for TerminalConfig {
    fn default() -> Self {
        Self {
            shell: if cfg!(windows) {
                "powershell".to_string()
            } else {
                "zsh".to_string()
            },
            working_directory: dirs::home_dir()
                .unwrap_or_else(|| std::path::PathBuf::from("/"))
                .to_string_lossy()
                .to_string(),
            environment: std::env::vars().collect(),
            rows: 24,
            cols: 80,
        }
    }
}

/// Terminal instance
#[derive(Debug)]
pub struct Terminal {
    pub id: String,
    pub config: TerminalConfig,
    pub pty: Option<Arc<Mutex<Box<dyn PtyInterface + Send>>>>,
    pub process: Option<Arc<Mutex<ProcessManager>>>,
    pub created_at: std::time::SystemTime,
    pub last_activity: std::time::SystemTime,
}

impl Terminal {
    /// Create a new terminal instance
    pub fn new(config: TerminalConfig) -> Self {
        let id = Uuid::new_v4().to_string();
        let now = std::time::SystemTime::now();

        Self {
            id,
            config,
            pty: None,
            process: None,
            created_at: now,
            last_activity: now,
        }
    }

    /// Start the terminal (create PTY and spawn shell)
    pub async fn start(&mut self) -> Result<(), TerminalError> {
        // Create PTY
        let mut pty = create_pty(&self.config)?;

        // Start shell process
        let mut process_manager = ProcessManager::new();
        let _pid = process_manager.spawn_shell(&self.config, &mut *pty).await?;

        // Store PTY and process manager
        self.pty = Some(Arc::new(Mutex::new(pty)));
        self.process = Some(Arc::new(Mutex::new(process_manager)));

        self.update_activity();

        Ok(())
    }

    /// Send input to the terminal
    pub async fn send_input(&mut self, input: &str) -> Result<(), TerminalError> {
        if let Some(pty) = &self.pty {
            {
                let mut pty_guard = pty.lock().map_err(|_| TerminalError::LockError)?;
                pty_guard.write_input(input.as_bytes())?;
            }
            self.update_activity();
            Ok(())
        } else {
            Err(TerminalError::NotStarted)
        }
    }

    /// Read output from the terminal
    pub async fn read_output(&mut self) -> Result<Vec<u8>, TerminalError> {
        if let Some(pty) = &self.pty {
            let output = {
                let mut pty_guard = pty.lock().map_err(|_| TerminalError::LockError)?;
                pty_guard.read_output()?
            };
            if !output.is_empty() {
                self.update_activity();
            }
            Ok(output)
        } else {
            Err(TerminalError::NotStarted)
        }
    }

    /// Kill the terminal
    pub async fn kill(&mut self) -> Result<(), TerminalError> {
        if let Some(process) = &self.process {
            let mut process_guard = process.lock().map_err(|_| TerminalError::LockError)?;
            process_guard.kill_all().await?;
        }

        self.pty = None;
        self.process = None;

        Ok(())
    }

    /// Check if terminal is alive
    pub fn is_alive(&self) -> bool {
        self.pty.is_some() && self.process.is_some()
    }

    /// Update last activity timestamp
    fn update_activity(&mut self) {
        self.last_activity = std::time::SystemTime::now();
    }
}

/// Terminal manager for handling multiple terminal instances
#[derive(Debug, Default)]
pub struct TerminalManager {
    terminals: HashMap<String, Terminal>,
}

impl TerminalManager {
    pub fn new() -> Self {
        Self {
            terminals: HashMap::new(),
        }
    }

    /// Create a new terminal
    pub async fn create_terminal(
        &mut self,
        config: TerminalConfig,
    ) -> Result<String, TerminalError> {
        let mut terminal = Terminal::new(config);
        let id = terminal.id.clone();

        terminal.start().await?;

        self.terminals.insert(id.clone(), terminal);

        Ok(id)
    }

    /// Get terminal by ID
    pub fn get_terminal(&mut self, id: &str) -> Option<&mut Terminal> {
        self.terminals.get_mut(id)
    }

    /// Remove terminal
    pub async fn remove_terminal(&mut self, id: &str) -> Result<(), TerminalError> {
        if let Some(mut terminal) = self.terminals.remove(id) {
            terminal.kill().await?;
        }
        Ok(())
    }

    /// Get all terminal IDs
    pub fn list_terminals(&self) -> Vec<String> {
        self.terminals.keys().cloned().collect()
    }

    /// Clean up inactive terminals
    pub async fn cleanup_inactive(
        &mut self,
        max_idle_duration: std::time::Duration,
    ) -> Result<(), TerminalError> {
        let now = std::time::SystemTime::now();
        let mut to_remove = Vec::new();

        for (id, terminal) in &self.terminals {
            if let Ok(duration) = now.duration_since(terminal.last_activity) {
                if duration > max_idle_duration && !terminal.is_alive() {
                    to_remove.push(id.clone());
                }
            }
        }

        for id in to_remove {
            self.remove_terminal(&id).await?;
        }

        Ok(())
    }
}

/// Terminal-related errors
#[derive(Debug, thiserror::Error)]
pub enum TerminalError {
    #[error("PTY error: {0}")]
    PtyError(#[from] PtyError),

    #[error("Process error: {0}")]
    ProcessError(#[from] ProcessError),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Terminal not started")]
    NotStarted,

    #[error("Lock error")]
    LockError,

    #[error("Terminal not found")]
    NotFound,

    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),
}
