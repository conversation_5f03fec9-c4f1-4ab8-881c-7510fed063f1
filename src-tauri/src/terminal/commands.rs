// Tauri commands for terminal functionality
// This module provides the command interface between frontend and terminal backend

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tauri::{command, Emitter, State, Window};
use tokio::sync::{mpsc, Mutex};
use uuid;

use super::{Async<PERSON>Hand<PERSON>, PtyConfig, PtyManager};

// Import AppState from the parent crate
use crate::AppState;

/// Terminal state management
pub struct TerminalState {
    pub pty_manager: Arc<PtyManager>,
    pub io_handlers: Arc<Mutex<HashMap<String, tokio::task::JoinHandle<()>>>>,
    // 新增：保存输入通道，用于向终端发送数据
    pub input_channels: Arc<Mutex<HashMap<String, mpsc::UnboundedSender<Vec<u8>>>>>,
}

impl TerminalState {
    pub fn new() -> Self {
        Self {
            pty_manager: Arc::new(PtyManager::new()),
            io_handlers: Arc::new(Mutex::new(HashMap::new())),
            input_channels: Arc::new(Mutex::new(HashMap::new())),
        }
    }
}

/// 创建终端请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTerminalRequest {
    pub shell: Option<String>,
    pub cwd: Option<String>,
    pub env: Option<HashMap<String, String>>,
    pub rows: Option<u16>,
    pub cols: Option<u16>,
}

/// 终端响应
#[derive(Debug, Serialize, Deserialize)]
pub struct TerminalResponse {
    pub id: String,
    pub success: bool,
    pub message: Option<String>,
}

/// 调整大小请求
#[derive(Debug, Serialize, Deserialize)]
pub struct ResizeRequest {
    pub rows: u16,
    pub cols: u16,
}

/// 创建新终端
#[command]
pub async fn create_terminal(
    request: CreateTerminalRequest,
    state: State<'_, AppState>,
    window: Window,
) -> Result<TerminalResponse, String> {
    use super::PtySizeConfig;

    let shell = request.shell.unwrap_or_else(|| get_default_shell());
    log::info!("Creating terminal with shell: {}", shell);

    // 检查shell是否存在
    if !std::path::Path::new(&shell).exists() {
        let error_msg = format!("Shell文件不存在: {}", shell);
        log::error!("{}", error_msg);
        return Err(error_msg);
    }

    let config = PtyConfig {
        shell: shell.clone(),
        cwd: request.cwd.clone(),
        env: request.env.unwrap_or_default(),
        size: PtySizeConfig {
            rows: request.rows.unwrap_or(24),
            cols: request.cols.unwrap_or(80),
            pixel_width: 0,
            pixel_height: 0,
        },
        args: vec![],
    };

    log::debug!(
        "PTY config: shell={}, cwd={:?}, size={}x{}",
        config.shell,
        config.cwd,
        config.size.cols,
        config.size.rows
    );
    match state.terminal_state.pty_manager.create_pty(config).await {
        Ok(id) => {
            log::info!("PTY created successfully with ID: {}", id);

            // 启动 IO 处理循环
            let result = start_terminal_io_handler(
                id.clone(),
                state.terminal_state.pty_manager.clone(),
                state.terminal_state.io_handlers.clone(),
                state.terminal_state.input_channels.clone(),
                window.clone(),
            )
            .await;

            match result {
                Ok(_) => {
                    log::info!("Terminal IO handler started for: {}", id);

                    // 发送测试消息以验证连接
                    let test_window = window.clone();
                    let test_terminal_id = id.clone();
                    tokio::spawn(async move {
                        // 等待一小段时间让IO处理器启动
                        tokio::time::sleep(std::time::Duration::from_millis(200)).await;

                        // 发送测试消息
                        // if let Err(e) = test_window.emit(
                        //     "terminal-output",
                        //     serde_json::json!({
                        //         "terminal_id": test_terminal_id,
                        //         "data": "TAgent 终端已连接\r\n"
                        //     }),
                        // ) {
                        //     log::error!("发送测试消息失败: {}", e);
                        // }
                    });

                    Ok(TerminalResponse {
                        id,
                        success: true,
                        message: None,
                    })
                }
                Err(e) => {
                    log::error!("启动 IO 处理器失败: {}", e);
                    // 如果 IO 处理器启动失败，清理 PTY
                    let _ = state.terminal_state.pty_manager.kill_pty(&id).await;
                    Err(format!("启动 IO 处理器失败: {}", e))
                }
            }
        }
        Err(e) => {
            log::error!("创建终端失败: {} (shell: {})", e, shell);
            Err(format!("创建终端失败: {}", e))
        }
    }
}

/// 写入数据到终端
#[command(rename_all = "snake_case")]
pub async fn write_to_terminal(
    terminal_id: String,
    data: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    log::info!("Writing to terminal {}: {:?}", terminal_id, data);

    // 尝试使用输入通道发送数据
    // {
    //     let channels = state.terminal_state.input_channels.lock().await;
    //     if let Some(input_tx) = channels.get(&terminal_id) {
    //         match input_tx.send(data.as_bytes().to_vec()) {
    //             Ok(_) => {
    //                 log::debug!("Data sent via input channel for terminal: {}", terminal_id);
    //                 return Ok(true);
    //             }
    //             Err(e) => {
    //                 log::error!(
    //                     "Failed to send via input channel for terminal {}: {}",
    //                     terminal_id,
    //                     e
    //                 );
    //                 // 继续尝试直接写入PTY作为备用方案
    //             }
    //         }
    //     } else {
    //         log::warn!("No input channel found for terminal: {}", terminal_id);
    //         // 继续尝试直接写入PTY作为备用方案
    //     }
    // }

    // 备用方案：直接写入PTY
    match state
        .terminal_state
        .pty_manager
        .write_to_pty(&terminal_id, data.as_bytes())
        .await
    {
        Ok(_) => {
            log::debug!("Data written directly to PTY for terminal: {}", terminal_id);
            Ok(true)
        }
        Err(e) => {
            log::error!("Failed to write to terminal {}: {}", terminal_id, e);
            Err(format!("写入终端失败: {}", e))
        }
    }
}

/// 调整终端大小
#[command(rename_all = "snake_case")]
pub async fn resize_terminal(
    terminal_id: String,
    request: ResizeRequest,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    use portable_pty::PtySize;

    let size = PtySize {
        rows: request.rows,
        cols: request.cols,
        pixel_width: 0,
        pixel_height: 0,
    };

    match state
        .terminal_state
        .pty_manager
        .resize_pty(&terminal_id, size)
        .await
    {
        Ok(_) => Ok(true),
        Err(e) => Err(format!("调整终端大小失败: {}", e)),
    }
}

/// 关闭终端
#[command(rename_all = "snake_case")]
pub async fn kill_terminal(
    terminal_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    // 停止 IO 处理器
    {
        let mut handlers = state.terminal_state.io_handlers.lock().await;
        if let Some(handle) = handlers.remove(&format!("{}_output", terminal_id)) {
            handle.abort();
        }
        if let Some(handle) = handlers.remove(&format!("{}_io", terminal_id)) {
            handle.abort();
        }
    }

    // 清理输入通道
    {
        let mut channels = state.terminal_state.input_channels.lock().await;
        if let Some(_) = channels.remove(&terminal_id) {
            log::info!("Input channel removed for terminal: {}", terminal_id);
        }
    }

    // 关闭 PTY
    match state
        .terminal_state
        .pty_manager
        .kill_pty(&terminal_id)
        .await
    {
        Ok(_) => Ok(true),
        Err(e) => Err(format!("关闭终端失败: {}", e)),
    }
}

/// 获取终端列表
#[command]
pub async fn list_terminals(state: State<'_, AppState>) -> Result<Vec<String>, String> {
    Ok(state.terminal_state.pty_manager.list_pty_ids())
}

/// 检查终端是否活跃
#[command(rename_all = "snake_case")]
pub async fn is_terminal_alive(
    terminal_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    Ok(state
        .terminal_state
        .pty_manager
        .is_process_alive(&terminal_id)
        .await)
}

/// 启动终端 IO 处理器
async fn start_terminal_io_handler(
    terminal_id: String,
    pty_manager: Arc<PtyManager>,
    handlers: Arc<Mutex<HashMap<String, tokio::task::JoinHandle<()>>>>,
    input_channels: Arc<Mutex<HashMap<String, mpsc::UnboundedSender<Vec<u8>>>>>,
    window: Window,
) -> Result<(), String> {
    log::info!("Starting IO handler for terminal: {}", terminal_id);

    // 验证PTY是否真正存在
    if !pty_manager.has_pty(&terminal_id) {
        let error_msg = format!("PTY {} does not exist", terminal_id);
        log::error!("{}", error_msg);
        return Err(error_msg);
    }

    let (mut io_handler, mut output_rx, input_tx) =
        AsyncIOHandler::new(terminal_id.clone(), window.clone());

    // 保存输入通道到全局状态中
    {
        let mut channels = input_channels.lock().await;
        channels.insert(terminal_id.clone(), input_tx);
        log::info!("Input channel stored for terminal: {}", terminal_id);
    }

    // 启动输出处理任务（简化版本）
    let output_terminal_id = terminal_id.clone();
    let output_window = window.clone();
    let output_handle = tokio::spawn(async move {
        log::debug!(
            "Starting output handler for terminal: {}",
            output_terminal_id
        );
        let mut output_count = 0;

        while let Some(output) = output_rx.recv().await {
            output_count += 1;
            let output_str = String::from_utf8_lossy(&output);

            if output_count % 10 == 1 {
                // 减少日志输出频率
                log::debug!(
                    "Sending output #{} for terminal {}: {} bytes",
                    output_count,
                    output_terminal_id,
                    output.len()
                );
            }

            if let Err(e) = output_window.emit(
                "terminal-output",
                serde_json::json!({
                    "terminal_id": output_terminal_id,
                    "data": output_str
                }),
            ) {
                log::error!("Failed to emit terminal output: {}", e);
                break;
            }
        }
        log::debug!(
            "Output handler terminated for terminal: {}",
            output_terminal_id
        );
    });

    // 启动 IO 循环任务（异步启动，不等待）
    let io_terminal_id = terminal_id.clone();
    let io_pty_manager = pty_manager.clone();
    let io_handle = tokio::spawn(async move {
        log::debug!("Starting IO loop for terminal: {}", io_terminal_id);

        // 添加小延迟确保PTY完全准备好
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        match io_handler.start_io_loop(io_pty_manager).await {
            Ok(_) => {
                log::info!(
                    "IO loop completed successfully for terminal: {}",
                    io_terminal_id
                );
            }
            Err(e) => {
                log::error!("IO loop error for terminal {}: {}", io_terminal_id, e);
            }
        }
        log::debug!("IO loop task terminated for terminal: {}", io_terminal_id);
    });

    // 将任务句柄存储起来以便后续管理
    {
        let mut handlers_guard = handlers.lock().await;
        handlers_guard.insert(format!("{}_output", terminal_id), output_handle);
        handlers_guard.insert(format!("{}_io", terminal_id), io_handle);
    }

    log::info!("IO handler setup completed for terminal: {}", terminal_id);
    Ok(())
}

/// 获取默认 shell
fn get_default_shell() -> String {
    #[cfg(target_os = "windows")]
    {
        std::env::var("COMSPEC").unwrap_or_else(|_| "cmd.exe".to_string())
    }

    #[cfg(target_os = "macos")]
    {
        // macOS 优先使用环境变量中的SHELL，然后按优先级检查存在的shell
        std::env::var("SHELL").unwrap_or_else(|_| {
            // 按优先级尝试不同的 shell，只检查实际存在的路径
            if std::path::Path::new("/bin/zsh").exists() {
                "/bin/zsh".to_string()
            } else if std::path::Path::new("/bin/bash").exists() {
                "/bin/bash".to_string()
            } else {
                "/bin/sh".to_string()
            }
        })
    }

    #[cfg(all(unix, not(target_os = "macos")))]
    {
        std::env::var("SHELL").unwrap_or_else(|_| "/bin/bash".to_string())
    }
}

// Terminal commands are exported directly
// and registered in main.rs using the generate_handler! macro
