// Advanced command execution commands with context tracking and security checks
// Integrated with terminal execution system for enhanced command handling

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tauri::{command, State};
use tokio::sync::Mutex;

use super::{
    command_executor::CommandExecutor, shell::ShellManager, ExecutionContext, ExecutionResult,
    HistoryEntry,
};

// Import AppState from the parent crate
use crate::AppState;

/// Global command execution state
pub struct CommandExecutionState {
    pub executors: Arc<Mutex<HashMap<String, CommandExecutor>>>,
    pub shell_manager: Arc<Mutex<ShellManager>>,
}

impl CommandExecutionState {
    pub fn new() -> Self {
        Self {
            executors: Arc::new(Mutex::new(HashMap::new())),
            shell_manager: Arc::new(Mutex::new(ShellManager::new())),
        }
    }
}

/// Request to execute a command
#[derive(Debug, Serialize, Deserialize)]
pub struct ExecuteCommandRequest {
    pub terminal_id: String,
    pub input: String,
}

/// Response from command execution
#[derive(Debug, Serialize, Deserialize)]
pub struct ExecuteCommandResponse {
    pub success: bool,
    pub result: Option<ExecutionResult>,
    pub error: Option<String>,
}

/// Request to confirm a dangerous command
#[derive(Debug, Serialize, Deserialize)]
pub struct ConfirmCommandRequest {
    pub terminal_id: String,
    pub confirmation_id: String,
    pub confirmed: bool,
}

/// Request to search command history
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchHistoryRequest {
    pub terminal_id: String,
    pub query: Option<String>,
    pub limit: Option<usize>,
}

/// Execute a command using the advanced execution engine
#[command(rename_all = "snake_case")]
pub async fn execute_advanced_command(
    request: ExecuteCommandRequest,
    state: State<'_, AppState>,
) -> Result<ExecuteCommandResponse, String> {
    let mut executors = state.command_execution_state.executors.lock().await;

    // Get or create executor for this terminal
    let executor = executors
        .entry(request.terminal_id.clone())
        .or_insert_with(|| {
            CommandExecutor::new(
                request.terminal_id.clone(),
                state.command_execution_state.shell_manager.clone(),
            )
        });

    // Execute the command
    match executor.execute(&request.input).await {
        Ok(result) => Ok(ExecuteCommandResponse {
            success: true,
            result: Some(result),
            error: None,
        }),
        Err(e) => Ok(ExecuteCommandResponse {
            success: false,
            result: None,
            error: Some(e.to_string()),
        }),
    }
}

/// Confirm or cancel a dangerous command
#[command(rename_all = "snake_case")]
pub async fn confirm_dangerous_command(
    request: ConfirmCommandRequest,
    state: State<'_, AppState>,
) -> Result<ExecuteCommandResponse, String> {
    let mut executors = state.command_execution_state.executors.lock().await;

    if let Some(executor) = executors.get_mut(&request.terminal_id) {
        if request.confirmed {
            // Confirm and execute the command
            match executor.confirm_command(&request.confirmation_id).await {
                Ok(result) => Ok(ExecuteCommandResponse {
                    success: true,
                    result: Some(result),
                    error: None,
                }),
                Err(e) => Ok(ExecuteCommandResponse {
                    success: false,
                    result: None,
                    error: Some(e.to_string()),
                }),
            }
        } else {
            // Cancel the command
            let cancelled = executor.cancel_confirmation(&request.confirmation_id);
            Ok(ExecuteCommandResponse {
                success: cancelled,
                result: None,
                error: if cancelled {
                    None
                } else {
                    Some("Confirmation not found".to_string())
                },
            })
        }
    } else {
        Err("Terminal not found".to_string())
    }
}

/// Get command history for a terminal
#[command(rename_all = "snake_case")]
pub async fn get_advanced_command_history(
    terminal_id: String,
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> Result<Vec<HistoryEntry>, String> {
    let executors = state.command_execution_state.executors.lock().await;

    if let Some(executor) = executors.get(&terminal_id) {
        executor
            .get_command_history(limit.unwrap_or(100))
            .map_err(|e| e.to_string())
    } else {
        Err("Terminal not found".to_string())
    }
}

/// Search command history
#[command(rename_all = "snake_case")]
pub async fn search_advanced_command_history(
    request: SearchHistoryRequest,
    state: State<'_, AppState>,
) -> Result<Vec<HistoryEntry>, String> {
    let executors = state.command_execution_state.executors.lock().await;

    if let Some(executor) = executors.get(&request.terminal_id) {
        executor
            .search_command_history(
                &request.query.unwrap_or_default(),
                request.limit.unwrap_or(50),
            )
            .map_err(|e| e.to_string())
    } else {
        Err("Terminal not found".to_string())
    }
}

/// Get execution context for a terminal
#[command(rename_all = "snake_case")]
pub async fn get_execution_context(
    terminal_id: String,
    state: State<'_, AppState>,
) -> Result<ExecutionContext, String> {
    let executors = state.command_execution_state.executors.lock().await;

    if let Some(executor) = executors.get(&terminal_id) {
        Ok(executor.get_context().clone())
    } else {
        Err("Terminal not found".to_string())
    }
}

/// Update working directory for a terminal
#[command(rename_all = "snake_case")]
pub async fn update_working_directory(
    terminal_id: String,
    directory: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut executors = state.command_execution_state.executors.lock().await;

    if let Some(executor) = executors.get_mut(&terminal_id) {
        executor.set_working_directory(directory);
        Ok(true)
    } else {
        Err("Terminal not found".to_string())
    }
}

/// Get pending confirmations for a terminal
#[command(rename_all = "snake_case")]
pub async fn get_pending_confirmations(
    terminal_id: String,
    state: State<'_, AppState>,
) -> Result<Vec<String>, String> {
    let executors = state.command_execution_state.executors.lock().await;

    if let Some(executor) = executors.get(&terminal_id) {
        let confirmations = executor.get_pending_confirmations();
        Ok(confirmations.iter().map(|c| c.id.clone()).collect())
    } else {
        Err("Terminal not found".to_string())
    }
}

/// Remove executor when terminal is closed
#[command(rename_all = "snake_case")]
pub async fn cleanup_terminal_executor(
    terminal_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut executors = state.command_execution_state.executors.lock().await;
    Ok(executors.remove(&terminal_id).is_some())
}

/// Initialize command execution state
pub fn init_command_execution_state() -> CommandExecutionState {
    CommandExecutionState::new()
}
