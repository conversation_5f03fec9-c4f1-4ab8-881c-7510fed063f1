// Tauri commands for shell management
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager, State};
use tokio::sync::Mutex;

use super::{ShellConfig, ShellDetector, ShellInfo, ShellManager, ShellStatus, ShellType};

// Import AppState from the parent crate
use crate::AppState;

/// Global shell manager state
pub type ShellManagerState = Arc<Mutex<ShellManager>>;

/// Output type for shell events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputType {
    Stdout,
    Stderr,
}

/// Shell output event
#[derive(Clone, Serialize)]
struct ShellOutputEvent {
    id: String,
    output: String,
    output_type: OutputType,
}

/// Shell status change event
#[derive(Clone, Serialize)]
struct ShellStatusEvent {
    id: String,
    status: ShellStatus,
    timestamp: u64,
}

/// Create a new shell instance
#[tauri::command]
pub async fn create_shell_instance(
    app: AppHandle,
    state: State<'_, AppState>,
    id: String,
    config: Option<ShellConfig>,
) -> Result<ShellConfig, String> {
    let mut manager = state.command_execution_state.shell_manager.lock().await;

    manager
        .create_shell(id.clone(), config.clone())
        .await
        .map_err(|e| e.to_string())?;

    // Return the actual config used (might be default if none provided)
    let actual_config =
        config.unwrap_or_else(|| ShellConfig::new(ShellDetector::detect_default_shell()));

    // Emit shell creation event
    emit_status_change(&app, &id, ShellStatus::Starting);

    Ok(actual_config)
}

/// Terminate a shell instance
#[tauri::command]
pub async fn terminate_shell_instance(
    app: AppHandle,
    state: State<'_, AppState>,
    id: String,
) -> Result<(), String> {
    let mut manager = state.command_execution_state.shell_manager.lock().await;

    manager
        .terminate_shell(&id)
        .await
        .map_err(|e| e.to_string())?;

    // Emit termination event
    emit_status_change(&app, &id, ShellStatus::Terminated);

    Ok(())
}

/// Send input to a shell instance
#[tauri::command]
pub async fn send_shell_input(
    app: AppHandle,
    state: State<'_, AppState>,
    id: String,
    input: String,
) -> Result<(), String> {
    let mut manager = state.command_execution_state.shell_manager.lock().await;

    manager
        .send_input(&id, &input)
        .await
        .map_err(|e| e.to_string())?;

    Ok(())
}

/// Get the status of a shell instance
#[tauri::command]
pub async fn get_shell_status(
    state: State<'_, AppState>,
    id: String,
) -> Result<ShellStatus, String> {
    let manager = state.command_execution_state.shell_manager.lock().await;

    manager
        .get_shell_status(&id)
        .ok_or_else(|| format!("Shell instance '{}' not found", id))
}

/// Get a list of all available shells on the system
#[tauri::command]
pub async fn get_available_shells() -> Result<Vec<ShellInfo>, String> {
    let available_shells = ShellDetector::get_available_shells();

    let mut shell_infos = Vec::new();
    for shell_type in available_shells {
        match ShellDetector::get_shell_info(&shell_type) {
            Ok(info) => shell_infos.push(info),
            Err(e) => {
                log::warn!("Failed to get info for shell {:?}: {}", shell_type, e);
            }
        }
    }

    Ok(shell_infos)
}

/// List all active shell instances
#[tauri::command]
pub async fn list_shell_instances(state: State<'_, AppState>) -> Result<Vec<String>, String> {
    let manager = state.command_execution_state.shell_manager.lock().await;
    Ok(manager.list_shells())
}

/// Get health status of all shell instances
#[tauri::command]
pub async fn get_shells_health(
    state: State<'_, AppState>,
) -> Result<Vec<(String, ShellStatus)>, String> {
    let manager = state.command_execution_state.shell_manager.lock().await;
    Ok(manager.health_check())
}

/// Detect the system default shell
#[tauri::command]
pub async fn detect_default_shell() -> Result<ShellType, String> {
    Ok(ShellDetector::detect_default_shell())
}

/// Check if a specific shell is available on the system
#[tauri::command]
pub async fn is_shell_available(shell_type: ShellType) -> Result<bool, String> {
    Ok(ShellDetector::is_shell_available(&shell_type))
}

/// Initialize the shell manager
pub fn init_shell_manager() -> ShellManagerState {
    let mut manager = ShellManager::new();

    // Set default configuration
    let default_config = ShellConfig::new(ShellDetector::detect_default_shell())
        .with_envs(std::env::vars().collect());

    manager.set_default_config(default_config);

    Arc::new(Mutex::new(manager))
}

/// Helper function to emit shell output events
pub fn emit_output(app: &AppHandle, id: &str, output: &str, output_type: OutputType) {
    let event = ShellOutputEvent {
        id: id.to_string(),
        output: output.to_string(),
        output_type,
    };

    if let Err(e) = app.emit("shell-output", event) {
        log::error!("Failed to emit shell output event: {}", e);
    }
}

/// Helper function to emit shell status change events
pub fn emit_status_change(app: &AppHandle, id: &str, status: ShellStatus) {
    let event = ShellStatusEvent {
        id: id.to_string(),
        status,
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
    };

    if let Err(e) = app.emit("shell-status", event) {
        log::error!("Failed to emit shell status event: {}", e);
    }
}

/// Setup shell event handlers
pub fn setup_shell_events(app: AppHandle, shell_manager: ShellManagerState) {
    // This would set up background tasks to monitor shell processes
    // and emit events when output is available or status changes

    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(100));

        loop {
            interval.tick().await;

            // Check shell health and emit status updates if needed
            let manager = shell_manager.lock().await;
            let health_status = manager.health_check();

            for (id, status) in health_status {
                // This is a simplified approach - in a real implementation,
                // we would track status changes and only emit when status actually changes
                if matches!(status, ShellStatus::Error(_)) {
                    emit_status_change(&app, &id, status);
                }
            }
        }
    });
}
