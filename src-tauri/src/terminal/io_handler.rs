// IO handling for terminal operations
// This module manages input/output processing, buffering, and formatting
// Enhanced version with ANSI parsing, input validation, and WebSocket communication

use anyhow::Result;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime};
use tauri::{Emitter, Window};
use thiserror::Error;
use tokio::sync::{broadcast, mpsc};
use tokio::time::{interval, Interval};
use uuid;

use super::{<PERSON><PERSON>Error, PtyManager};

/// Enhanced IO configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IOConfig {
    pub buffer_size: usize,
    pub flush_interval_ms: u64,
    pub max_history_lines: usize,
    pub ansi_colors_enabled: bool,
    pub unicode_support: bool,
    pub input_validation_enabled: bool,
}

impl Default for IOConfig {
    fn default() -> Self {
        Self {
            buffer_size: 8192,
            flush_interval_ms: 16, // ~60fps
            max_history_lines: 10000,
            ansi_colors_enabled: true,
            unicode_support: true,
            input_validation_enabled: true,
        }
    }
}

/// Cursor position in terminal
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct CursorPosition {
    pub row: u16,
    pub col: u16,
}

impl Default for CursorPosition {
    fn default() -> Self {
        Self { row: 0, col: 0 }
    }
}

/// Terminal size information
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct TerminalSize {
    pub rows: u16,
    pub cols: u16,
}

impl Default for TerminalSize {
    fn default() -> Self {
        Self { rows: 24, cols: 80 }
    }
}

/// Terminal operating modes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TerminalMode {
    Command,     // 命令输入模式
    Output,      // 输出显示模式
    Interactive, // 交互式程序模式 (如 vim, top)
    AI,          // AI 交互模式
}

/// Screen buffer for terminal display
#[derive(Debug, Clone)]
pub struct ScreenBuffer {
    lines: Vec<Vec<FormattedChar>>,
    size: TerminalSize,
    cursor: CursorPosition,
}

impl ScreenBuffer {
    pub fn new(size: TerminalSize) -> Self {
        let mut lines = Vec::new();
        for _ in 0..size.rows {
            lines.push(vec![FormattedChar::default(); size.cols as usize]);
        }

        Self {
            lines,
            size,
            cursor: CursorPosition::default(),
        }
    }

    pub fn resize(&mut self, new_size: TerminalSize) {
        self.size = new_size;
        self.lines.resize(new_size.rows as usize, Vec::new());
        for line in &mut self.lines {
            line.resize(new_size.cols as usize, FormattedChar::default());
        }
    }

    pub fn set_cursor(&mut self, pos: CursorPosition) {
        self.cursor = pos;
    }

    pub fn write_char(&mut self, ch: FormattedChar) {
        if self.cursor.row < self.size.rows && self.cursor.col < self.size.cols {
            self.lines[self.cursor.row as usize][self.cursor.col as usize] = ch;
            self.cursor.col += 1;
            if self.cursor.col >= self.size.cols {
                self.cursor.col = 0;
                self.cursor.row += 1;
            }
        }
    }

    pub fn clear(&mut self) {
        for line in &mut self.lines {
            for ch in line {
                *ch = FormattedChar::default();
            }
        }
        self.cursor = CursorPosition::default();
    }
}

/// Enhanced session state
#[derive(Debug, Clone)]
pub struct SessionState {
    pub cursor_position: CursorPosition,
    pub terminal_size: TerminalSize,
    pub screen_buffer: ScreenBuffer,
    pub input_history: VecDeque<String>,
    pub output_history: VecDeque<OutputLine>,
    pub current_mode: TerminalMode,
    pub session_id: String,
}

impl SessionState {
    pub fn new(session_id: String, terminal_size: TerminalSize) -> Self {
        Self {
            cursor_position: CursorPosition::default(),
            terminal_size,
            screen_buffer: ScreenBuffer::new(terminal_size),
            input_history: VecDeque::new(),
            output_history: VecDeque::new(),
            current_mode: TerminalMode::Command,
            session_id,
        }
    }
}

/// Output line with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutputLine {
    pub content: String,
    pub line_type: OutputType,
    pub timestamp: SystemTime,
    pub ansi_formatted: bool,
}

/// Text styling information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextStyle {
    pub fg_color: Option<Color>,
    pub bg_color: Option<Color>,
    pub bold: bool,
    pub italic: bool,
    pub underline: bool,
    pub strikethrough: bool,
}

impl Default for TextStyle {
    fn default() -> Self {
        Self {
            fg_color: None,
            bg_color: None,
            bold: false,
            italic: false,
            underline: false,
            strikethrough: false,
        }
    }
}

/// Color representation
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum Color {
    Black,
    Red,
    Green,
    Yellow,
    Blue,
    Magenta,
    Cyan,
    White,
    BrightBlack,
    BrightRed,
    BrightGreen,
    BrightYellow,
    BrightBlue,
    BrightMagenta,
    BrightCyan,
    BrightWhite,
    RGB(u8, u8, u8),
    Indexed(u8),
}

/// Formatted character for display
#[derive(Debug, Clone)]
pub struct FormattedChar {
    pub ch: char,
    pub style: TextStyle,
}

impl Default for FormattedChar {
    fn default() -> Self {
        Self {
            ch: ' ',
            style: TextStyle::default(),
        }
    }
}

/// Formatted output chunk
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormattedChunk {
    pub text: String,
    pub style: TextStyle,
    pub chunk_type: ChunkType,
}

/// Chunk type for different kinds of output
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ChunkType {
    Text,
    CursorMove { row: u16, col: u16 },
    ClearScreen,
    ClearLine,
    StyleChange,
    Newline,
    Bell,
    Unknown,
}

/// ANSI parser state
#[derive(Debug, Clone, Copy)]
enum ParserState {
    Normal,
    Escape,
    CSI,
    OSC,
}

/// ANSI sequence parser
pub struct AnsiParser {
    state: ParserState,
    current_sequence: String,
    current_style: TextStyle,
}

impl AnsiParser {
    pub fn new() -> Self {
        Self {
            state: ParserState::Normal,
            current_sequence: String::new(),
            current_style: TextStyle::default(),
        }
    }

    pub fn process_char(&mut self, ch: char) -> AnsiEvent {
        match self.state {
            ParserState::Normal => {
                if ch == '\x1b' {
                    self.state = ParserState::Escape;
                    self.current_sequence.clear();
                    AnsiEvent::Continue
                } else {
                    AnsiEvent::Character(ch, self.current_style.clone())
                }
            }
            ParserState::Escape => {
                self.current_sequence.push(ch);
                match ch {
                    '[' => {
                        self.state = ParserState::CSI;
                        AnsiEvent::Continue
                    }
                    ']' => {
                        self.state = ParserState::OSC;
                        AnsiEvent::Continue
                    }
                    _ => {
                        self.state = ParserState::Normal;
                        AnsiEvent::ControlSequence(self.parse_escape_sequence())
                    }
                }
            }
            ParserState::CSI => {
                self.current_sequence.push(ch);
                if ch.is_ascii_alphabetic() || ch == '~' {
                    self.state = ParserState::Normal;
                    let sequence = self.parse_csi_sequence();
                    AnsiEvent::ControlSequence(sequence)
                } else {
                    AnsiEvent::Continue
                }
            }
            ParserState::OSC => {
                self.current_sequence.push(ch);
                if ch == '\x07' || (ch == '\\' && self.current_sequence.ends_with("\x1b\\")) {
                    self.state = ParserState::Normal;
                    AnsiEvent::ControlSequence(ControlSequence::Bell)
                } else {
                    AnsiEvent::Continue
                }
            }
        }
    }

    fn parse_escape_sequence(&self) -> ControlSequence {
        match self.current_sequence.as_str() {
            "c" => ControlSequence::ClearScreen,
            _ => ControlSequence::Unknown,
        }
    }

    fn parse_csi_sequence(&mut self) -> ControlSequence {
        let seq = &self.current_sequence;
        if seq.is_empty() {
            return ControlSequence::Unknown;
        }

        let last_char = seq.chars().last().unwrap();
        let params: Vec<u16> = seq[1..seq.len() - 1]
            .split(';')
            .filter_map(|s| s.parse().ok())
            .collect();

        match last_char {
            'H' | 'f' => {
                let row = params.get(0).copied().unwrap_or(1).saturating_sub(1);
                let col = params.get(1).copied().unwrap_or(1).saturating_sub(1);
                ControlSequence::CursorMove { row, col }
            }
            'A' => ControlSequence::CursorUp(params.get(0).copied().unwrap_or(1)),
            'B' => ControlSequence::CursorDown(params.get(0).copied().unwrap_or(1)),
            'C' => ControlSequence::CursorRight(params.get(0).copied().unwrap_or(1)),
            'D' => ControlSequence::CursorLeft(params.get(0).copied().unwrap_or(1)),
            'J' => match params.get(0).copied().unwrap_or(0) {
                0 => ControlSequence::ClearScreen,
                2 => ControlSequence::ClearScreen,
                _ => ControlSequence::Unknown,
            },
            'K' => ControlSequence::ClearLine,
            'm' => {
                self.parse_sgr_sequence(&params);
                ControlSequence::SetStyle(self.current_style.clone())
            }
            _ => ControlSequence::Unknown,
        }
    }

    fn parse_sgr_sequence(&mut self, params: &[u16]) {
        for &param in params {
            match param {
                0 => self.current_style = TextStyle::default(),
                1 => self.current_style.bold = true,
                3 => self.current_style.italic = true,
                4 => self.current_style.underline = true,
                9 => self.current_style.strikethrough = true,
                22 => self.current_style.bold = false,
                23 => self.current_style.italic = false,
                24 => self.current_style.underline = false,
                29 => self.current_style.strikethrough = false,
                30..=37 => {
                    self.current_style.fg_color = Some(self.ansi_color_from_code(param - 30))
                }
                40..=47 => {
                    self.current_style.bg_color = Some(self.ansi_color_from_code(param - 40))
                }
                90..=97 => {
                    self.current_style.fg_color = Some(self.ansi_bright_color_from_code(param - 90))
                }
                100..=107 => {
                    self.current_style.bg_color =
                        Some(self.ansi_bright_color_from_code(param - 100))
                }
                _ => {} // Ignore unknown codes
            }
        }
    }

    fn ansi_color_from_code(&self, code: u16) -> Color {
        match code {
            0 => Color::Black,
            1 => Color::Red,
            2 => Color::Green,
            3 => Color::Yellow,
            4 => Color::Blue,
            5 => Color::Magenta,
            6 => Color::Cyan,
            7 => Color::White,
            _ => Color::White,
        }
    }

    fn ansi_bright_color_from_code(&self, code: u16) -> Color {
        match code {
            0 => Color::BrightBlack,
            1 => Color::BrightRed,
            2 => Color::BrightGreen,
            3 => Color::BrightYellow,
            4 => Color::BrightBlue,
            5 => Color::BrightMagenta,
            6 => Color::BrightCyan,
            7 => Color::BrightWhite,
            _ => Color::BrightWhite,
        }
    }
}

/// ANSI parser events
#[derive(Debug, Clone)]
pub enum AnsiEvent {
    Character(char, TextStyle),
    ControlSequence(ControlSequence),
    Continue,
}

/// Control sequence types
#[derive(Debug, Clone)]
pub enum ControlSequence {
    CursorMove { row: u16, col: u16 },
    CursorUp(u16),
    CursorDown(u16),
    CursorLeft(u16),
    CursorRight(u16),
    ClearScreen,
    ClearLine,
    SetStyle(TextStyle),
    Bell,
    Unknown,
}

/// Simplified AsyncIOHandler for more reliable PTY communication
pub struct AsyncIOHandler {
    terminal_id: String,
    output_tx: mpsc::UnboundedSender<Vec<u8>>,
    input_rx: mpsc::UnboundedReceiver<Vec<u8>>,
    read_buffer: Vec<u8>,
    window: Window,
}

impl AsyncIOHandler {
    /// Create new AsyncIOHandler with channels
    pub fn new(
        terminal_id: String,
        window: Window,
    ) -> (
        Self,
        mpsc::UnboundedReceiver<Vec<u8>>,
        mpsc::UnboundedSender<Vec<u8>>,
    ) {
        let (output_tx, output_rx) = mpsc::unbounded_channel();
        let (input_tx, input_rx) = mpsc::unbounded_channel();

        let handler = Self {
            terminal_id,
            output_tx,
            input_rx,
            read_buffer: vec![0; 8192],
            window,
        };

        (handler, output_rx, input_tx)
    }

    /// Start simplified IO loop with better error handling
    pub async fn start_io_loop(&mut self, pty_manager: Arc<PtyManager>) -> Result<(), IOError> {
        log::info!("Starting IO loop for terminal: {}", self.terminal_id);

        // Verify PTY exists before starting loop
        if !pty_manager.has_pty(&self.terminal_id) {
            let error_msg = format!("PTY {} not found when starting IO loop", self.terminal_id);
            log::error!("{}", error_msg);
            return Err(IOError::ConnectionError(error_msg));
        }

        // 使用更高频率的轮询（20ms = ~50fps）和更好的错误处理
        let mut read_interval = tokio::time::interval(Duration::from_millis(20));
        let mut consecutive_errors = 0;
        const MAX_CONSECUTIVE_ERRORS: u32 = 10; // 增加最大错误次数
        let mut total_bytes_read = 0u64;

        log::info!(
            "IO loop started successfully for terminal: {} (polling every 20ms)",
            self.terminal_id
        );

        loop {
            tokio::select! {
                // Some(input_data) = self.input_rx.recv() => {
                //     // Write input to PTY
                //     let input_str = String::from_utf8_lossy(&input_data);
                //     log::debug!("Writing input to PTY {}: {:?}", self.terminal_id, input_str);

                //     match pty_manager.write_to_pty(&self.terminal_id, &input_data).await {
                //         Ok(_) => {
                //             log::debug!("Input written successfully to PTY: {} ({} bytes)",
                //                        self.terminal_id, input_data.len());
                //         }
                //         Err(PtyError::NotFound) => {
                //             log::info!("PTY {} was closed during write, stopping IO loop", self.terminal_id);
                //             break;
                //         }
                //         Err(e) => {
                //             log::error!("Write error for PTY {}: {}", self.terminal_id, e);
                //             // Don't break on write errors, just log them
                //         }
                //     }
                // },

                _ = read_interval.tick() => {
                    log::debug!("Reading from PTY: {}", self.terminal_id);
                    // Try to read from PTY
                    match pty_manager.read_from_pty(&self.terminal_id, &mut self.read_buffer).await {
                        Ok(0) => {
                            // No data available, this is normal
                            consecutive_errors = 0;
                        }
                        Ok(bytes_read) => {
                            consecutive_errors = 0; // Reset error counter
                            total_bytes_read += bytes_read as u64;
                            let data = self.read_buffer[..bytes_read].to_vec();

                            // 记录读取的数据用于调试
                            if bytes_read > 0 {
                                let data_str = String::from_utf8_lossy(&data);
                                log::debug!("Read {} bytes from PTY {}: {:?}",
                                          bytes_read, self.terminal_id, data_str);

                                if let Err(e) = self.window.emit("terminal-output", serde_json::json!({
                                    "terminal_id": self.terminal_id,
                                    "data": data_str,
                                })) {
                                    log::error!("发送测试消息失败: {}", e);
                                }
                            }

                            // Send output to frontend
                            // if let Err(e) = self.output_tx.send(data) {
                            //     log::info!("Output channel closed for terminal {} (sent {} total bytes), error: {}, stopping IO loop",
                            //              self.terminal_id, total_bytes_read, e);
                            //     break;
                            // }
                        }
                        Err(PtyError::NotFound) => {
                            log::info!("PTY {} was closed, stopping IO loop (sent {} total bytes)",
                                     self.terminal_id, total_bytes_read);
                            break;
                        }
                        Err(e) => {
                            consecutive_errors += 1;
                            log::warn!("Read error {}/{} for PTY {}: {}",
                                     consecutive_errors, MAX_CONSECUTIVE_ERRORS, self.terminal_id, e);

                            if consecutive_errors >= MAX_CONSECUTIVE_ERRORS {
                                log::error!("Too many consecutive read errors for PTY {}, stopping (sent {} total bytes)",
                                          self.terminal_id, total_bytes_read);
                                break; // 不返回错误，只是停止循环
                            }

                            // Brief pause before retrying - shorter pause for better responsiveness
                            tokio::time::sleep(Duration::from_millis(50)).await;
                        }
                    }
                },

                else => {
                    log::info!("All channels closed for terminal {} (sent {} total bytes)",
                             self.terminal_id, total_bytes_read);
                    break;
                }
            }
        }

        log::info!(
            "IO loop terminated cleanly for terminal {} (total bytes transferred: {})",
            self.terminal_id,
            total_bytes_read
        );
        Ok(())
    }

    /// Get terminal ID
    pub fn terminal_id(&self) -> &str {
        &self.terminal_id
    }
}

/// Enhanced Input Processor
pub struct InputProcessor {
    input_buffer: Arc<Mutex<InputBuffer>>,
    key_handler: KeyHandler,
    validator: InputValidator,
    config: IOConfig,
}

/// Input buffer for line editing
#[derive(Debug)]
pub struct InputBuffer {
    current_line: String,
    cursor_pos: usize,
    history: VecDeque<String>,
    history_index: Option<usize>,
}

impl InputBuffer {
    pub fn new() -> Self {
        Self {
            current_line: String::new(),
            cursor_pos: 0,
            history: VecDeque::new(),
            history_index: None,
        }
    }

    pub fn insert_char(&mut self, ch: char) {
        if self.cursor_pos >= self.current_line.len() {
            self.current_line.push(ch);
        } else {
            self.current_line.insert(self.cursor_pos, ch);
        }
        self.cursor_pos += 1;
    }

    pub fn delete_char(&mut self) {
        if self.cursor_pos > 0 {
            self.cursor_pos -= 1;
            self.current_line.remove(self.cursor_pos);
        }
    }

    pub fn get_current_line(&self) -> &str {
        &self.current_line
    }

    pub fn clear_current_line(&mut self) {
        self.current_line.clear();
        self.cursor_pos = 0;
        self.history_index = None;
    }

    pub fn add_to_history(&mut self, line: String) {
        if !line.trim().is_empty() {
            self.history.push_back(line);
            if self.history.len() > 1000 {
                self.history.pop_front();
            }
        }
    }

    pub fn navigate_history(&mut self, direction: i32) {
        if self.history.is_empty() {
            return;
        }

        let new_index = match self.history_index {
            None => {
                if direction < 0 {
                    Some(self.history.len() - 1)
                } else {
                    None
                }
            }
            Some(current) => {
                let new_idx = current as i32 + direction;
                if new_idx < 0 {
                    Some(0)
                } else if new_idx >= self.history.len() as i32 {
                    None
                } else {
                    Some(new_idx as usize)
                }
            }
        };

        self.history_index = new_index;
        self.current_line = match new_index {
            Some(idx) => self.history[idx].clone(),
            None => String::new(),
        };
        self.cursor_pos = self.current_line.len();
    }
}

impl InputProcessor {
    pub fn new(config: IOConfig) -> Self {
        Self {
            input_buffer: Arc::new(Mutex::new(InputBuffer::new())),
            key_handler: KeyHandler::new(),
            validator: InputValidator::new(),
            config,
        }
    }

    pub async fn process_key_event(&self, key_event: KeyEvent) -> Result<InputAction, IOError> {
        let action = self.key_handler.handle_key(key_event)?;

        match action {
            KeyAction::Character(ch) => {
                let mut buffer = self.input_buffer.lock().unwrap();
                buffer.insert_char(ch);
                Ok(InputAction::UpdateDisplay)
            }
            KeyAction::Backspace => {
                let mut buffer = self.input_buffer.lock().unwrap();
                buffer.delete_char();
                Ok(InputAction::UpdateDisplay)
            }
            KeyAction::Enter => {
                let mut buffer = self.input_buffer.lock().unwrap();
                let line = buffer.get_current_line().to_string();
                if self.config.input_validation_enabled && !self.validator.validate(&line)? {
                    return Ok(InputAction::ShowError("Invalid input".to_string()));
                }
                buffer.add_to_history(line.clone());
                buffer.clear_current_line();
                Ok(InputAction::ExecuteCommand(line))
            }
            KeyAction::ArrowUp => {
                let mut buffer = self.input_buffer.lock().unwrap();
                buffer.navigate_history(-1);
                Ok(InputAction::UpdateDisplay)
            }
            KeyAction::ArrowDown => {
                let mut buffer = self.input_buffer.lock().unwrap();
                buffer.navigate_history(1);
                Ok(InputAction::UpdateDisplay)
            }
            KeyAction::Tab => {
                Ok(InputAction::ShowCompletion(vec![])) // TODO: Implement tab completion
            }
            KeyAction::CtrlC => Ok(InputAction::Interrupt),
            KeyAction::CtrlD => Ok(InputAction::EOF),
            _ => Ok(InputAction::None),
        }
    }

    pub fn get_current_line(&self) -> String {
        let buffer = self.input_buffer.lock().unwrap();
        buffer.get_current_line().to_string()
    }
}

/// Key handler for terminal input
pub struct KeyHandler;

impl KeyHandler {
    pub fn new() -> Self {
        Self
    }

    pub fn handle_key(&self, key_event: KeyEvent) -> Result<KeyAction, IOError> {
        Ok(match key_event {
            KeyEvent::Char(ch) => KeyAction::Character(ch),
            KeyEvent::Enter => KeyAction::Enter,
            KeyEvent::Backspace => KeyAction::Backspace,
            KeyEvent::Tab => KeyAction::Tab,
            KeyEvent::ArrowUp => KeyAction::ArrowUp,
            KeyEvent::ArrowDown => KeyAction::ArrowDown,
            KeyEvent::ArrowLeft => KeyAction::ArrowLeft,
            KeyEvent::ArrowRight => KeyAction::ArrowRight,
            KeyEvent::CtrlC => KeyAction::CtrlC,
            KeyEvent::CtrlD => KeyAction::CtrlD,
            _ => KeyAction::Unknown,
        })
    }
}

/// Input validation
pub struct InputValidator {
    max_line_length: usize,
    forbidden_patterns: Vec<Regex>,
}

impl InputValidator {
    pub fn new() -> Self {
        let forbidden_patterns = vec![
            Regex::new(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]").unwrap(), // 控制字符
        ];

        Self {
            max_line_length: 8192,
            forbidden_patterns,
        }
    }

    pub fn validate(&self, input: &str) -> Result<bool, IOError> {
        // 检查长度限制
        if input.len() > self.max_line_length {
            return Ok(false);
        }

        // 检查禁止的模式
        for pattern in &self.forbidden_patterns {
            if pattern.is_match(input) {
                return Ok(false);
            }
        }

        Ok(true)
    }
}

/// Key events
#[derive(Debug, Clone)]
pub enum KeyEvent {
    Char(char),
    Enter,
    Backspace,
    Delete,
    Tab,
    ArrowUp,
    ArrowDown,
    ArrowLeft,
    ArrowRight,
    Home,
    End,
    CtrlC,
    CtrlD,
    CtrlL,
    Unknown,
}

/// Key actions
#[derive(Debug, Clone)]
pub enum KeyAction {
    Character(char),
    Backspace,
    Delete,
    Enter,
    Tab,
    ArrowUp,
    ArrowDown,
    ArrowLeft,
    ArrowRight,
    Home,
    End,
    CtrlC,
    CtrlD,
    CtrlL,
    Unknown,
}

/// Input actions
#[derive(Debug, Clone)]
pub enum InputAction {
    None,
    UpdateDisplay,
    ExecuteCommand(String),
    ShowError(String),
    ShowCompletion(Vec<String>),
    Interrupt,
    EOF,
}

/// Enhanced Output Processor
pub struct OutputProcessor {
    ansi_parser: AnsiParser,
    config: IOConfig,
}

impl OutputProcessor {
    pub fn new(config: IOConfig) -> Self {
        Self {
            ansi_parser: AnsiParser::new(),
            config,
        }
    }

    pub async fn process_output(
        &mut self,
        data: &[u8],
        output_type: OutputType,
    ) -> Result<Vec<FormattedChunk>, IOError> {
        let text = String::from_utf8_lossy(data);
        let mut chunks = Vec::new();

        if !self.config.ansi_colors_enabled {
            // If ANSI is disabled, return plain text
            chunks.push(FormattedChunk {
                text: text.to_string(),
                style: TextStyle::default(),
                chunk_type: ChunkType::Text,
            });
            return Ok(chunks);
        }

        for ch in text.chars() {
            match self.ansi_parser.process_char(ch) {
                AnsiEvent::Character(ch, style) => {
                    chunks.push(FormattedChunk {
                        text: ch.to_string(),
                        style,
                        chunk_type: ChunkType::Text,
                    });
                }
                AnsiEvent::ControlSequence(sequence) => {
                    chunks.push(self.handle_control_sequence(sequence));
                }
                AnsiEvent::Continue => {
                    // 继续解析当前序列
                }
            }
        }

        Ok(chunks)
    }

    fn handle_control_sequence(&self, sequence: ControlSequence) -> FormattedChunk {
        match sequence {
            ControlSequence::CursorMove { row, col } => FormattedChunk {
                text: String::new(),
                style: TextStyle::default(),
                chunk_type: ChunkType::CursorMove { row, col },
            },
            ControlSequence::ClearScreen => FormattedChunk {
                text: String::new(),
                style: TextStyle::default(),
                chunk_type: ChunkType::ClearScreen,
            },
            ControlSequence::ClearLine => FormattedChunk {
                text: String::new(),
                style: TextStyle::default(),
                chunk_type: ChunkType::ClearLine,
            },
            ControlSequence::SetStyle(style) => FormattedChunk {
                text: String::new(),
                style,
                chunk_type: ChunkType::StyleChange,
            },
            ControlSequence::Bell => FormattedChunk {
                text: String::new(),
                style: TextStyle::default(),
                chunk_type: ChunkType::Bell,
            },
            _ => FormattedChunk {
                text: String::new(),
                style: TextStyle::default(),
                chunk_type: ChunkType::Unknown,
            },
        }
    }
}

/// WebSocket message types for frontend communication
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WSMessage {
    // Frontend to Backend
    Input {
        session_id: String,
        data: InputData,
    },
    ResizeTerminal {
        session_id: String,
        rows: u16,
        cols: u16,
    },
    // Backend to Frontend
    Output {
        session_id: String,
        chunks: Vec<FormattedChunk>,
    },
    StateUpdate {
        session_id: String,
        state: SessionStateUpdate,
    },
    Error {
        session_id: String,
        error: String,
    },
}

/// Input data from frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum InputData {
    KeyEvent { key: String, modifiers: Vec<String> },
    Paste { text: String },
    Command { command: String },
}

/// Session state update for frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionStateUpdate {
    pub cursor_position: CursorPosition,
    pub terminal_size: TerminalSize,
    pub current_mode: TerminalMode,
}

/// Communication manager for WebSocket
pub struct CommunicationManager {
    connections: Arc<Mutex<HashMap<String, WebSocketConnection>>>,
    message_queue: Arc<Mutex<VecDeque<WSMessage>>>,
    broadcast_tx: broadcast::Sender<WSMessage>,
}

impl CommunicationManager {
    pub fn new() -> Self {
        let (broadcast_tx, _) = broadcast::channel(1000);

        Self {
            connections: Arc::new(Mutex::new(HashMap::new())),
            message_queue: Arc::new(Mutex::new(VecDeque::new())),
            broadcast_tx,
        }
    }

    pub async fn send_to_frontend(
        &self,
        session_id: &str,
        message: WSMessage,
    ) -> Result<(), IOError> {
        // TODO: Implement actual WebSocket sending
        self.broadcast_tx
            .send(message)
            .map_err(|e| IOError::OperationFailed(e.to_string()))?;
        Ok(())
    }

    pub async fn broadcast_output(
        &self,
        session_id: &str,
        chunks: Vec<FormattedChunk>,
    ) -> Result<(), IOError> {
        let message = WSMessage::Output {
            session_id: session_id.to_string(),
            chunks,
        };

        self.send_to_frontend(session_id, message).await
    }

    pub async fn handle_input(
        &self,
        session_id: &str,
        input_data: InputData,
    ) -> Result<(), IOError> {
        // TODO: Route input to appropriate processor
        log::debug!(
            "Handling input for session {}: {:?}",
            session_id,
            input_data
        );
        Ok(())
    }
}

/// Placeholder for WebSocket connection
pub struct WebSocketConnection;

/// Enhanced IO Handler with all features
pub struct EnhancedIOHandler {
    session_state: Arc<Mutex<SessionState>>,
    input_processor: InputProcessor,
    output_processor: OutputProcessor,
    comm_manager: CommunicationManager,
    config: IOConfig,
}

impl EnhancedIOHandler {
    pub fn new(session_id: String, terminal_size: TerminalSize, config: IOConfig) -> Self {
        Self {
            session_state: Arc::new(Mutex::new(SessionState::new(session_id, terminal_size))),
            input_processor: InputProcessor::new(config.clone()),
            output_processor: OutputProcessor::new(config.clone()),
            comm_manager: CommunicationManager::new(),
            config,
        }
    }

    pub async fn process_input(&self, key_event: KeyEvent) -> Result<InputAction, IOError> {
        self.input_processor.process_key_event(key_event).await
    }

    pub async fn process_output(
        &mut self,
        data: &[u8],
        output_type: OutputType,
    ) -> Result<Vec<FormattedChunk>, IOError> {
        self.output_processor
            .process_output(data, output_type)
            .await
    }

    pub async fn resize_terminal(&self, rows: u16, cols: u16) -> Result<(), IOError> {
        let mut state = self.session_state.lock().unwrap();
        let new_size = TerminalSize { rows, cols };
        state.terminal_size = new_size;
        state.screen_buffer.resize(new_size);
        Ok(())
    }

    pub fn get_session_state(&self) -> SessionState {
        let state = self.session_state.lock().unwrap();
        state.clone()
    }
}

/// 简化的 IO 缓冲区
#[derive(Debug, Clone)]
pub struct SimpleIOBuffer {
    data: VecDeque<u8>,
    max_size: usize,
    created_at: SystemTime,
}

impl SimpleIOBuffer {
    pub fn new(max_size: usize) -> Self {
        Self {
            data: VecDeque::new(),
            max_size,
            created_at: SystemTime::now(),
        }
    }

    pub fn write(&mut self, data: &[u8]) -> usize {
        let mut written = 0;
        for &byte in data {
            if self.data.len() >= self.max_size {
                self.data.pop_front();
            }
            self.data.push_back(byte);
            written += 1;
        }
        written
    }

    pub fn read(&mut self, size: usize) -> Vec<u8> {
        let mut result = Vec::new();
        for _ in 0..size {
            if let Some(byte) = self.data.pop_front() {
                result.push(byte);
            } else {
                break;
            }
        }
        result
    }

    pub fn read_all(&mut self) -> Vec<u8> {
        let result: Vec<u8> = self.data.iter().cloned().collect();
        self.data.clear();
        result
    }

    pub fn len(&self) -> usize {
        self.data.len()
    }

    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    pub fn clear(&mut self) {
        self.data.clear();
    }
}

/// 输出类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OutputType {
    Stdout,
    Stderr,
    System,
    Error,
    Debug,
    AI, // Added for AI responses
}

/// 输出块
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutputChunk {
    pub id: String,
    pub terminal_id: String,
    pub data: Vec<u8>,
    pub chunk_type: OutputType,
    pub timestamp: SystemTime,
    pub sequence: u64,
}

/// 特殊按键
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SpecialKey {
    Enter,
    Tab,
    Escape,
    Backspace,
    Delete,
    ArrowUp,
    ArrowDown,
    ArrowLeft,
    ArrowRight,
    Home,
    End,
    PageUp,
    PageDown,
    CtrlC,
    CtrlD,
    CtrlL,
    CtrlZ,
}

/// 输入处理结果
#[derive(Debug, Clone)]
pub enum InputResult {
    Command(String),
    Partial(String),
    SpecialKey(SpecialKey),
    ControlSequence(Vec<u8>),
}

/// IO 错误
#[derive(Error, Debug)]
pub enum IOError {
    #[error("Buffer overflow")]
    BufferOverflow,

    #[error("Invalid input sequence")]
    InvalidSequence,

    #[error("IO operation failed: {0}")]
    OperationFailed(String),

    #[error("Encoding error: {0}")]
    EncodingError(String),

    #[error("Invalid UTF-8 sequence")]
    InvalidUtf8,

    #[error("Connection error: {0}")]
    ConnectionError(String),
}

// 保持向后兼容
pub use SimpleIOBuffer as IOBuffer;

// 原有的 IOHandler 结构保持不变，但标记为已弃用
#[deprecated(note = "Use EnhancedIOHandler instead")]
pub struct IOHandler {
    terminal_id: String,
    input_buffer: SimpleIOBuffer,
    output_buffer: SimpleIOBuffer,
    error_buffer: SimpleIOBuffer,
    output_sequence: u64,
    input_history: VecDeque<String>,
    max_history: usize,
}

#[allow(deprecated)]
impl IOHandler {
    pub fn new(terminal_id: String) -> Self {
        Self {
            terminal_id,
            input_buffer: SimpleIOBuffer::new(8192),
            output_buffer: SimpleIOBuffer::new(65536),
            error_buffer: SimpleIOBuffer::new(8192),
            output_sequence: 0,
            input_history: VecDeque::new(),
            max_history: 1000,
        }
    }

    pub fn process_input(&mut self, data: &[u8]) -> Vec<InputResult> {
        self.input_buffer.write(data);
        Vec::new() // 简化实现
    }

    pub fn process_output(&mut self, data: &[u8], output_type: OutputType) -> OutputChunk {
        self.output_buffer.write(data);
        self.output_sequence += 1;

        OutputChunk {
            id: uuid::Uuid::new_v4().to_string(),
            terminal_id: self.terminal_id.clone(),
            data: data.to_vec(),
            chunk_type: output_type,
            timestamp: SystemTime::now(),
            sequence: self.output_sequence,
        }
    }

    pub fn get_history(&self) -> Vec<String> {
        self.input_history.iter().cloned().collect()
    }

    pub fn clear_history(&mut self) {
        self.input_history.clear();
    }

    /// 解析特殊按键序列
    pub fn parse_special_key(&self, data: &[u8]) -> Option<(SpecialKey, usize)> {
        if data.is_empty() {
            return None;
        }

        match data[0] {
            0x0D => Some((SpecialKey::Enter, 1)),
            0x09 => Some((SpecialKey::Tab, 1)),
            0x1B => {
                if data.len() >= 3 && data[1] == 0x5B {
                    match data[2] {
                        0x41 => Some((SpecialKey::ArrowUp, 3)),
                        0x42 => Some((SpecialKey::ArrowDown, 3)),
                        0x43 => Some((SpecialKey::ArrowRight, 3)),
                        0x44 => Some((SpecialKey::ArrowLeft, 3)),
                        _ => None,
                    }
                } else {
                    Some((SpecialKey::Escape, 1))
                }
            }
            0x08 | 0x7F => Some((SpecialKey::Backspace, 1)),
            0x03 => Some((SpecialKey::CtrlC, 1)),
            0x04 => Some((SpecialKey::CtrlD, 1)),
            0x0C => Some((SpecialKey::CtrlL, 1)),
            0x1A => Some((SpecialKey::CtrlZ, 1)),
            _ => None,
        }
    }
}

/// Buffer statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BufferStats {
    pub input_buffer_size: usize,
    pub output_buffer_size: usize,
    pub error_buffer_size: usize,
    pub history_count: usize,
    pub output_sequence: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_io_buffer() {
        let mut buffer = IOBuffer::new(10);

        // Test writing
        let written = buffer.write(b"hello");
        assert_eq!(written, 5);
        assert_eq!(buffer.len(), 5);

        // Test reading
        let data = buffer.read(3);
        assert_eq!(data, b"hel");
        assert_eq!(buffer.len(), 2);

        // Test reading all
        let remaining = buffer.read_all();
        assert_eq!(remaining, b"lo");
        assert!(buffer.is_empty());
    }

    #[test]
    fn test_special_key_parsing() {
        let handler = IOHandler::new("test".to_string());

        // Test Enter key
        let result = handler.parse_special_key(&[0x0D]);
        assert!(matches!(result, Some((SpecialKey::Enter, 1))));

        // Test Ctrl+C
        let result = handler.parse_special_key(&[0x03]);
        assert!(matches!(result, Some((SpecialKey::CtrlC, 1))));

        // Test Arrow Up
        let result = handler.parse_special_key(&[0x1B, 0x5B, 0x41]);
        assert!(matches!(result, Some((SpecialKey::ArrowUp, 3))));
    }

    #[tokio::test]
    async fn test_ansi_parsing() {
        let config = IOConfig::default();
        let mut processor = OutputProcessor::new(config);

        // Test color sequence
        let input = b"\x1b[31mRed Text\x1b[0m";
        let chunks = processor
            .process_output(input, OutputType::Stdout)
            .await
            .unwrap();

        assert!(!chunks.is_empty());

        // Find the text chunk
        let text_chunk = chunks
            .iter()
            .find(|c| c.chunk_type == ChunkType::Text && c.text == "R");
        assert!(text_chunk.is_some());
    }

    #[tokio::test]
    async fn test_input_processing() {
        let config = IOConfig::default();
        let processor = InputProcessor::new(config);

        // Test character input
        let result = processor
            .process_key_event(KeyEvent::Char('a'))
            .await
            .unwrap();
        assert!(matches!(result, InputAction::UpdateDisplay));

        // Test enter key
        let result = processor.process_key_event(KeyEvent::Enter).await.unwrap();
        assert!(matches!(result, InputAction::ExecuteCommand(_)));
    }

    #[test]
    fn test_input_validation() {
        let validator = InputValidator::new();

        // Test normal input
        assert!(validator.validate("ls -la").unwrap());

        // Test input that's too long
        let long_input = "a".repeat(10000);
        assert!(!validator.validate(&long_input).unwrap());
    }
}
