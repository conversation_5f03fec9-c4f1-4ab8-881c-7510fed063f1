// Unix-specific shell operations
use crate::terminal::shell::{ShellConfig, ShellError};
use std::collections::HashMap;
use std::path::PathBuf;

/// Get default shell paths for Unix systems
pub fn get_shell_paths() -> Vec<String> {
    vec![
        "/bin/bash".to_string(),
        "/usr/bin/bash".to_string(),
        "/bin/zsh".to_string(),
        "/usr/bin/zsh".to_string(),
        "/usr/local/bin/zsh".to_string(),
        "/bin/fish".to_string(),
        "/usr/bin/fish".to_string(),
        "/usr/local/bin/fish".to_string(),
        "/bin/sh".to_string(),
        "/usr/bin/sh".to_string(),
    ]
}

/// Setup Unix-specific shell environment
pub fn setup_shell_env(config: &ShellConfig) -> HashMap<String, String> {
    let mut env = std::env::vars().collect::<HashMap<_, _>>();

    // Add custom environment variables
    env.extend(config.env_vars.clone());

    // Set Unix-specific environment variables
    match config.shell_type {
        crate::terminal::shell::ShellType::Bash => {
            env.insert("BASH_ENV".to_string(), "~/.bashrc".to_string());
            env.insert("SHELL".to_string(), config.executable_path.clone());
        }
        crate::terminal::shell::ShellType::Zsh => {
            if let Some(home) = dirs::home_dir() {
                let zdotdir = home.join(".config/zsh");
                if zdotdir.exists() {
                    env.insert("ZDOTDIR".to_string(), zdotdir.to_string_lossy().to_string());
                }
            }
            env.insert("SHELL".to_string(), config.executable_path.clone());
        }
        crate::terminal::shell::ShellType::Fish => {
            env.insert("SHELL".to_string(), config.executable_path.clone());
        }
        _ => {}
    }

    // Ensure PATH is set
    if !env.contains_key("PATH") {
        env.insert(
            "PATH".to_string(),
            "/usr/local/bin:/usr/bin:/bin".to_string(),
        );
    }

    // Set terminal type
    env.insert("TERM".to_string(), "xterm-256color".to_string());

    env
}

/// Check if shell executable exists and is executable on Unix
pub fn is_executable_available(path: &str) -> Result<bool, ShellError> {
    use std::os::unix::fs::PermissionsExt;

    let path_buf = PathBuf::from(path);

    if !path_buf.exists() {
        return Ok(false);
    }

    let metadata = std::fs::metadata(&path_buf).map_err(|e| ShellError::PermissionDenied {
        details: format!("Cannot access {}: {}", path, e),
    })?;

    // Check if file is executable (has execute permission)
    let permissions = metadata.permissions();
    let is_executable = permissions.mode() & 0o111 != 0;

    Ok(is_executable)
}

/// Get the default shell from system configuration
pub fn get_system_default_shell() -> Option<String> {
    // Try to get from environment variable first
    if let Ok(shell) = std::env::var("SHELL") {
        return Some(shell);
    }

    // Try to read from /etc/passwd
    if let Ok(passwd_content) = std::fs::read_to_string("/etc/passwd") {
        if let Ok(username) = std::env::var("USER") {
            for line in passwd_content.lines() {
                let parts: Vec<&str> = line.split(':').collect();
                if parts.len() >= 7 && parts[0] == username {
                    return Some(parts[6].to_string());
                }
            }
        }
    }

    None
}

/// Setup signal handling for Unix processes
pub fn setup_signal_handling() -> Result<(), ShellError> {
    // This would set up signal handlers for the shell process
    // For now, we'll just return Ok as signal handling is complex
    // and would require more sophisticated implementation
    Ok(())
}
