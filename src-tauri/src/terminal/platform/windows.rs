// Windows-specific shell operations
use crate::terminal::shell::{ShellConfig, ShellError};
use std::collections::HashMap;
use std::path::PathBuf;

/// Get default shell paths for Windows systems
pub fn get_shell_paths() -> Vec<String> {
    vec![
        "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe".to_string(),
        "C:\\Program Files\\PowerShell\\7\\pwsh.exe".to_string(),
        "C:\\Program Files\\PowerShell\\6\\pwsh.exe".to_string(),
        "C:\\Windows\\System32\\cmd.exe".to_string(),
        "powershell.exe".to_string(),
        "pwsh.exe".to_string(),
        "cmd.exe".to_string(),
    ]
}

/// Setup Windows-specific shell environment
pub fn setup_shell_env(config: &ShellConfig) -> HashMap<String, String> {
    let mut env = std::env::vars().collect::<HashMap<_, _>>();

    // Add custom environment variables
    env.extend(config.env_vars.clone());

    // Set Windows-specific environment variables
    match config.shell_type {
        crate::terminal::shell::ShellType::PowerShell => {
            env.insert("PSModulePath".to_string(), get_powershell_module_path());
        }
        crate::terminal::shell::ShellType::Pwsh => {
            env.insert(
                "PSModulePath".to_string(),
                get_powershell_core_module_path(),
            );
        }
        crate::terminal::shell::ShellType::Cmd => {
            // CMD doesn't need special environment setup
        }
        _ => {}
    }

    // Ensure common Windows environment variables are set
    if !env.contains_key("PATHEXT") {
        env.insert(
            "PATHEXT".to_string(),
            ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC".to_string(),
        );
    }

    // Set terminal type for Windows
    env.insert("TERM".to_string(), "xterm".to_string());

    env
}

/// Get PowerShell module path
fn get_powershell_module_path() -> String {
    let mut paths = Vec::new();

    // System modules
    paths.push("C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\Modules".to_string());

    // User profile modules
    if let Ok(profile) = std::env::var("USERPROFILE") {
        paths.push(format!(
            "{}\\Documents\\WindowsPowerShell\\Modules",
            profile
        ));
    }

    // Program Files modules
    paths.push("C:\\Program Files\\WindowsPowerShell\\Modules".to_string());

    paths.join(";")
}

/// Get PowerShell Core module path
fn get_powershell_core_module_path() -> String {
    let mut paths = Vec::new();

    // PowerShell Core system modules
    if let Ok(program_files) = std::env::var("ProgramFiles") {
        paths.push(format!("{}\\PowerShell\\Modules", program_files));
    }

    // User profile modules
    if let Ok(profile) = std::env::var("USERPROFILE") {
        paths.push(format!("{}\\Documents\\PowerShell\\Modules", profile));
    }

    paths.join(";")
}

/// Check if shell executable exists on Windows
pub fn is_executable_available(path: &str) -> Result<bool, ShellError> {
    let path_buf = PathBuf::from(path);

    if path_buf.exists() {
        return Ok(true);
    }

    // Try to find in PATH
    if let Ok(_) = which::which(path) {
        return Ok(true);
    }

    Ok(false)
}

/// Get the default shell from Windows system
pub fn get_system_default_shell() -> Option<String> {
    // Check for PowerShell Core first
    if is_executable_available("pwsh.exe").unwrap_or(false) {
        return Some("pwsh.exe".to_string());
    }

    // Then check for Windows PowerShell
    if is_executable_available("powershell.exe").unwrap_or(false) {
        return Some("powershell.exe".to_string());
    }

    // Fallback to CMD
    Some("cmd.exe".to_string())
}

/// Setup Windows-specific process attributes
pub fn setup_process_attributes() -> Result<(), ShellError> {
    // Windows-specific process setup would go here
    // For now, just return Ok
    Ok(())
}
