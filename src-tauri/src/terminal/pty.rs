// PTY (Pseudo Terminal) implementation
// This module provides cross-platform PTY functionality

use anyhow::Result;
use portable_pty::{Child as Pty<PERSON>hil<PERSON>, CommandBuilder, MasterPty, PtySize, PtySystem};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::io::{Read, Write};
use std::sync::{Arc, Mutex};
use thiserror::Error;
use uuid::Uuid;

#[cfg(unix)]
use std::os::unix::process::CommandExt;
#[cfg(windows)]
use std::os::windows::process::CommandExt;

use super::TerminalConfig;

/// PTY interface trait for cross-platform compatibility
pub trait PtyInterface: std::fmt::Debug {
    fn write_input(&mut self, data: &[u8]) -> Result<usize, PtyError>;
    fn read_output(&mut self) -> Result<Vec<u8>, PtyError>;
    fn resize(&mut self, rows: u16, cols: u16) -> Result<(), PtyError>;
    fn get_size(&self) -> (u16, u16);
    fn is_alive(&self) -> bool;
}

/// PTY 配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PtyConfig {
    pub shell: String,
    pub args: Vec<String>,
    pub cwd: Option<String>,
    pub env: HashMap<String, String>,
    pub size: PtySizeConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PtySizeConfig {
    pub rows: u16,
    pub cols: u16,
    pub pixel_width: u16,
    pub pixel_height: u16,
}

impl From<PtySizeConfig> for PtySize {
    fn from(config: PtySizeConfig) -> Self {
        PtySize {
            rows: config.rows,
            cols: config.cols,
            pixel_width: config.pixel_width,
            pixel_height: config.pixel_height,
        }
    }
}

impl From<PtySize> for PtySizeConfig {
    fn from(size: PtySize) -> Self {
        PtySizeConfig {
            rows: size.rows,
            cols: size.cols,
            pixel_width: size.pixel_width,
            pixel_height: size.pixel_height,
        }
    }
}

impl Default for PtyConfig {
    fn default() -> Self {
        Self {
            shell: get_default_shell(),
            args: vec![],
            cwd: None,
            env: HashMap::new(),
            size: PtySizeConfig {
                rows: 24,
                cols: 80,
                pixel_width: 0,
                pixel_height: 0,
            },
        }
    }
}

/// PTY 实例
pub struct PtyInstance {
    pub id: String,
    pub master: Box<dyn MasterPty + Send>,
    pub child: Box<dyn PtyChild + Send + Sync>,
    pub config: PtyConfig,
    pub writer: Option<Box<dyn Write + Send>>,
}

/// PTY 管理器
pub struct PtyManager {
    instances: Arc<Mutex<HashMap<String, PtyInstance>>>,
}

impl PtyManager {
    pub fn new() -> Self {
        Self {
            instances: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 创建新的 PTY 实例
    pub async fn create_pty(&self, config: PtyConfig) -> Result<String, PtyError> {
        let id = Uuid::new_v4().to_string();
        let pty_size: PtySize = config.size.clone().into();

        log::info!("Creating PTY {} with shell: {}", id, config.shell);
        log::debug!("PTY config: {:?}", config);

        // 验证shell是否存在
        if !std::path::Path::new(&config.shell).exists() {
            let error_msg = format!("Shell not found: {}", config.shell);
            log::error!("{}", error_msg);
            return Err(PtyError::CreationFailed(error_msg));
        }

        // 创建 PTY 对
        let pty_system = portable_pty::native_pty_system();

        let pty_pair = pty_system.openpty(pty_size).map_err(|e| {
            let error_msg = format!("Failed to create PTY pair: {}", e);
            log::error!("{}", error_msg);
            PtyError::CreationFailed(error_msg)
        })?;

        log::debug!("PTY pair created successfully for: {}", id);

        // 构建命令
        let mut cmd = CommandBuilder::new(&config.shell);
        cmd.args(&config.args);

        if let Some(cwd) = &config.cwd {
            if std::path::Path::new(cwd).exists() {
                cmd.cwd(cwd);
                log::debug!("Set working directory to: {}", cwd);
            } else {
                log::warn!("Working directory does not exist, using default: {}", cwd);
            }
        }

        for (key, value) in &config.env {
            cmd.env(key, value);
        }

        log::debug!("Spawning shell process: {}", config.shell);

        // 启动子进程
        let child = pty_pair.slave.spawn_command(cmd).map_err(|e| {
            let error_msg = format!("Failed to spawn shell process '{}': {}", config.shell, e);
            log::error!("{}", error_msg);
            PtyError::SpawnFailed(error_msg)
        })?;

        log::info!("Shell process spawned successfully for PTY: {}", id);

        // 获取 writer 并存储
        let writer = pty_pair.master.take_writer().map_err(|e| {
            let error_msg = format!("Failed to take writer for PTY {}: {}", id, e);
            log::error!("{}", error_msg);
            PtyError::CreationFailed(error_msg)
        })?;

        let instance = PtyInstance {
            id: id.clone(),
            master: pty_pair.master,
            child,
            config: config.clone(),
            writer: Some(writer),
        };

        log::info!("About to acquire lock for PTY registration: {}", id);

        {
            let mut instances = self.instances.lock().unwrap();
            log::info!("Lock acquired, inserting PTY: {}", id);
            instances.insert(id.clone(), instance);
            log::info!("PTY instance inserted successfully: {}", id);
        } // 锁在这里自动释放

        log::info!("Lock released for PTY: {}", id);

        log::info!("PTY {} created and registered successfully", id);
        Ok(id)
    }

    /// 向 PTY 写入数据
    pub async fn write_to_pty(&self, pty_id: &str, data: &[u8]) -> Result<(), PtyError> {
        log::debug!(
            "Attempting to write {} bytes to PTY {}: {:?}",
            data.len(),
            pty_id,
            String::from_utf8_lossy(data)
        );

        let mut instances = self.instances.lock().unwrap();
        if let Some(instance) = instances.get_mut(pty_id) {
            if let Some(ref mut writer) = instance.writer {
                use std::io::Write;

                // 进行写入和flush操作
                let write_result = writer.write_all(data);
                let flush_result = writer.flush();

                match write_result {
                    Ok(_) => {
                        if let Err(e) = flush_result {
                            log::warn!("Flush failed for PTY {} (non-fatal): {}", pty_id, e);
                            // 不将flush失败视为致命错误，继续
                        }
                        log::debug!("Successfully wrote {} bytes to PTY {}", data.len(), pty_id);
                        Ok(())
                    }
                    Err(e) => {
                        log::error!("Write failed for PTY {}: {}", pty_id, e);
                        Err(PtyError::WriteError(e.to_string()))
                    }
                }
            } else {
                log::error!("Writer not available for PTY {}", pty_id);
                Err(PtyError::WriteError("Writer not available".to_string()))
            }
        } else {
            log::warn!("PTY {} not found for write operation", pty_id);
            Err(PtyError::NotFound)
        }
    }

    /// 从 PTY 读取数据
    pub async fn read_from_pty(&self, pty_id: &str, buffer: &mut [u8]) -> Result<usize, PtyError> {
        // 先获取reader，然后立即释放锁
        let reader_result = {
            let instances = self.instances.lock().unwrap();
            if let Some(instance) = instances.get(pty_id) {
                instance.master.try_clone_reader()
            } else {
                return Err(PtyError::NotFound);
            }
        }; // 锁在这里释放

        // 现在在没有持锁的情况下进行IO操作
        match reader_result {
            Ok(mut reader) => {
                use std::io::Read;
                match reader.read(buffer) {
                    Ok(n) => {
                        if n > 0 {
                            log::debug!("Read {} bytes from PTY {}", n, pty_id);
                        }
                        Ok(n)
                    }
                    Err(e) if e.kind() == std::io::ErrorKind::WouldBlock => {
                        // 非阻塞读取，没有数据可用是正常的
                        Ok(0)
                    }
                    Err(e) if e.kind() == std::io::ErrorKind::UnexpectedEof => {
                        // PTY可能已关闭
                        log::info!("PTY {} reached EOF, process may have terminated", pty_id);
                        Ok(0)
                    }
                    Err(e) => {
                        log::error!("Read error for PTY {}: {}", pty_id, e);
                        Err(PtyError::ReadError(e.to_string()))
                    }
                }
            }
            Err(e) => {
                log::error!("Failed to clone reader for PTY {}: {}", pty_id, e);
                Err(PtyError::ReadError(e.to_string()))
            }
        }
    }

    /// 调整 PTY 大小
    pub async fn resize_pty(&self, pty_id: &str, size: PtySize) -> Result<(), PtyError> {
        let instances = self.instances.lock().unwrap();
        if let Some(instance) = instances.get(pty_id) {
            instance
                .master
                .resize(size)
                .map_err(|e| PtyError::ResizeError(e.to_string()))?;
        } else {
            return Err(PtyError::NotFound);
        }
        Ok(())
    }

    /// 终止 PTY 实例
    pub async fn kill_pty(&self, pty_id: &str) -> Result<(), PtyError> {
        let mut instances = self.instances.lock().unwrap();
        if let Some(mut instance) = instances.remove(pty_id) {
            // 直接尝试终止进程，不检查状态
            instance
                .child
                .kill()
                .map_err(|e| PtyError::KillError(e.to_string()))?;

            // 等待进程结束
            instance
                .child
                .wait()
                .map_err(|e| PtyError::WaitError(e.to_string()))?;

            Ok(())
        } else {
            Err(PtyError::NotFound)
        }
    }

    /// 检查 PTY 是否存在
    pub fn has_pty(&self, pty_id: &str) -> bool {
        self.instances.lock().unwrap().contains_key(pty_id)
    }

    /// 获取所有 PTY ID
    pub fn list_pty_ids(&self) -> Vec<String> {
        self.instances.lock().unwrap().keys().cloned().collect()
    }

    /// 检查进程是否还活着
    pub async fn is_process_alive(&self, pty_id: &str) -> bool {
        // 简化实现：如果 PTY 存在就认为进程还活着
        self.has_pty(pty_id)
    }
}

/// 获取默认 Shell
fn get_default_shell() -> String {
    #[cfg(target_os = "windows")]
    {
        std::env::var("COMSPEC").unwrap_or_else(|_| "cmd.exe".to_string())
    }

    #[cfg(target_os = "macos")]
    {
        // macOS 优先使用环境变量中的SHELL，然后按优先级检查存在的shell
        std::env::var("SHELL").unwrap_or_else(|_| {
            // 按优先级尝试不同的 shell，只检查实际存在的路径
            if std::path::Path::new("/bin/zsh").exists() {
                "/bin/zsh".to_string()
            } else if std::path::Path::new("/bin/bash").exists() {
                "/bin/bash".to_string()
            } else {
                "/bin/sh".to_string()
            }
        })
    }

    #[cfg(all(unix, not(target_os = "macos")))]
    {
        std::env::var("SHELL").unwrap_or_else(|_| "/bin/bash".to_string())
    }
}

/// PTY 错误类型
#[derive(Error, Debug)]
pub enum PtyError {
    #[error("PTY creation failed: {0}")]
    CreationFailed(String),

    #[error("Process spawn failed: {0}")]
    SpawnFailed(String),

    #[error("Write error: {0}")]
    WriteError(String),

    #[error("Read error: {0}")]
    ReadError(String),

    #[error("Resize error: {0}")]
    ResizeError(String),

    #[error("Kill error: {0}")]
    KillError(String),

    #[error("Wait error: {0}")]
    WaitError(String),

    #[error("PTY not found")]
    NotFound,

    #[error("PTY not initialized")]
    NotInitialized,

    #[error("Unsupported operation")]
    Unsupported,
}

/// 简单的 PTY 实现用于测试和开发
#[derive(Debug)]
pub struct SimplePty {
    pub size: (u16, u16),
    pub alive: bool,
}

impl PtyInterface for SimplePty {
    fn write_input(&mut self, _data: &[u8]) -> Result<usize, PtyError> {
        Ok(0)
    }

    fn read_output(&mut self) -> Result<Vec<u8>, PtyError> {
        Ok(Vec::new())
    }

    fn resize(&mut self, rows: u16, cols: u16) -> Result<(), PtyError> {
        self.size = (rows, cols);
        Ok(())
    }

    fn get_size(&self) -> (u16, u16) {
        self.size
    }

    fn is_alive(&self) -> bool {
        self.alive
    }
}

pub fn create_pty(_config: &TerminalConfig) -> Result<Box<dyn PtyInterface + Send>, PtyError> {
    Ok(Box::new(SimplePty {
        size: (24, 80),
        alive: true,
    }))
}
