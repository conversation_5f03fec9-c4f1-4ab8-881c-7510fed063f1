// Command execution engine - Main orchestrator for command processing
// Integrates parser, security checker, and history manager

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use thiserror::Error;
use tokio::sync::Mutex;
use uuid::Uuid;

use super::command_parser::{
    AICommandType, BuiltinCommand, CommandParser, CommandType, ParseError, ShellCommand,
};
use super::history::{
    CommandHistoryManager, ExecutionRecord, HistoryEntry, HistoryError, SearchCriteria,
};
use super::security::{RiskLevel, SafetyCheck, SecurityChecker};
use super::shell::ShellManager;

/// Execution context containing session and environment information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionContext {
    pub current_directory: String,
    pub environment_vars: HashMap<String, String>,
    pub user_id: String,
    pub session_id: String,
}

impl ExecutionContext {
    pub fn new(session_id: String) -> Self {
        Self {
            current_directory: std::env::current_dir()
                .unwrap_or_else(|_| std::path::PathBuf::from("/"))
                .to_string_lossy()
                .to_string(),
            environment_vars: std::env::vars().collect(),
            user_id: whoami::username(),
            session_id,
        }
    }
}

/// Command execution result variants
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionResult {
    /// Successful command execution
    Success {
        command: ShellCommand,
        output: CommandOutput,
        duration: Duration,
    },
    /// Command execution failed
    Error {
        command: ShellCommand,
        error: String,
        duration: Duration,
    },
    /// Command requires user confirmation due to safety concerns
    RequiresConfirmation {
        command: ShellCommand,
        safety_check: SafetyCheck,
        confirmation_id: String,
    },
    /// AI command was processed and converted
    AIProcessed {
        original_input: String,
        processed_command: Option<ShellCommand>,
        response: String,
    },
    /// Built-in command was executed
    BuiltinExecuted {
        command: BuiltinCommand,
        output: String,
    },
}

/// Command output structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandOutput {
    pub stdout: String,
    pub stderr: String,
    pub exit_code: i32,
}

/// Execution errors
#[derive(Debug, Error)]
pub enum ExecutionError {
    #[error("Parse error: {0}")]
    ParseError(#[from] ParseError),

    #[error("Security violation: {message}")]
    SecurityViolation { message: String },

    #[error("History error: {0}")]
    HistoryError(#[from] HistoryError),

    #[error("Shell error: {message}")]
    ShellError { message: String },

    #[error("AI service unavailable")]
    AIServiceUnavailable,

    #[error("Context error: {message}")]
    ContextError { message: String },

    #[error("Unknown error: {message}")]
    Unknown { message: String },
}

/// Pending confirmation for dangerous commands
#[derive(Debug, Clone)]
pub struct PendingConfirmation {
    pub id: String,
    pub command: ShellCommand,
    pub safety_check: SafetyCheck,
    pub timestamp: Instant,
}

/// Main command executor
pub struct CommandExecutor {
    parser: CommandParser,
    security_checker: SecurityChecker,
    history_manager: CommandHistoryManager,
    shell_manager: Arc<Mutex<ShellManager>>,
    context: ExecutionContext,
    pending_confirmations: HashMap<String, PendingConfirmation>,
}

impl CommandExecutor {
    /// Create new command executor
    pub fn new(session_id: String, shell_manager: Arc<Mutex<ShellManager>>) -> Self {
        let context = ExecutionContext::new(session_id.clone());
        let history_manager = CommandHistoryManager::with_memory_storage(session_id.clone(), 10000);

        Self {
            parser: CommandParser::new(),
            security_checker: SecurityChecker::new(),
            history_manager,
            shell_manager,
            context,
            pending_confirmations: HashMap::new(),
        }
    }

    /// Execute a command from user input
    pub async fn execute(&mut self, input: &str) -> Result<ExecutionResult, ExecutionError> {
        // Clean up expired confirmations
        self.cleanup_expired_confirmations();

        // Parse the command
        let command_type = self.parser.parse(input)?;

        // Execute based on command type
        match command_type {
            CommandType::Shell(shell_cmd) => self.execute_shell_command(shell_cmd).await,
            CommandType::AICommand(ai_cmd) => self.execute_ai_command(ai_cmd).await,
            CommandType::Builtin(builtin_cmd) => self.execute_builtin_command(builtin_cmd).await,
        }
    }

    /// Confirm a pending dangerous command
    pub async fn confirm_command(
        &mut self,
        confirmation_id: &str,
    ) -> Result<ExecutionResult, ExecutionError> {
        if let Some(pending) = self.pending_confirmations.remove(confirmation_id) {
            // Execute the confirmed command without safety check
            self.execute_shell_command_internal(pending.command, true)
                .await
        } else {
            Err(ExecutionError::Unknown {
                message: "Confirmation ID not found or expired".to_string(),
            })
        }
    }

    /// Cancel a pending confirmation
    pub fn cancel_confirmation(&mut self, confirmation_id: &str) -> bool {
        self.pending_confirmations.remove(confirmation_id).is_some()
    }

    /// Execute shell command with safety checks
    async fn execute_shell_command(
        &mut self,
        command: ShellCommand,
    ) -> Result<ExecutionResult, ExecutionError> {
        self.execute_shell_command_internal(command, false).await
    }

    /// Internal shell command execution
    async fn execute_shell_command_internal(
        &mut self,
        command: ShellCommand,
        skip_safety_check: bool,
    ) -> Result<ExecutionResult, ExecutionError> {
        // Perform safety check unless skipped
        if !skip_safety_check {
            let safety_check = self.security_checker.check_command_safety(&command);

            if safety_check.requires_confirmation && !safety_check.is_safe {
                // Create pending confirmation
                let confirmation_id = Uuid::new_v4().to_string();
                let pending = PendingConfirmation {
                    id: confirmation_id.clone(),
                    command: command.clone(),
                    safety_check: safety_check.clone(),
                    timestamp: Instant::now(),
                };

                self.pending_confirmations
                    .insert(confirmation_id.clone(), pending);

                return Ok(ExecutionResult::RequiresConfirmation {
                    command,
                    safety_check,
                    confirmation_id,
                });
            }
        }

        // Add to history
        let history_id = self
            .history_manager
            .add_command(&command, self.context.current_directory.clone())?;

        // Execute the command
        let start_time = Instant::now();
        let result = self.execute_command_on_shell(&command).await;
        let duration = start_time.elapsed();

        // Create execution record
        let execution_record = ExecutionRecord {
            id: history_id,
            command: command.clone(),
            start_time: chrono::Utc::now(),
            end_time: Some(chrono::Utc::now()),
            duration: Some(duration),
            exit_code: result.as_ref().map(|output| output.exit_code).ok(),
            output: result.as_ref().map(|output| output.stdout.clone()).ok(),
            error_output: result.as_ref().map(|output| output.stderr.clone()).ok(),
            working_directory: self.context.current_directory.clone(),
            session_id: self.context.session_id.clone(),
        };

        // Update history with execution details
        self.history_manager
            .add_execution_record(execution_record)?;

        // Return result
        match result {
            Ok(output) => Ok(ExecutionResult::Success {
                command,
                output,
                duration,
            }),
            Err(error) => Ok(ExecutionResult::Error {
                command,
                error,
                duration,
            }),
        }
    }

    /// Execute AI enhanced commands
    async fn execute_ai_command(
        &mut self,
        ai_command: AICommandType,
    ) -> Result<ExecutionResult, ExecutionError> {
        match ai_command {
            AICommandType::NaturalLanguage { prompt } => {
                // For now, return a placeholder response
                // TODO: Integrate with actual AI service
                let response = format!("AI command processing not yet implemented for: {}", prompt);

                Ok(ExecutionResult::AIProcessed {
                    original_input: prompt,
                    processed_command: None,
                    response,
                })
            }
            AICommandType::ModelChat { query } => {
                // For now, return a placeholder response
                // TODO: Integrate with actual AI service
                let response = format!("AI chat not yet implemented for: {}", query);

                Ok(ExecutionResult::AIProcessed {
                    original_input: query,
                    processed_command: None,
                    response,
                })
            }
            AICommandType::CommandExplain { command } => {
                // Basic command explanation
                let response = self.explain_command_basic(&command);

                Ok(ExecutionResult::AIProcessed {
                    original_input: command,
                    processed_command: None,
                    response,
                })
            }
        }
    }

    /// Execute built-in terminal commands
    async fn execute_builtin_command(
        &mut self,
        builtin: BuiltinCommand,
    ) -> Result<ExecutionResult, ExecutionError> {
        let output = match builtin {
            BuiltinCommand::Clear => "Terminal cleared".to_string(),
            BuiltinCommand::Exit => "Exiting terminal...".to_string(),
            BuiltinCommand::Help => self.generate_help_text(),
            BuiltinCommand::History { limit } => {
                self.generate_history_output(limit.unwrap_or(50)).await?
            }
        };

        Ok(ExecutionResult::BuiltinExecuted {
            command: builtin,
            output,
        })
    }

    /// Execute command on shell manager
    async fn execute_command_on_shell(
        &self,
        command: &ShellCommand,
    ) -> Result<CommandOutput, String> {
        let shell_manager = self.shell_manager.lock().await;

        // For now, simulate command execution
        // TODO: Integrate with actual shell execution
        let full_command = format!("{} {}", command.executable, command.args.join(" "));

        // Simulate some common commands
        match command.executable.as_str() {
            "echo" => Ok(CommandOutput {
                stdout: command.args.join(" "),
                stderr: String::new(),
                exit_code: 0,
            }),
            "pwd" => Ok(CommandOutput {
                stdout: self.context.current_directory.clone(),
                stderr: String::new(),
                exit_code: 0,
            }),
            _ => {
                // For other commands, return a placeholder
                Ok(CommandOutput {
                    stdout: format!("Executed: {}", full_command),
                    stderr: String::new(),
                    exit_code: 0,
                })
            }
        }
    }

    /// Basic command explanation without AI
    fn explain_command_basic(&self, command: &str) -> String {
        // Parse the command to provide basic explanation
        if let Ok(CommandType::Shell(shell_cmd)) = self.parser.parse(command) {
            let mut explanation = format!("Command: {}\n", shell_cmd.executable);

            if !shell_cmd.args.is_empty() {
                explanation.push_str(&format!("Arguments: {}\n", shell_cmd.args.join(" ")));
            }

            // Add basic descriptions for common commands
            match shell_cmd.executable.as_str() {
                "ls" => explanation.push_str("Lists directory contents"),
                "cd" => explanation.push_str("Changes current directory"),
                "pwd" => explanation.push_str("Prints current working directory"),
                "mkdir" => explanation.push_str("Creates directories"),
                "rm" => explanation.push_str("Removes files and directories"),
                "cp" => explanation.push_str("Copies files and directories"),
                "mv" => explanation.push_str("Moves/renames files and directories"),
                "grep" => explanation.push_str("Searches text patterns in files"),
                "find" => explanation.push_str("Searches for files and directories"),
                "cat" => explanation.push_str("Displays file contents"),
                _ => explanation.push_str("Command not recognized in basic database"),
            }

            explanation
        } else {
            format!("Unable to parse command: {}", command)
        }
    }

    /// Generate help text for built-in commands
    fn generate_help_text(&self) -> String {
        let mut help = String::new();
        help.push_str("TAgent Terminal - Available Commands:\n\n");
        help.push_str("Built-in Commands:\n");
        help.push_str("  clear         - Clear the terminal screen\n");
        help.push_str("  exit          - Exit the terminal\n");
        help.push_str("  help          - Show this help message\n");
        help.push_str("  history [n]   - Show command history (optional limit)\n\n");
        help.push_str("AI Commands:\n");
        help.push_str("  @command <description>  - Convert natural language to command\n");
        help.push_str("  @model <query>          - Chat with AI model\n");
        help.push_str("  @explain <command>      - Explain what a command does\n\n");
        help.push_str("Examples:\n");
        help.push_str("  @command list all files in current directory\n");
        help.push_str("  @model how do I optimize git workflow?\n");
        help.push_str("  @explain ps aux | grep python\n");
        help
    }

    /// Generate history output
    async fn generate_history_output(&self, limit: usize) -> Result<String, ExecutionError> {
        let entries = self.history_manager.get_recent_commands(limit)?;

        if entries.is_empty() {
            return Ok("No command history available.".to_string());
        }

        let mut output = format!("Recent {} commands:\n\n", entries.len());

        for (index, entry) in entries.iter().enumerate() {
            let command_text = format!(
                "{} {}",
                entry.command.executable,
                entry.command.args.join(" ")
            );
            let timestamp = entry.timestamp.format("%Y-%m-%d %H:%M:%S");

            output.push_str(&format!(
                "{:3}: {} - {}\n",
                index + 1,
                timestamp,
                command_text
            ));
        }

        Ok(output)
    }

    /// Clean up expired confirmation requests
    fn cleanup_expired_confirmations(&mut self) {
        let expiry_duration = Duration::from_secs(300); // 5 minutes
        let now = Instant::now();

        self.pending_confirmations
            .retain(|_, pending| now.duration_since(pending.timestamp) < expiry_duration);
    }

    /// Get current execution context
    pub fn get_context(&self) -> &ExecutionContext {
        &self.context
    }

    /// Update working directory
    pub fn set_working_directory(&mut self, path: String) {
        self.context.current_directory = path;
    }

    /// Get command history
    pub fn get_command_history(&self, limit: usize) -> Result<Vec<HistoryEntry>, HistoryError> {
        self.history_manager.get_recent_commands(limit)
    }

    /// Search command history
    pub fn search_command_history(
        &self,
        query: &str,
        limit: usize,
    ) -> Result<Vec<HistoryEntry>, HistoryError> {
        let criteria = SearchCriteria {
            query: Some(query.to_string()),
            command_name: None,
            working_directory: None,
            session_id: None,
            date_range: None,
            exit_code: None,
            limit: Some(limit),
        };
        self.history_manager.search_history(criteria)
    }

    /// Get pending confirmations
    pub fn get_pending_confirmations(&self) -> Vec<&PendingConfirmation> {
        self.pending_confirmations.values().collect()
    }
}
