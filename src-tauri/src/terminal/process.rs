// Process management for terminal operations
// This module handles shell process creation, management, and cleanup

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::process::{Child, Command, Stdio};
use std::sync::{Arc, Mutex};
use tokio::process::{Child as TokioChild, Command as TokioCommand};

use super::{PtyInterface, TerminalConfig};

/// Process information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessInfo {
    pub pid: u32,
    pub command: String,
    pub args: Vec<String>,
    pub working_directory: String,
    pub started_at: std::time::SystemTime,
    pub status: ProcessStatus,
}

/// Process status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProcessStatus {
    Running,
    Stopped,
    Killed,
    Exited(i32),
}

/// Process manager for handling shell and child processes
#[derive(Debug)]
pub struct ProcessManager {
    main_process: Option<TokioChild>,
    child_processes: HashMap<u32, TokioChild>,
    process_info: HashMap<u32, ProcessInfo>,
}

impl ProcessManager {
    pub fn new() -> Self {
        Self {
            main_process: None,
            child_processes: HashMap::new(),
            process_info: HashMap::new(),
        }
    }

    /// Spawn a shell process with the given configuration
    pub async fn spawn_shell(
        &mut self,
        config: &TerminalConfig,
        pty: &mut dyn PtyInterface,
    ) -> Result<u32, ProcessError> {
        let shell_command = self.get_shell_command(&config.shell)?;

        #[cfg(unix)]
        {
            self.spawn_unix_shell(config, pty, shell_command).await
        }

        #[cfg(windows)]
        {
            self.spawn_windows_shell(config, pty, shell_command).await
        }
    }

    #[cfg(unix)]
    async fn spawn_unix_shell(
        &mut self,
        config: &TerminalConfig,
        pty: &mut dyn PtyInterface,
        shell_command: Vec<String>,
    ) -> Result<u32, ProcessError> {
        use std::os::unix::process::CommandExt;
        use tokio::process::Command;

        let mut cmd = Command::new(&shell_command[0]);

        if shell_command.len() > 1 {
            cmd.args(&shell_command[1..]);
        }

        cmd.current_dir(&config.working_directory);
        cmd.envs(&config.environment);

        // Set up PTY file descriptors
        // This is a simplified approach - in a real implementation,
        // we would properly connect the PTY slave to the process
        cmd.stdin(Stdio::piped());
        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        let mut child = cmd
            .spawn()
            .map_err(|e| ProcessError::SpawnFailed(e.to_string()))?;

        let pid = child.id().ok_or(ProcessError::InvalidPid)?;

        let process_info = ProcessInfo {
            pid,
            command: shell_command[0].clone(),
            args: shell_command[1..].to_vec(),
            working_directory: config.working_directory.clone(),
            started_at: std::time::SystemTime::now(),
            status: ProcessStatus::Running,
        };

        self.process_info.insert(pid, process_info);
        self.main_process = Some(child);

        Ok(pid)
    }

    #[cfg(windows)]
    async fn spawn_windows_shell(
        &mut self,
        config: &TerminalConfig,
        pty: &mut dyn PtyInterface,
        shell_command: Vec<String>,
    ) -> Result<u32, ProcessError> {
        use tokio::process::Command;

        let mut cmd = Command::new(&shell_command[0]);

        if shell_command.len() > 1 {
            cmd.args(&shell_command[1..]);
        }

        cmd.current_dir(&config.working_directory);
        cmd.envs(&config.environment);
        cmd.stdin(Stdio::piped());
        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        let mut child = cmd
            .spawn()
            .map_err(|e| ProcessError::SpawnFailed(e.to_string()))?;

        let pid = child.id().ok_or(ProcessError::InvalidPid)?;

        let process_info = ProcessInfo {
            pid,
            command: shell_command[0].clone(),
            args: shell_command[1..].to_vec(),
            working_directory: config.working_directory.clone(),
            started_at: std::time::SystemTime::now(),
            status: ProcessStatus::Running,
        };

        self.process_info.insert(pid, process_info);
        self.main_process = Some(child);

        Ok(pid)
    }

    /// Kill the main shell process
    pub async fn kill_main(&mut self) -> Result<(), ProcessError> {
        if let Some(mut process) = self.main_process.take() {
            let pid = process.id().unwrap_or(0);

            // Try graceful termination first
            #[cfg(unix)]
            {
                if let Err(_) = process.kill().await {
                    // Force kill if graceful termination fails
                    let _ = nix::sys::signal::kill(
                        nix::unistd::Pid::from_raw(pid as i32),
                        nix::sys::signal::Signal::SIGKILL,
                    );
                }
            }

            #[cfg(windows)]
            {
                let _ = process.kill().await;
            }

            if let Some(info) = self.process_info.get_mut(&pid) {
                info.status = ProcessStatus::Killed;
            }
        }

        Ok(())
    }

    /// Kill all child processes
    pub async fn kill_children(&mut self) -> Result<(), ProcessError> {
        let pids: Vec<u32> = self.child_processes.keys().cloned().collect();

        for pid in pids {
            if let Some(mut child) = self.child_processes.remove(&pid) {
                let _ = child.kill().await;

                if let Some(info) = self.process_info.get_mut(&pid) {
                    info.status = ProcessStatus::Killed;
                }
            }
        }

        Ok(())
    }

    /// Kill all processes (main + children)
    pub async fn kill_all(&mut self) -> Result<(), ProcessError> {
        self.kill_children().await?;
        self.kill_main().await?;
        Ok(())
    }

    /// Get process information
    pub fn get_process_info(&self, pid: u32) -> Option<&ProcessInfo> {
        self.process_info.get(&pid)
    }

    /// List all processes
    pub fn list_processes(&self) -> Vec<&ProcessInfo> {
        self.process_info.values().collect()
    }

    /// Check if main process is running
    pub fn is_main_running(&self) -> bool {
        self.main_process.is_some()
    }

    /// Get shell command for the given shell type
    fn get_shell_command(&self, shell: &str) -> Result<Vec<String>, ProcessError> {
        match shell.to_lowercase().as_str() {
            "bash" => Ok(vec!["bash".to_string(), "-l".to_string()]),
            "zsh" => Ok(vec!["zsh".to_string(), "-l".to_string()]),
            "fish" => Ok(vec!["fish".to_string()]),
            "sh" => Ok(vec!["sh".to_string()]),
            #[cfg(windows)]
            "powershell" => Ok(vec!["powershell.exe".to_string(), "-NoExit".to_string()]),
            #[cfg(windows)]
            "cmd" => Ok(vec!["cmd.exe".to_string()]),
            #[cfg(windows)]
            "pwsh" => Ok(vec!["pwsh.exe".to_string(), "-NoExit".to_string()]),
            _ => {
                // Try to use the shell as-is
                Ok(vec![shell.to_string()])
            }
        }
    }

    /// Update process status by checking if processes are still running
    pub async fn update_process_status(&mut self) -> Result<(), ProcessError> {
        // Check main process
        if let Some(main) = &mut self.main_process {
            match main.try_wait() {
                Ok(Some(status)) => {
                    let pid = main.id().unwrap_or(0);
                    if let Some(info) = self.process_info.get_mut(&pid) {
                        info.status = ProcessStatus::Exited(status.code().unwrap_or(-1));
                    }
                    self.main_process = None;
                }
                Ok(None) => {
                    // Still running
                }
                Err(_) => {
                    // Error occurred, assume process died
                    let pid = main.id().unwrap_or(0);
                    if let Some(info) = self.process_info.get_mut(&pid) {
                        info.status = ProcessStatus::Exited(-1);
                    }
                    self.main_process = None;
                }
            }
        }

        // Check child processes
        let mut completed_pids = Vec::new();
        for (pid, child) in &mut self.child_processes {
            match child.try_wait() {
                Ok(Some(status)) => {
                    if let Some(info) = self.process_info.get_mut(pid) {
                        info.status = ProcessStatus::Exited(status.code().unwrap_or(-1));
                    }
                    completed_pids.push(*pid);
                }
                Ok(None) => {
                    // Still running
                }
                Err(_) => {
                    // Error occurred, assume process died
                    if let Some(info) = self.process_info.get_mut(pid) {
                        info.status = ProcessStatus::Exited(-1);
                    }
                    completed_pids.push(*pid);
                }
            }
        }

        // Remove completed processes
        for pid in completed_pids {
            self.child_processes.remove(&pid);
        }

        Ok(())
    }
}

/// Process-related errors
#[derive(Debug, thiserror::Error)]
pub enum ProcessError {
    #[error("Failed to spawn process: {0}")]
    SpawnFailed(String),

    #[error("Invalid process ID")]
    InvalidPid,

    #[error("Process not found")]
    NotFound,

    #[error("Permission denied")]
    PermissionDenied,

    #[error("Process already running")]
    AlreadyRunning,

    #[error("Unsupported shell: {0}")]
    UnsupportedShell(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}
