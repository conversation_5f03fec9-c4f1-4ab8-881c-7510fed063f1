use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

use crate::ai::context_analyzer::ContextAnalyzer;
use crate::ai::provider::ModelProvider;
use crate::ai::types::*;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub role: String, // "user" 或 "assistant"
    pub content: String,
    pub timestamp: i64,
    pub metadata: Option<ChatMessageMetadata>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessageMetadata {
    pub is_command: Option<bool>,
    pub command_suggestion: Option<String>,
    pub risk_level: Option<String>, // "Low", "Medium", "High", "Critical"
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ChatContext {
    pub session_id: String,
    pub history: Vec<ChatMessage>,
    pub context_history: Vec<String>,
}

impl Default for ChatContext {
    fn default() -> Self {
        Self {
            session_id: Uuid::new_v4().to_string(),
            history: Vec::new(),
            context_history: Vec::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationHistory {
    messages: VecDeque<ChatMessage>,
    max_history: usize,
    context_window: usize,
}

impl ConversationHistory {
    pub fn new(max_history: usize, context_window: usize) -> Self {
        Self {
            messages: VecDeque::new(),
            max_history,
            context_window,
        }
    }

    pub fn add_message(&mut self, message: ChatMessage) {
        self.messages.push_back(message);

        // 保持历史记录在限制范围内
        while self.messages.len() > self.max_history {
            self.messages.pop_front();
        }
    }

    pub fn get_context(&self) -> String {
        let recent_messages: Vec<_> = self
            .messages
            .iter()
            .rev()
            .take(self.context_window)
            .rev()
            .collect();

        let mut context = String::new();
        for message in recent_messages {
            context.push_str(&format!(
                "{}：{}\n",
                if message.role == "user" {
                    "用户"
                } else {
                    "助手"
                },
                message.content
            ));
        }

        context
    }

    pub fn clear_old_messages(&mut self) {
        if self.messages.len() > self.max_history {
            let keep_count = self.max_history / 2;
            self.messages = self.messages.split_off(self.messages.len() - keep_count);
        }
    }

    pub fn export_history(&self) -> String {
        let mut export = String::from("=== 对话历史记录 ===\n\n");

        for message in &self.messages {
            let timestamp = chrono::DateTime::from_timestamp(message.timestamp / 1000, 0)
                .unwrap_or_default()
                .format("%Y-%m-%d %H:%M:%S");

            export.push_str(&format!(
                "[{}] {}：{}\n\n",
                timestamp,
                if message.role == "user" {
                    "用户"
                } else {
                    "AI助手"
                },
                message.content
            ));
        }

        export
    }
}

/// AI 对话管理器
pub struct ChatManager {
    session_id: String,
    conversation_history: ConversationHistory,
    context_analyzer: ContextAnalyzer,
    ai_client: Option<Arc<dyn ModelProvider>>,
    system_prompt: String,
}

impl ChatManager {
    pub fn new() -> Self {
        Self {
            session_id: Uuid::new_v4().to_string(),
            conversation_history: ConversationHistory::new(100, 10), // 最多100条历史，上下文窗口10条
            context_analyzer: ContextAnalyzer::default(),
            ai_client: None,
            system_prompt: Self::default_system_prompt(),
        }
    }

    pub fn with_provider(mut self, provider: Arc<dyn ModelProvider>) -> Self {
        self.ai_client = Some(provider);
        self
    }

    pub async fn start_session(&mut self) -> Result<String, AIError> {
        self.session_id = Uuid::new_v4().to_string();
        self.conversation_history = ConversationHistory::new(100, 10);

        log::info!("Started new chat session: {}", self.session_id);
        Ok(self.session_id.clone())
    }

    pub async fn end_session(&mut self) -> Result<(), AIError> {
        log::info!("Ended chat session: {}", self.session_id);

        // 清理会话数据
        self.conversation_history = ConversationHistory::new(100, 10);
        self.session_id = String::new();

        Ok(())
    }

    pub async fn send_message(
        &mut self,
        message: &str,
        context: &ChatContext,
    ) -> Result<String, AIError> {
        // 检查AI客户端是否可用
        let provider = self.ai_client.as_ref().ok_or(AIError::ServiceUnavailable)?;

        if !provider.is_available() {
            return Err(AIError::ServiceUnavailable);
        }

        // 添加用户消息到历史
        let user_message = ChatMessage {
            role: "user".to_string(),
            content: message.to_string(),
            timestamp: chrono::Utc::now().timestamp_millis(),
            metadata: None,
        };
        self.conversation_history.add_message(user_message);

        // 构建完整的提示词
        let full_prompt = self.build_conversation_prompt(message, context)?;

        // 生成AI回复
        let generation_options = GenerationOptions {
            max_tokens: Some(2048),
            temperature: Some(0.7),
            top_p: Some(0.9),
            ..Default::default()
        };

        let ai_response = provider.generate(&full_prompt, &generation_options).await?;

        // 分析回复中的命令建议
        let metadata = self.analyze_response_metadata(&ai_response);

        // 添加AI回复到历史
        let assistant_message = ChatMessage {
            role: "assistant".to_string(),
            content: ai_response.clone(),
            timestamp: chrono::Utc::now().timestamp_millis(),
            metadata,
        };
        self.conversation_history.add_message(assistant_message);

        // 定期清理旧消息
        self.conversation_history.clear_old_messages();

        Ok(ai_response)
    }

    pub fn get_conversation_history(&self) -> &ConversationHistory {
        &self.conversation_history
    }

    pub fn add_context(&mut self, _context: &str) {
        // TODO: 实现上下文分析功能
        // self.context_analyzer.add_context(context);
    }

    fn build_conversation_prompt(
        &self,
        message: &str,
        context: &ChatContext,
    ) -> Result<String, AIError> {
        let mut prompt = String::new();

        // 系统提示词
        prompt.push_str(&self.system_prompt);
        prompt.push_str("\n\n");

        // 当前环境上下文
        // TODO: 实现环境上下文构建
        // if let Ok(env_context) = self.context_analyzer.build_context() {
        //     prompt.push_str("当前环境信息：\n");
        //     prompt.push_str(&env_context);
        //     prompt.push_str("\n\n");
        // }

        // 对话历史上下文
        if !context.history.is_empty() {
            prompt.push_str("最近的对话历史：\n");
            for msg in context.history.iter().rev().take(5).rev() {
                prompt.push_str(&format!(
                    "{}：{}\n",
                    if msg.role == "user" {
                        "用户"
                    } else {
                        "助手"
                    },
                    msg.content
                ));
            }
            prompt.push_str("\n");
        }

        // 当前用户消息
        prompt.push_str(&format!("用户：{}\n助手：", message));

        Ok(prompt)
    }

    fn analyze_response_metadata(&self, response: &str) -> Option<ChatMessageMetadata> {
        let mut metadata = ChatMessageMetadata {
            is_command: None,
            command_suggestion: None,
            risk_level: None,
        };

        // 检查是否包含命令建议
        if response.contains("```") {
            metadata.is_command = Some(true);

            // 简单的风险评估
            let risk_keywords = [
                ("rm -rf", "Critical"),
                ("sudo rm", "High"),
                ("dd if=", "Critical"),
                ("mkfs", "Critical"),
                ("fdisk", "High"),
                ("passwd", "Medium"),
                ("chmod 777", "Medium"),
                ("sudo", "Medium"),
            ];

            for (keyword, risk) in risk_keywords.iter() {
                if response.to_lowercase().contains(&keyword.to_lowercase()) {
                    metadata.risk_level = Some(risk.to_string());
                    break;
                }
            }

            if metadata.risk_level.is_none() {
                metadata.risk_level = Some("Low".to_string());
            }
        }

        if metadata.is_command.is_some() || metadata.risk_level.is_some() {
            Some(metadata)
        } else {
            None
        }
    }

    fn default_system_prompt() -> String {
        r#"你是一个智能终端助手，专门帮助用户使用命令行工具和解决技术问题。

能力范围：
- 解释和推荐命令行操作
- 分析和诊断系统问题
- 提供编程和开发建议
- 解答技术相关问题

交互原则：
- 回答简洁明了，直接有用
- 对危险命令进行安全提醒
- 提供具体的命令示例，使用代码块格式
- 考虑用户的操作环境和上下文
- 当推荐命令时，解释命令的作用和注意事项

回复格式：
- 使用 ```bash 代码块包围命令
- 在代码块前后提供清晰的解释
- 对有风险的操作给出明确警告"#
            .to_string()
    }
}
