use std::sync::Arc;
use tauri::{command, State};
use tokio::sync::RwLock;

use crate::ai::manager::AIServiceManager;
use crate::ai::types::*;

/// 处理自然语言转命令
#[command]
pub async fn ai_process_natural_language(
    input: String,
    context: AIContext,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<AIResponse, String> {
    let manager = state.read().await;
    manager
        .process_natural_language(&input, &context)
        .await
        .map_err(|e| e.to_string())
}

/// 解释命令
#[command]
pub async fn ai_explain_command(
    command: String,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<String, String> {
    let manager = state.read().await;
    manager
        .explain_command(&command)
        .await
        .map_err(|e| e.to_string())
}

/// AI 聊天
#[command]
pub async fn ai_chat(
    message: String,
    context: ChatContext,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<String, String> {
    let manager = state.read().await;
    manager
        .chat(&message, &context)
        .await
        .map_err(|e| e.to_string())
}

/// 获取可用模型列表
#[command]
pub async fn ai_get_available_models(
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<Vec<ModelInfo>, String> {
    let manager = state.read().await;
    manager
        .get_available_models()
        .await
        .map_err(|e| e.to_string())
}

/// 切换 AI 模型
#[command]
pub async fn ai_switch_model(
    model_id: String,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<(), String> {
    let mut manager = state.write().await;
    manager
        .switch_provider(&model_id)
        .await
        .map_err(|e| e.to_string())
}

/// 更新 AI 配置
#[command]
pub async fn ai_update_config(
    config: AIConfig,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<(), String> {
    let mut manager = state.write().await;
    manager
        .update_config(config)
        .await
        .map_err(|e| e.to_string())
}

/// AI 健康检查
#[command]
pub async fn ai_health_check(
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<(), String> {
    let manager = state.read().await;
    manager.health_check().await.map_err(|e| e.to_string())
}

/// 获取当前目录 (辅助命令)
#[command]
pub async fn get_current_directory() -> Result<String, String> {
    std::env::current_dir()
        .map(|path| path.to_string_lossy().to_string())
        .map_err(|e| e.to_string())
}

/// 获取 Shell 类型 (辅助命令)
#[command]
pub async fn get_shell_type() -> Result<String, String> {
    // 检测当前 Shell 类型
    if let Ok(shell) = std::env::var("SHELL") {
        if let Some(shell_name) = shell.split('/').last() {
            Ok(shell_name.to_string())
        } else {
            Ok("unknown".to_string())
        }
    } else if cfg!(windows) {
        Ok("powershell".to_string())
    } else {
        Ok("bash".to_string())
    }
}

/// 获取环境变量 (辅助命令)
#[command]
pub async fn get_environment_variables() -> Result<std::collections::HashMap<String, String>, String>
{
    let mut env_vars = std::collections::HashMap::new();

    // 只获取一些常用的环境变量，避免泄露敏感信息
    let safe_vars = [
        "PATH", "HOME", "USER", "USERNAME", "PWD", "OLDPWD", "LANG", "LC_ALL", "TERM", "SHELL",
        "EDITOR", "PAGER",
    ];

    for var in &safe_vars {
        if let Ok(value) = std::env::var(var) {
            env_vars.insert(var.to_string(), value);
        }
    }

    Ok(env_vars)
}

// 为了向后兼容，保留旧的命令名称
#[command]
pub async fn process_natural_language(
    input: String,
    context: Option<String>,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<String, String> {
    let ai_context = AIContext {
        session_id: uuid::Uuid::new_v4().to_string(),
        current_directory: get_current_directory().await.unwrap_or_default(),
        shell_type: get_shell_type().await.unwrap_or_default(),
        environment: get_environment_variables().await.unwrap_or_default(),
        command_history: Vec::new(),
        user_preferences: UserPreferences::default(),
    };

    let manager = state.read().await;
    let response = manager
        .process_natural_language(&input, &ai_context)
        .await
        .map_err(|e| e.to_string())?;

    Ok(response.content)
}

#[command]
pub async fn explain_command(
    command: String,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<String, String> {
    let manager = state.read().await;
    manager
        .explain_command(&command)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn ai_chat_simple(
    message: String,
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<String, String> {
    let context = ChatContext::default();
    let manager = state.read().await;
    manager
        .chat(&message, &context)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn list_ai_services(
    state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<Vec<String>, String> {
    let manager = state.read().await;
    let models = manager
        .get_available_models()
        .await
        .map_err(|e| e.to_string())?;

    Ok(models.into_iter().map(|model| model.id).collect())
}
