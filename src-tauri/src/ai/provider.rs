use async_trait::async_trait;

use crate::ai::types::*;

/// 模型提供者抽象接口
#[async_trait]
pub trait ModelProvider: Send + Sync {
    /// 初始化提供者
    async fn initialize(&mut self, config: ProviderConfig) -> Result<(), AIError>;

    /// 生成文本
    async fn generate(&self, prompt: &str, options: &GenerationOptions) -> Result<String, AIError>;

    /// 清理资源
    async fn cleanup(&self) -> Result<(), AIError>;

    /// 获取模型信息
    fn get_model_info(&self) -> ModelInfo;

    /// 检查可用性
    fn is_available(&self) -> bool;

    /// 健康检查
    async fn health_check(&self) -> Result<(), AIError>;
}

/// AI 服务抽象接口
#[async_trait]
pub trait AIService: Send + Sync {
    /// 处理自然语言命令
    async fn process_command(
        &self,
        input: &str,
        context: &AIContext,
    ) -> Result<AIResponse, AIError>;

    /// 解释命令
    async fn explain_command(&self, command: &str) -> Result<String, AIError>;

    /// AI 聊天
    async fn chat(&self, message: &str, context: &ChatContext) -> Result<String, AIError>;

    /// 健康检查
    async fn health_check(&self) -> Result<(), AIError>;

    /// 获取能力
    fn get_capabilities(&self) -> AICapabilities;
}

/// Mock 模型提供者 (用于测试和演示)
pub struct MockProvider {
    config: Option<ProviderConfig>,
    is_initialized: bool,
}

impl MockProvider {
    pub fn new() -> Self {
        Self {
            config: None,
            is_initialized: false,
        }
    }
}

#[async_trait]
impl ModelProvider for MockProvider {
    async fn initialize(&mut self, config: ProviderConfig) -> Result<(), AIError> {
        self.config = Some(config);
        self.is_initialized = true;

        // 模拟初始化延迟
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        log::info!("Mock provider initialized");
        Ok(())
    }

    async fn generate(
        &self,
        prompt: &str,
        _options: &GenerationOptions,
    ) -> Result<String, AIError> {
        if !self.is_initialized {
            return Err(AIError::InitializationFailed(
                "Provider not initialized".to_string(),
            ));
        }

        // 模拟生成延迟
        tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

        // 简单的模式匹配模拟 AI 响应
        if prompt.contains("列出") || prompt.contains("显示") || prompt.contains("查看") {
            if prompt.contains("文件") || prompt.contains("目录") {
                Ok(r#"{"command": "ls -la", "description": "列出当前目录下的所有文件和文件夹，包括隐藏文件", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            } else if prompt.contains("进程") {
                Ok(r#"{"command": "ps aux", "description": "显示所有运行中的进程", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            } else {
                Ok(r#"{"command": "ls", "description": "列出当前目录内容", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            }
        } else if prompt.contains("创建") || prompt.contains("新建") {
            if prompt.contains("目录") || prompt.contains("文件夹") {
                Ok(r#"{"command": "mkdir new_directory", "description": "创建一个新目录", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            } else if prompt.contains("文件") {
                Ok(r#"{"command": "touch new_file.txt", "description": "创建一个新文件", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            } else {
                Ok(r#"{"command": "mkdir new_item", "description": "创建新项目", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            }
        } else if prompt.contains("删除") || prompt.contains("移除") {
            if prompt.contains("强制") || prompt.contains("递归") {
                Ok(r#"{"command": "rm -rf target", "description": "强制递归删除文件或目录", "risk_level": "high", "requires_confirmation": true}"#.to_string())
            } else {
                Ok(r#"{"command": "rm file.txt", "description": "删除指定文件", "risk_level": "medium", "requires_confirmation": true}"#.to_string())
            }
        } else if prompt.contains("复制") || prompt.contains("拷贝") {
            Ok(r#"{"command": "cp source.txt destination.txt", "description": "复制文件", "risk_level": "low", "requires_confirmation": false}"#.to_string())
        } else if prompt.contains("移动") || prompt.contains("重命名") {
            Ok(r#"{"command": "mv old_name.txt new_name.txt", "description": "移动或重命名文件", "risk_level": "medium", "requires_confirmation": true}"#.to_string())
        } else if prompt.contains("查找") || prompt.contains("搜索") {
            Ok(r#"{"command": "find . -name '*.txt'", "description": "在当前目录及子目录中搜索文件", "risk_level": "low", "requires_confirmation": false}"#.to_string())
        } else if prompt.contains("安装") || prompt.contains("install") {
            Ok(r#"{"command": "sudo apt install package_name", "description": "安装软件包", "risk_level": "medium", "requires_confirmation": true}"#.to_string())
        } else if prompt.contains("权限") || prompt.contains("chmod") {
            Ok(r#"{"command": "chmod 755 file.txt", "description": "修改文件权限", "risk_level": "medium", "requires_confirmation": true}"#.to_string())
        } else if prompt.contains("网络") || prompt.contains("ping") {
            Ok(r#"{"command": "ping google.com", "description": "测试网络连接", "risk_level": "low", "requires_confirmation": false}"#.to_string())
        } else if prompt.contains("压缩") || prompt.contains("解压") {
            if prompt.contains("解压") {
                Ok(r#"{"command": "tar -xzf archive.tar.gz", "description": "解压缩文件", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            } else {
                Ok(r#"{"command": "tar -czf archive.tar.gz directory/", "description": "压缩目录", "risk_level": "low", "requires_confirmation": false}"#.to_string())
            }
        } else {
            // 默认响应
            Ok(format!(
                r#"{{"command": "echo '{}'", "description": "回显用户输入", "risk_level": "low", "requires_confirmation": false}}"#,
                prompt.replace("\"", "\\\"")
            ))
        }
    }

    async fn cleanup(&self) -> Result<(), AIError> {
        log::info!("Mock provider cleaned up");
        Ok(())
    }

    fn get_model_info(&self) -> ModelInfo {
        ModelInfo {
            id: "mock-model".to_string(),
            name: "Mock AI Model".to_string(),
            provider: "Mock".to_string(),
            version: "1.0.0".to_string(),
            capabilities: vec![
                "command_generation".to_string(),
                "command_explanation".to_string(),
                "chat".to_string(),
            ],
            max_tokens: Some(1024),
            context_window: Some(4096),
        }
    }

    fn is_available(&self) -> bool {
        self.is_initialized
    }

    async fn health_check(&self) -> Result<(), AIError> {
        if !self.is_initialized {
            return Err(AIError::InitializationFailed(
                "Provider not initialized".to_string(),
            ));
        }
        Ok(())
    }
}

/// 本地模型提供者接口 (将在后续任务中实现)
pub struct LocalModelProvider {
    config: Option<ProviderConfig>,
    is_initialized: bool,
}

impl LocalModelProvider {
    pub fn new() -> Self {
        Self {
            config: None,
            is_initialized: false,
        }
    }
}

#[async_trait]
impl ModelProvider for LocalModelProvider {
    async fn initialize(&mut self, config: ProviderConfig) -> Result<(), AIError> {
        self.config = Some(config);
        self.is_initialized = false; // 还未实现
        Err(AIError::InitializationFailed(
            "Local model provider not implemented yet".to_string(),
        ))
    }

    async fn generate(
        &self,
        _prompt: &str,
        _options: &GenerationOptions,
    ) -> Result<String, AIError> {
        Err(AIError::GenerationFailed(
            "Local model provider not implemented yet".to_string(),
        ))
    }

    async fn cleanup(&self) -> Result<(), AIError> {
        Ok(())
    }

    fn get_model_info(&self) -> ModelInfo {
        ModelInfo {
            id: "local-model".to_string(),
            name: "Local AI Model".to_string(),
            provider: "Local".to_string(),
            version: "0.0.0".to_string(),
            capabilities: vec![],
            max_tokens: None,
            context_window: None,
        }
    }

    fn is_available(&self) -> bool {
        false // 还未实现
    }

    async fn health_check(&self) -> Result<(), AIError> {
        Err(AIError::ServiceUnavailable)
    }
}

/// OpenAI 提供者接口 (将在后续任务中实现)
pub struct OpenAIProvider {
    config: Option<ProviderConfig>,
    is_initialized: bool,
}

impl OpenAIProvider {
    pub fn new() -> Self {
        Self {
            config: None,
            is_initialized: false,
        }
    }
}

#[async_trait]
impl ModelProvider for OpenAIProvider {
    async fn initialize(&mut self, config: ProviderConfig) -> Result<(), AIError> {
        self.config = Some(config);
        self.is_initialized = false; // 还未实现
        Err(AIError::InitializationFailed(
            "OpenAI provider not implemented yet".to_string(),
        ))
    }

    async fn generate(
        &self,
        _prompt: &str,
        _options: &GenerationOptions,
    ) -> Result<String, AIError> {
        Err(AIError::GenerationFailed(
            "OpenAI provider not implemented yet".to_string(),
        ))
    }

    async fn cleanup(&self) -> Result<(), AIError> {
        Ok(())
    }

    fn get_model_info(&self) -> ModelInfo {
        ModelInfo {
            id: "openai-model".to_string(),
            name: "OpenAI Model".to_string(),
            provider: "OpenAI".to_string(),
            version: "0.0.0".to_string(),
            capabilities: vec![],
            max_tokens: None,
            context_window: None,
        }
    }

    fn is_available(&self) -> bool {
        false // 还未实现
    }

    async fn health_check(&self) -> Result<(), AIError> {
        Err(AIError::ServiceUnavailable)
    }
}

/// Claude 提供者接口 (将在后续任务中实现)
pub struct ClaudeProvider {
    config: Option<ProviderConfig>,
    is_initialized: bool,
}

impl ClaudeProvider {
    pub fn new() -> Self {
        Self {
            config: None,
            is_initialized: false,
        }
    }
}

#[async_trait]
impl ModelProvider for ClaudeProvider {
    async fn initialize(&mut self, config: ProviderConfig) -> Result<(), AIError> {
        self.config = Some(config);
        self.is_initialized = false; // 还未实现
        Err(AIError::InitializationFailed(
            "Claude provider not implemented yet".to_string(),
        ))
    }

    async fn generate(
        &self,
        _prompt: &str,
        _options: &GenerationOptions,
    ) -> Result<String, AIError> {
        Err(AIError::GenerationFailed(
            "Claude provider not implemented yet".to_string(),
        ))
    }

    async fn cleanup(&self) -> Result<(), AIError> {
        Ok(())
    }

    fn get_model_info(&self) -> ModelInfo {
        ModelInfo {
            id: "claude-model".to_string(),
            name: "Claude Model".to_string(),
            provider: "Claude".to_string(),
            version: "0.0.0".to_string(),
            capabilities: vec![],
            max_tokens: None,
            context_window: None,
        }
    }

    fn is_available(&self) -> bool {
        false // 还未实现
    }

    async fn health_check(&self) -> Result<(), AIError> {
        Err(AIError::ServiceUnavailable)
    }
}

// ============================================================================
// Deepseek Provider Implementation
// ============================================================================

use reqwest::Client;
use std::time::{Duration, Instant};
use tokio_stream::StreamExt;

/// Deepseek API 提供者
pub struct DeepseekProvider {
    client: Client,
    config: Option<ProviderConfig>,
    is_initialized: bool,
    models: Vec<DeepseekModel>,
    rate_limiter: Option<RateLimiter>,
}

/// 简单的速率限制器
#[derive(Debug)]
pub struct RateLimiter {
    last_request: std::sync::Mutex<Instant>,
    min_interval: Duration,
}

impl RateLimiter {
    pub fn new(requests_per_minute: u32) -> Self {
        let min_interval = Duration::from_secs(60) / requests_per_minute;
        Self {
            last_request: std::sync::Mutex::new(Instant::now() - min_interval),
            min_interval,
        }
    }

    pub async fn wait(&self) {
        let wait_time = {
            let last_request = self.last_request.lock().unwrap();
            let elapsed = last_request.elapsed();
            if elapsed < self.min_interval {
                Some(self.min_interval - elapsed)
            } else {
                None
            }
        };

        if let Some(wait_time) = wait_time {
            tokio::time::sleep(wait_time).await;
        }

        {
            let mut last_request = self.last_request.lock().unwrap();
            *last_request = Instant::now();
        }
    }
}

impl DeepseekProvider {
    pub fn new() -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            config: None,
            is_initialized: false,
            models: Vec::new(),
            rate_limiter: None,
        }
    }

    /// 验证 API 密钥
    async fn validate_api_key(&self, api_key: &str, endpoint: &str) -> Result<(), AIError> {
        let response = self
            .client
            .get(&format!("{}/models", endpoint))
            .header("Authorization", format!("Bearer {}", api_key))
            .send()
            .await
            .map_err(|e| AIError::NetworkError(e.to_string()))?;

        if !response.status().is_success() {
            return Err(AIError::AuthenticationFailed("Invalid API key".to_string()));
        }

        Ok(())
    }

    /// 获取可用模型列表
    async fn fetch_models(&mut self, api_key: &str, endpoint: &str) -> Result<(), AIError> {
        let response = self
            .client
            .get(&format!("{}/models", endpoint))
            .header("Authorization", format!("Bearer {}", api_key))
            .send()
            .await
            .map_err(|e| AIError::NetworkError(e.to_string()))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(AIError::GenerationFailed(format!(
                "Failed to fetch models: {}",
                error_text
            )));
        }

        let models_response: DeepseekModelsResponse = response
            .json()
            .await
            .map_err(|e| AIError::ParsingError(e.to_string()))?;

        self.models = models_response.data;
        log::info!("Fetched {} Deepseek models", self.models.len());

        Ok(())
    }

    /// 构建 API 请求
    fn build_request(
        &self,
        prompt: &str,
        options: &GenerationOptions,
        model_name: &str,
    ) -> DeepseekRequest {
        let messages = vec![DeepseekMessage {
            role: "user".to_string(),
            content: prompt.to_string(),
        }];

        DeepseekRequest {
            model: model_name.to_string(),
            messages,
            max_tokens: options.max_tokens,
            temperature: options.temperature,
            top_p: options.top_p,
            stream: Some(options.stream),
            stop: if options.stop_sequences.is_empty() {
                None
            } else {
                Some(options.stop_sequences.clone())
            },
        }
    }

    /// 发送同步请求
    async fn generate_sync(
        &self,
        request: DeepseekRequest,
        api_key: &str,
        endpoint: &str,
    ) -> Result<String, AIError> {
        // 应用速率限制
        if let Some(rate_limiter) = &self.rate_limiter {
            rate_limiter.wait().await;
        }

        let response = self
            .client
            .post(&format!("{}/chat/completions", endpoint))
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await
            .map_err(|e| AIError::NetworkError(e.to_string()))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();

            // 尝试解析错误响应
            if let Ok(error_response) = serde_json::from_str::<DeepseekError>(&error_text) {
                return Err(AIError::GenerationFailed(error_response.error.message));
            }

            return Err(AIError::GenerationFailed(format!(
                "API error: {}",
                error_text
            )));
        }

        let deepseek_response: DeepseekResponse = response
            .json()
            .await
            .map_err(|e| AIError::ParsingError(e.to_string()))?;

        deepseek_response
            .choices
            .first()
            .and_then(|choice| choice.message.as_ref())
            .map(|message| message.content.clone())
            .ok_or_else(|| AIError::GenerationFailed("No response content".to_string()))
    }

    /// 发送流式请求
    async fn generate_stream(
        &self,
        mut request: DeepseekRequest,
        api_key: &str,
        endpoint: &str,
    ) -> Result<String, AIError> {
        request.stream = Some(true);

        // 应用速率限制
        if let Some(rate_limiter) = &self.rate_limiter {
            rate_limiter.wait().await;
        }

        let response = self
            .client
            .post(&format!("{}/chat/completions", endpoint))
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await
            .map_err(|e| AIError::NetworkError(e.to_string()))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(AIError::GenerationFailed(format!(
                "API error: {}",
                error_text
            )));
        }

        let mut stream = response.bytes_stream();
        let mut complete_response = String::new();

        while let Some(chunk) = stream.next().await {
            let chunk = chunk.map_err(|e| AIError::NetworkError(e.to_string()))?;
            let chunk_str = String::from_utf8_lossy(&chunk);

            for line in chunk_str.lines() {
                if line.starts_with("data: ") {
                    let data = &line[6..];
                    if data == "[DONE]" {
                        break;
                    }

                    if let Ok(stream_response) = serde_json::from_str::<DeepseekResponse>(data) {
                        if let Some(choice) = stream_response.choices.first() {
                            if let Some(delta) = &choice.delta {
                                if let Some(content) = &delta.content {
                                    complete_response.push_str(content);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(complete_response)
    }
}

#[async_trait]
impl ModelProvider for DeepseekProvider {
    async fn initialize(&mut self, config: ProviderConfig) -> Result<(), AIError> {
        // 验证配置
        let api_key = config
            .api_key
            .as_ref()
            .ok_or_else(|| AIError::ConfigError("Deepseek API key is required".to_string()))?;

        let endpoint = config
            .api_endpoint
            .as_ref()
            .map(|s| s.as_str())
            .unwrap_or("https://api.deepseek.com");

        // 验证 API 密钥
        self.validate_api_key(api_key, endpoint).await?;

        // 获取可用模型列表
        self.fetch_models(api_key, endpoint).await?;

        // 初始化速率限制器
        self.rate_limiter = Some(RateLimiter::new(60)); // 每分钟60个请求

        self.config = Some(config);
        self.is_initialized = true;

        log::info!("Deepseek provider initialized successfully");
        Ok(())
    }

    async fn generate(&self, prompt: &str, options: &GenerationOptions) -> Result<String, AIError> {
        if !self.is_initialized {
            return Err(AIError::InitializationFailed(
                "Provider not initialized".to_string(),
            ));
        }

        let config = self.config.as_ref().unwrap();
        let api_key = config.api_key.as_ref().unwrap();
        let endpoint = config
            .api_endpoint
            .as_ref()
            .map(|s| s.as_str())
            .unwrap_or("https://api.deepseek.com");

        let request = self.build_request(prompt, options, &config.model_name);

        // 直接执行，暂时不使用重试机制来避免复杂的异步闭包问题
        if options.stream {
            self.generate_stream(request, api_key, endpoint).await
        } else {
            self.generate_sync(request, api_key, endpoint).await
        }
    }

    async fn cleanup(&self) -> Result<(), AIError> {
        log::info!("Deepseek provider cleaned up");
        Ok(())
    }

    fn get_model_info(&self) -> ModelInfo {
        let config = self.config.as_ref();
        let model_name = config
            .map(|c| c.model_name.clone())
            .unwrap_or_else(|| "deepseek-chat".to_string());

        ModelInfo {
            id: format!("deepseek-{}", model_name),
            name: model_name.clone(),
            provider: "Deepseek".to_string(),
            version: "1.0.0".to_string(),
            capabilities: vec![
                "command_generation".to_string(),
                "command_explanation".to_string(),
                "chat".to_string(),
                "code_analysis".to_string(),
            ],
            max_tokens: config.and_then(|c| c.max_tokens).or(Some(4096)),
            context_window: Some(32768), // Deepseek 支持 32k context
        }
    }

    fn is_available(&self) -> bool {
        self.is_initialized && self.config.is_some()
    }

    async fn health_check(&self) -> Result<(), AIError> {
        if !self.is_initialized {
            return Err(AIError::InitializationFailed(
                "Provider not initialized".to_string(),
            ));
        }

        let config = self.config.as_ref().unwrap();
        let api_key = config.api_key.as_ref().unwrap();
        let endpoint = config
            .api_endpoint
            .as_ref()
            .map(|s| s.as_str())
            .unwrap_or("https://api.deepseek.com");

        // 简单的健康检查：尝试获取模型列表
        let response = self
            .client
            .get(&format!("{}/models", endpoint))
            .header("Authorization", format!("Bearer {}", api_key))
            .timeout(Duration::from_secs(10))
            .send()
            .await
            .map_err(|e| AIError::NetworkError(e.to_string()))?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(AIError::ServiceUnavailable)
        }
    }
}
