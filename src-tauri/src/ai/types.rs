use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;

/// AI 提供者类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProviderType {
    Local,
    OpenAI,
    Claude,
    Gemini,
    Deepseek,
    Mock,
}

/// 响应类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResponseType {
    Command,
    Explanation,
    Chat,
    Error,
}

/// 风险等级
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// AI 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIConfig {
    pub default_provider: String,
    pub providers: HashMap<String, ProviderConfig>,
    pub fallback_enabled: bool,
    pub cache_enabled: bool,
    pub timeout_ms: u64,
    pub max_retries: u32,
}

impl Default for AIConfig {
    fn default() -> Self {
        let mut providers = HashMap::new();

        // 默认 Mock 提供者配置
        providers.insert(
            "mock".to_string(),
            ProviderConfig {
                provider_type: ProviderType::Mock,
                model_name: "mock-model".to_string(),
                api_endpoint: None,
                api_key: None,
                max_tokens: Some(1024),
                temperature: Some(0.7),
                custom_params: HashMap::new(),
            },
        );

        // Deepseek 提供者配置 (需要用户配置 API key)
        providers.insert(
            "deepseek".to_string(),
            ProviderConfig {
                provider_type: ProviderType::Deepseek,
                model_name: "deepseek-chat".to_string(),
                api_endpoint: Some("https://api.deepseek.com".to_string()),
                api_key: None, // 需要用户设置环境变量 DEEPSEEK_API_KEY
                max_tokens: Some(4096),
                temperature: Some(0.7),
                custom_params: HashMap::new(),
            },
        );

        Self {
            default_provider: "mock".to_string(),
            providers,
            fallback_enabled: true,
            cache_enabled: true,
            timeout_ms: 30000,
            max_retries: 3,
        }
    }
}

/// 提供者配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderConfig {
    pub provider_type: ProviderType,
    pub model_name: String,
    pub api_endpoint: Option<String>,
    pub api_key: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub custom_params: HashMap<String, serde_json::Value>,
}

/// AI 上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIContext {
    pub session_id: String,
    pub current_directory: String,
    pub shell_type: String,
    pub environment: HashMap<String, String>,
    pub command_history: Vec<String>,
    pub user_preferences: UserPreferences,
}

impl Default for AIContext {
    fn default() -> Self {
        Self {
            session_id: uuid::Uuid::new_v4().to_string(),
            current_directory: "/".to_string(),
            shell_type: "bash".to_string(),
            environment: HashMap::new(),
            command_history: Vec::new(),
            user_preferences: UserPreferences::default(),
        }
    }
}

/// 用户偏好设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub preferred_commands: HashMap<String, String>,
    pub risk_tolerance: RiskLevel,
    pub confirmation_required: bool,
    pub language: String,
}

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            preferred_commands: HashMap::new(),
            risk_tolerance: RiskLevel::Medium,
            confirmation_required: true,
            language: "zh-CN".to_string(),
        }
    }
}

/// AI 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIResponse {
    pub response_type: ResponseType,
    pub content: String,
    pub confidence: f32,
    pub suggested_commands: Vec<SuggestedCommand>,
    pub requires_confirmation: bool,
    pub metadata: ResponseMetadata,
}

/// 建议命令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuggestedCommand {
    pub command: String,
    pub description: String,
    pub risk_level: RiskLevel,
    pub estimated_duration: Option<String>,
}

/// 响应元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseMetadata {
    pub provider: String,
    pub model: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub processing_time_ms: u64,
    pub cached: bool,
}

/// 聊天上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatContext {
    pub session_id: String,
    pub history: Vec<ChatMessage>,
    pub max_history: usize,
}

impl Default for ChatContext {
    fn default() -> Self {
        Self {
            session_id: uuid::Uuid::new_v4().to_string(),
            history: Vec::new(),
            max_history: 50,
        }
    }
}

/// 聊天消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub role: String, // "user" or "assistant"
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 模型信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelInfo {
    pub id: String,
    pub name: String,
    pub provider: String,
    pub version: String,
    pub capabilities: Vec<String>,
    pub max_tokens: Option<u32>,
    pub context_window: Option<u32>,
}

/// AI 能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AICapabilities {
    pub command_generation: bool,
    pub command_explanation: bool,
    pub chat: bool,
    pub code_analysis: bool,
    pub file_operations: bool,
    pub system_administration: bool,
}

impl Default for AICapabilities {
    fn default() -> Self {
        Self {
            command_generation: true,
            command_explanation: true,
            chat: true,
            code_analysis: false,
            file_operations: true,
            system_administration: false,
        }
    }
}

/// 生成选项
#[derive(Debug, Clone)]
pub struct GenerationOptions {
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub stop_sequences: Vec<String>,
    pub stream: bool,
}

impl Default for GenerationOptions {
    fn default() -> Self {
        Self {
            max_tokens: Some(1024),
            temperature: Some(0.7),
            top_p: Some(0.9),
            stop_sequences: Vec::new(),
            stream: false,
        }
    }
}

/// AI 错误类型
#[derive(Debug, Error, Clone)]
pub enum AIError {
    #[error("Provider not available: {0}")]
    ProviderNotAvailable(String),

    #[error("Model initialization failed: {0}")]
    InitializationFailed(String),

    #[error("Generation failed: {0}")]
    GenerationFailed(String),

    #[error("Timeout: operation took too long")]
    Timeout,

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Parsing error: {0}")]
    ParsingError(String),

    #[error("Authentication failed: {0}")]
    AuthenticationFailed(String),

    #[error("Rate limit exceeded")]
    RateLimitExceeded,

    #[error("Service unavailable")]
    ServiceUnavailable,
}

// ============================================================================
// Deepseek 相关数据结构
// ============================================================================

/// Deepseek API 请求
#[derive(Debug, Clone, Serialize)]
pub struct DeepseekRequest {
    pub model: String,
    pub messages: Vec<DeepseekMessage>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub top_p: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub stream: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub stop: Option<Vec<String>>,
}

/// Deepseek API 消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeepseekMessage {
    pub role: String,
    pub content: String,
}

/// Deepseek API 响应
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekResponse {
    pub id: String,
    pub object: String,
    pub created: u64,
    pub model: String,
    pub choices: Vec<DeepseekChoice>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub usage: Option<DeepseekUsage>,
}

/// Deepseek 选择项
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekChoice {
    pub index: u32,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<DeepseekMessage>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub delta: Option<DeepseekDelta>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub finish_reason: Option<String>,
}

/// Deepseek 流式响应的增量
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekDelta {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub content: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub role: Option<String>,
}

/// Deepseek 使用情况统计
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// Deepseek 模型列表响应
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekModelsResponse {
    pub data: Vec<DeepseekModel>,
}

/// Deepseek 模型信息
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekModel {
    pub id: String,
    pub object: String,
    pub created: u64,
    pub owned_by: String,
}

/// Deepseek 错误响应
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekError {
    pub error: DeepseekErrorDetail,
}

/// Deepseek 错误详情
#[derive(Debug, Clone, Deserialize)]
pub struct DeepseekErrorDetail {
    pub message: String,
    #[serde(rename = "type")]
    pub error_type: String,
    pub param: Option<String>,
    pub code: Option<String>,
}

/// 速率限制器配置
#[derive(Debug, Clone)]
pub struct RateLimiterConfig {
    pub requests_per_minute: u32,
    pub tokens_per_minute: u32,
}

impl Default for RateLimiterConfig {
    fn default() -> Self {
        Self {
            requests_per_minute: 60,
            tokens_per_minute: 100000,
        }
    }
}

/// 命令模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub patterns: Vec<String>,
    pub command_template: String,
    pub parameters: Vec<TemplateParameter>,
    pub examples: Vec<TemplateExample>,
    pub category: CommandCategory,
    pub risk_level: RiskLevel,
}

/// 模板参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateParameter {
    pub name: String,
    pub param_type: ParameterType,
    pub required: bool,
    pub default_value: Option<String>,
    pub validation_rules: Vec<ValidationRule>,
    pub extraction_patterns: Vec<String>,
}

/// 模板示例
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateExample {
    pub input: String,
    pub output: String,
    pub description: String,
}

/// 参数类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ParameterType {
    String,
    Path,
    Flags,
    Number,
    Host,
    Signal,
    ProcessId,
    Port,
    Url,
}

/// 验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationRule {
    PathExists,
    ValidHost,
    ValidNumber,
    ValidSignal,
    NotEmpty,
}

/// 命令分类
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CommandCategory {
    FileSystem,
    Process,
    Network,
    Git,
    System,
    Development,
    Database,
    Security,
    General,
}

/// NLP 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NLPResponse {
    pub generated_command: GeneratedCommand,
    pub explanation: CommandExplanation,
    pub suggestions: Vec<CommandSuggestion>,
    pub risk_assessment: RiskAssessment,
    pub confidence: f32,
    pub metadata: NLPMetadata,
}

/// 生成的命令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratedCommand {
    pub command: String,
    pub parsed_parts: Vec<CommandPart>,
    pub execution_context: ExecutionContext,
    pub requires_confirmation: bool,
}

/// 命令部分
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandPart {
    pub part_type: CommandPartType,
    pub value: String,
    pub description: String,
}

/// 命令部分类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandPartType {
    Program,
    Argument,
    Flag,
    Parameter,
    Pipe,
    Redirect,
}

/// 执行上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionContext {
    pub working_directory: String,
    pub required_permissions: Vec<String>,
    pub expected_output_type: OutputType,
    pub side_effects: Vec<String>,
}

/// 输出类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputType {
    Text,
    Binary,
    Json,
    Xml,
    None,
}

/// 命令解释
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandExplanation {
    pub summary: String,
    pub detailed_breakdown: Vec<ParameterExplanation>,
    pub expected_output: String,
    pub potential_side_effects: Vec<String>,
}

/// 参数解释
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParameterExplanation {
    pub parameter: String,
    pub description: String,
    pub example: Option<String>,
    pub is_optional: bool,
}

/// 命令建议
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandSuggestion {
    pub suggestion_type: SuggestionType,
    pub content: String,
    pub priority: u8,
    pub category: SuggestionCategory,
}

/// 建议类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SuggestionType {
    Alternative,    // 替代命令
    Optimization,   // 优化建议
    SafetyTip,      // 安全提示
    LearningTip,    // 学习建议
    RelatedCommand, // 相关命令
}

/// 建议分类
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SuggestionCategory {
    Performance,
    Safety,
    Learning,
    Alternative,
    BestPractice,
}

/// 风险评估
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    pub risk_level: RiskLevel,
    pub risk_factors: Vec<RiskFactor>,
    pub mitigation_suggestions: Vec<String>,
    pub requires_confirmation: bool,
}

/// 风险因素
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    pub factor_type: RiskFactorType,
    pub description: String,
    pub severity: u8, // 1-10
}

/// 风险因素类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskFactorType {
    DataLoss,
    SecurityRisk,
    SystemDamage,
    NetworkExposure,
    PermissionEscalation,
    ResourceConsumption,
}

/// NLP 元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NLPMetadata {
    pub processing_time_ms: u64,
    pub strategy_used: ProcessingStrategy,
    pub model_version: String,
    pub template_matches: u32,
    pub intent_confidence: f32,
}

/// 处理策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProcessingStrategy {
    TemplateMatching,
    AIGeneration,
    HybridApproach,
    RuleBased,
}

/// 意图
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Intent {
    pub intent_type: IntentType,
    pub confidence: f32,
    pub context_dependent: bool,
}

/// 意图类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IntentType {
    FileOperation,
    ProcessControl,
    SystemInfo,
    NetworkOperation,
    GitOperation,
    Development,
    Search,
    Navigation,
    Configuration,
    Help,
    Unknown,
}

impl std::fmt::Display for IntentType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            IntentType::FileOperation => write!(f, "file_operation"),
            IntentType::ProcessControl => write!(f, "process_control"),
            IntentType::SystemInfo => write!(f, "system_info"),
            IntentType::NetworkOperation => write!(f, "network_operation"),
            IntentType::GitOperation => write!(f, "git_operation"),
            IntentType::Development => write!(f, "development"),
            IntentType::Search => write!(f, "search"),
            IntentType::Navigation => write!(f, "navigation"),
            IntentType::Configuration => write!(f, "configuration"),
            IntentType::Help => write!(f, "help"),
            IntentType::Unknown => write!(f, "unknown"),
        }
    }
}

/// 实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Entity {
    pub entity_type: EntityType,
    pub value: String,
    pub confidence: f32,
    pub start_index: usize,
    pub end_index: usize,
    pub context: Option<String>,
}

/// 实体类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EntityType {
    FilePath,
    DirectoryPath,
    Command,
    Number,
    Url,
    IpAddress,
    ProcessId,
    Port,
    FileName,
    Extension,
    Username,
    Hostname,
    EnvironmentVariable,
}

/// 清理后的输入
#[derive(Debug, Clone)]
pub struct CleanedInput {
    pub original: String,
    pub normalized: String,
    pub tokens: Vec<String>,
    pub sanitized: String,
}

/// 生成结果
#[derive(Debug, Clone)]
pub struct GenerationResult {
    pub command: String,
    pub confidence: f32,
    pub strategy: ProcessingStrategy,
    pub template_matches: u32,
}

/// 上下文分析错误
#[derive(Debug, Error)]
pub enum ContextError {
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Permission denied: {0}")]
    PermissionDenied(String),
    #[error("Path not found: {0}")]
    PathNotFound(String),
    #[error("Analysis failed: {0}")]
    AnalysisFailed(String),
}

/// NLP 错误
#[derive(Debug, Error)]
pub enum NLPError {
    #[error("Parsing failed: {0}")]
    ParsingFailed(String),
    #[error("Context analysis failed: {0}")]
    ContextAnalysisFailed(#[from] ContextError),
    #[error("AI service error: {0}")]
    AIServiceError(#[from] AIError),
    #[error("Suggestion error: {0}")]
    SuggestionError(#[from] SuggestionError),
    #[error("Template matching failed: {0}")]
    TemplateMatchingFailed(String),
    #[error("Entity extraction failed: {0}")]
    EntityExtractionFailed(String),
    #[error("Intent detection failed: {0}")]
    IntentDetectionFailed(String),
    #[error("Validation error: {0}")]
    ValidationError(String),
}

impl From<String> for NLPError {
    fn from(s: String) -> Self {
        NLPError::ValidationError(s)
    }
}

/// 建议错误
#[derive(Debug, Error)]
pub enum SuggestionError {
    #[error("Generation failed: {0}")]
    GenerationFailed(String),
    #[error("Database error: {0}")]
    DatabaseError(String),
    #[error("Analysis error: {0}")]
    AnalysisError(String),
}

/// 终端上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalContext {
    pub current_directory: String,
    pub shell_type: String,
    pub environment_variables: HashMap<String, String>,
    pub active_processes: Vec<ProcessInfo>,
    pub command_history: Vec<HistoryEntry>,
    pub user: String,
    pub hostname: String,
    pub platform: String,
}

/// 进程信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessInfo {
    pub pid: u32,
    pub name: String,
    pub command_line: String,
    pub cpu_usage: f32,
    pub memory_usage: u64,
}

/// 历史记录项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistoryEntry {
    pub command: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub exit_code: Option<i32>,
    pub directory: String,
}
