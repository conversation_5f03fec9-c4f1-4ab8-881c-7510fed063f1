// AI module - Artificial Intelligence functionality
// This module provides AI services including local models and cloud APIs

// Core new architecture modules
pub mod commands;
pub mod manager;
pub mod provider;
pub mod types;

// Chat functionality modules
pub mod chat_commands;
pub mod chat_manager;

// NLP processing modules
pub mod context_analyzer;
pub mod nlp_processor;
pub mod suggestion_engine;
pub mod template_engine;

use std::sync::Arc;
use tokio::sync::RwLock;

// Re-export the new unified architecture
pub use commands::*;
pub use manager::*;
pub use provider::*;
pub use types::*;

// Chat functionality re-exports
pub use chat_commands::*;
pub use chat_manager::*;

// Legacy re-exports for backward compatibility
pub use nlp_processor::*;

/// Initialize AI services with default configuration
pub async fn initialize_ai_services() -> Result<Arc<RwLock<AIServiceManager>>, AIError> {
    log::info!("Initializing AI services");

    let config = AIConfig::default();
    let mut manager = AIServiceManager::new(config);

    match manager.initialize().await {
        Ok(_) => {
            log::info!("AI services initialized successfully");
            Ok(Arc::new(RwLock::new(manager)))
        }
        Err(e) => {
            log::error!("Failed to initialize AI services: {}", e);
            Err(e)
        }
    }
}

/// Create AI service manager with custom configuration
pub async fn create_ai_manager(config: AIConfig) -> Result<Arc<RwLock<AIServiceManager>>, AIError> {
    log::info!("Creating AI manager with custom config");

    let mut manager = AIServiceManager::new(config);
    manager.initialize().await?;

    Ok(Arc::new(RwLock::new(manager)))
}

// ============================================================================
// Legacy AI Manager for backward compatibility
// ============================================================================

/// Legacy AI Manager - Main coordinator for AI services
pub struct LegacyAIManager {
    ai_manager: Arc<RwLock<AIServiceManager>>,
}

impl LegacyAIManager {
    pub async fn new() -> Result<Self, AIError> {
        let ai_manager = initialize_ai_services().await?;
        Ok(Self { ai_manager })
    }

    /// Generate command from natural language
    pub async fn generate_command(
        &self,
        input: &str,
        _context: Option<&str>,
    ) -> Result<String, AIError> {
        let context = AIContext::default();
        let manager = self.ai_manager.read().await;
        let response = manager.process_natural_language(input, &context).await?;
        Ok(response.content)
    }

    /// Chat with AI
    pub async fn chat(&self, message: &str, _context: Option<&str>) -> Result<String, AIError> {
        let context = ChatContext::default();
        let manager = self.ai_manager.read().await;
        manager.chat(message, &context).await
    }

    /// Explain command
    pub async fn explain_command(&self, command: &str) -> Result<String, AIError> {
        let manager = self.ai_manager.read().await;
        manager.explain_command(command).await
    }

    /// List available AI services
    pub async fn list_services(&self) -> Vec<String> {
        // Return available providers
        vec!["mock".to_string(), "local".to_string(), "cloud".to_string()]
    }

    /// Add a new AI service (placeholder for future expansion)
    pub async fn add_service(&self, _name: String, _service: Box<dyn AIService + Send + Sync>) {
        // This will be implemented when we add dynamic service registration
        log::warn!("Dynamic service registration not yet implemented");
    }
}
