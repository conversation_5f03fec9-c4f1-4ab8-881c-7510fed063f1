use crate::ai::types::*;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 上下文分析器
pub struct ContextAnalyzer {
    terminal_state: Arc<RwLock<TerminalState>>,
    history_analyzer: HistoryAnalyzer,
    environment_parser: EnvironmentParser,
    project_detector: ProjectDetector,
}

/// 终端状态
#[derive(Debug, Clone)]
pub struct TerminalState {
    pub current_directory: String,
    pub command_history: Vec<String>,
    pub environment: HashMap<String, String>,
    pub shell_type: String,
    pub user: String,
    pub hostname: String,
    pub platform: String,
}

/// 增强的上下文信息
#[derive(Debug, Clone)]
pub struct EnrichedContext {
    pub base: TerminalContext,
    pub directory: DirectoryContext,
    pub history: HistoryPatterns,
    pub environment: EnvironmentContext,
    pub project: ProjectContext,
    pub intent: IntentContext,
    pub timestamp: DateTime<Utc>,
}

/// 目录上下文
#[derive(Debug, Clone)]
pub struct DirectoryContext {
    pub current_path: String,
    pub files: Vec<FileInfo>,
    pub special_files: Vec<SpecialFile>,
    pub path_features: PathFeatures,
    pub is_git_repo: bool,
    pub is_project_root: bool,
    pub disk_usage: DiskUsage,
}

/// 文件信息
#[derive(Debug, Clone)]
pub struct FileInfo {
    pub name: String,
    pub is_dir: bool,
    pub size: u64,
    pub permissions: FilePermissions,
    pub last_modified: Option<DateTime<Utc>>,
}

/// 特殊文件
#[derive(Debug, Clone)]
pub struct SpecialFile {
    pub file_type: SpecialFileType,
    pub name: String,
    pub significance: String,
}

/// 特殊文件类型
#[derive(Debug, Clone)]
pub enum SpecialFileType {
    GitConfig,   // .git/
    PackageJson, // package.json
    Dockerfile,  // Dockerfile
    Makefile,    // Makefile
    ReadMe,      // README.md
    Config,      // 配置文件
    Hidden,      // 隐藏文件
    Executable,  // 可执行文件
}

/// 路径特征
#[derive(Debug, Clone)]
pub struct PathFeatures {
    pub depth: u32,
    pub is_home: bool,
    pub is_root: bool,
    pub is_tmp: bool,
    pub is_system: bool,
    pub contains_spaces: bool,
}

/// 文件权限
#[derive(Debug, Clone)]
pub struct FilePermissions {
    pub readable: bool,
    pub writable: bool,
    pub executable: bool,
    pub is_symlink: bool,
}

/// 磁盘使用情况
#[derive(Debug, Clone)]
pub struct DiskUsage {
    pub total_space: u64,
    pub free_space: u64,
    pub used_space: u64,
    pub usage_percentage: f32,
}

/// 历史模式分析
#[derive(Debug, Clone)]
pub struct HistoryPatterns {
    pub frequent_commands: Vec<CommandFrequency>,
    pub recent_patterns: Vec<CommandPattern>,
    pub user_preferences: UserCommandPreferences,
    pub error_patterns: Vec<ErrorPattern>,
}

/// 命令频率
#[derive(Debug, Clone)]
pub struct CommandFrequency {
    pub command: String,
    pub count: u32,
    pub last_used: DateTime<Utc>,
    pub success_rate: f32,
}

/// 命令模式
#[derive(Debug, Clone)]
pub struct CommandPattern {
    pub pattern: String,
    pub frequency: u32,
    pub context: String,
}

/// 用户命令偏好
#[derive(Debug, Clone)]
pub struct UserCommandPreferences {
    pub preferred_flags: HashMap<String, String>,
    pub command_aliases: HashMap<String, String>,
    pub working_patterns: Vec<String>,
}

/// 错误模式
#[derive(Debug, Clone)]
pub struct ErrorPattern {
    pub command: String,
    pub error_type: String,
    pub frequency: u32,
    pub last_occurrence: DateTime<Utc>,
}

/// 环境上下文
#[derive(Debug, Clone)]
pub struct EnvironmentContext {
    pub shell_type: String,
    pub shell_version: Option<String>,
    pub path_dirs: Vec<String>,
    pub important_vars: HashMap<String, String>,
    pub developer_tools: Vec<DeveloperTool>,
}

/// 开发者工具
#[derive(Debug, Clone)]
pub struct DeveloperTool {
    pub name: String,
    pub version: Option<String>,
    pub path: String,
    pub is_available: bool,
}

/// 项目上下文
#[derive(Debug, Clone)]
pub struct ProjectContext {
    pub project_type: ProjectType,
    pub language: Option<String>,
    pub framework: Option<String>,
    pub build_system: Option<String>,
    pub package_manager: Option<String>,
    pub dependencies: Vec<String>,
}

/// 项目类型
#[derive(Debug, Clone)]
pub enum ProjectType {
    NodeJs,
    Python,
    Rust,
    Go,
    Java,
    CSharp,
    Cpp,
    React,
    Vue,
    Angular,
    Docker,
    Unknown,
}

/// 意图上下文
#[derive(Debug, Clone)]
pub struct IntentContext {
    pub likely_next_actions: Vec<String>,
    pub context_hints: Vec<String>,
    pub related_files: Vec<String>,
}

/// 历史分析器
pub struct HistoryAnalyzer {
    pattern_cache: Arc<RwLock<HashMap<String, Vec<CommandPattern>>>>,
}

/// 环境解析器
pub struct EnvironmentParser {
    shell_detectors: HashMap<String, Box<dyn Fn(&str) -> bool + Send + Sync>>,
}

/// 项目检测器
pub struct ProjectDetector {
    detectors: Vec<Box<dyn ProjectTypeDetector + Send + Sync>>,
}

/// 项目类型检测器
pub trait ProjectTypeDetector: Send + Sync {
    fn detect(&self, path: &Path) -> Option<ProjectContext>;
    fn confidence(&self) -> f32;
}

impl ContextAnalyzer {
    pub async fn new() -> Self {
        Self {
            terminal_state: Arc::new(RwLock::new(TerminalState::default())),
            history_analyzer: HistoryAnalyzer::new(),
            environment_parser: EnvironmentParser::new(),
            project_detector: ProjectDetector::new(),
        }
    }

    /// 分析完整上下文
    pub async fn analyze_context(
        &self,
        base_context: &TerminalContext,
        entities: &[Entity],
    ) -> Result<EnrichedContext, ContextError> {
        let terminal_state = self.terminal_state.read().await;

        // 分析当前目录上下文
        let directory_context = self
            .analyze_directory_context(&terminal_state.current_directory)
            .await?;

        // 分析命令历史模式
        let history_patterns = self
            .history_analyzer
            .analyze_patterns(&terminal_state.command_history)?;

        // 分析环境变量
        let environment_context = self.environment_parser.parse(&terminal_state.environment)?;

        // 检测项目类型
        let project_context = self
            .project_detector
            .detect_project_type(&terminal_state.current_directory)
            .await?;

        // 分析用户意图的上下文依赖
        let intent_context = self
            .analyze_intent_context(entities, &directory_context)
            .await?;

        Ok(EnrichedContext {
            base: base_context.clone(),
            directory: directory_context,
            history: history_patterns,
            environment: environment_context,
            project: project_context,
            intent: intent_context,
            timestamp: Utc::now(),
        })
    }

    /// 更新终端状态
    pub async fn update_terminal_state(
        &self,
        context: &TerminalContext,
    ) -> Result<(), ContextError> {
        let mut state = self.terminal_state.write().await;
        state.current_directory = context.current_directory.clone();
        state.environment = context.environment_variables.clone();
        state.shell_type = context.shell_type.clone();
        state.user = context.user.clone();
        state.hostname = context.hostname.clone();
        state.platform = context.platform.clone();

        // 更新命令历史
        for entry in &context.command_history {
            if !state.command_history.contains(&entry.command) {
                state.command_history.push(entry.command.clone());
            }
        }

        // 保持历史记录在合理范围内
        if state.command_history.len() > 1000 {
            state.command_history.drain(0..500);
        }

        Ok(())
    }

    async fn analyze_directory_context(
        &self,
        path: &str,
    ) -> Result<DirectoryContext, ContextError> {
        let path_obj = Path::new(path);

        // 分析目录结构
        let files = self.analyze_files(path_obj).await?;

        // 检测特殊文件
        let special_files = self.detect_special_files(&files);

        // 分析路径特征
        let path_features = self.analyze_path_features(path_obj);

        // 检查是否为 Git 仓库
        let is_git_repo = path_obj.join(".git").exists();

        // 检查是否为项目根目录
        let is_project_root = self.is_project_root(path_obj);

        // 计算磁盘使用情况
        let disk_usage = self.calculate_disk_usage(path_obj).await?;

        Ok(DirectoryContext {
            current_path: path.to_string(),
            files,
            special_files,
            path_features,
            is_git_repo,
            is_project_root,
            disk_usage,
        })
    }

    async fn analyze_files(&self, path: &Path) -> Result<Vec<FileInfo>, ContextError> {
        let mut files = Vec::new();

        let entries = fs::read_dir(path).map_err(|e| ContextError::IoError(e))?;

        for entry in entries {
            let entry = entry.map_err(|e| ContextError::IoError(e))?;
            let metadata = entry.metadata().map_err(|e| ContextError::IoError(e))?;

            let file_info = FileInfo {
                name: entry.file_name().to_string_lossy().to_string(),
                is_dir: metadata.is_dir(),
                size: metadata.len(),
                permissions: self.analyze_permissions(&entry.path()),
                last_modified: metadata.modified().ok().and_then(|time| {
                    DateTime::from_timestamp(
                        time.duration_since(std::time::UNIX_EPOCH)
                            .unwrap_or_default()
                            .as_secs() as i64,
                        0,
                    )
                }),
            };

            files.push(file_info);
        }

        Ok(files)
    }

    fn detect_special_files(&self, files: &[FileInfo]) -> Vec<SpecialFile> {
        let mut special_files = Vec::new();

        for file in files {
            if let Some(special_type) = self.classify_special_file(&file.name) {
                special_files.push(SpecialFile {
                    file_type: special_type.clone(),
                    name: file.name.clone(),
                    significance: self.get_file_significance(&special_type),
                });
            }
        }

        special_files
    }

    fn classify_special_file(&self, name: &str) -> Option<SpecialFileType> {
        match name.to_lowercase().as_str() {
            ".git" => Some(SpecialFileType::GitConfig),
            "package.json" => Some(SpecialFileType::PackageJson),
            "dockerfile" => Some(SpecialFileType::Dockerfile),
            "makefile" => Some(SpecialFileType::Makefile),
            name if name.starts_with("readme") => Some(SpecialFileType::ReadMe),
            name if name.starts_with(".") => Some(SpecialFileType::Hidden),
            _ => None,
        }
    }

    fn get_file_significance(&self, file_type: &SpecialFileType) -> String {
        match file_type {
            SpecialFileType::GitConfig => "Git版本控制仓库".to_string(),
            SpecialFileType::PackageJson => "Node.js项目配置文件".to_string(),
            SpecialFileType::Dockerfile => "Docker容器配置".to_string(),
            SpecialFileType::Makefile => "构建配置文件".to_string(),
            SpecialFileType::ReadMe => "项目说明文档".to_string(),
            SpecialFileType::Config => "配置文件".to_string(),
            SpecialFileType::Hidden => "隐藏文件".to_string(),
            SpecialFileType::Executable => "可执行文件".to_string(),
        }
    }

    fn analyze_permissions(&self, path: &Path) -> FilePermissions {
        // 简化的权限分析，可以根据平台进行扩展
        FilePermissions {
            readable: path.exists(),
            writable: true,    // 简化处理
            executable: false, // 简化处理
            is_symlink: path.is_symlink(),
        }
    }

    fn analyze_path_features(&self, path: &Path) -> PathFeatures {
        let path_str = path.to_string_lossy();
        let components: Vec<_> = path.components().collect();

        PathFeatures {
            depth: components.len() as u32,
            is_home: path_str.contains("/home/") || path_str.contains("/Users/"),
            is_root: path == Path::new("/"),
            is_tmp: path_str.contains("/tmp/") || path_str.contains("/temp/"),
            is_system: path_str.starts_with("/sys/") || path_str.starts_with("/proc/"),
            contains_spaces: path_str.contains(' '),
        }
    }

    fn is_project_root(&self, path: &Path) -> bool {
        // 检查常见的项目根目录标识文件
        let indicators = [
            "package.json",
            "Cargo.toml",
            "pom.xml",
            "build.gradle",
            "requirements.txt",
            "setup.py",
            "go.mod",
            "composer.json",
            ".git",
            "Dockerfile",
            "docker-compose.yml",
        ];

        indicators
            .iter()
            .any(|&indicator| path.join(indicator).exists())
    }

    async fn calculate_disk_usage(&self, _path: &Path) -> Result<DiskUsage, ContextError> {
        // 简化的磁盘使用情况计算
        // 在实际实现中，可以使用系统调用获取真实的磁盘信息
        Ok(DiskUsage {
            total_space: 1000000000, // 1GB
            free_space: 500000000,   // 500MB
            used_space: 500000000,   // 500MB
            usage_percentage: 50.0,
        })
    }

    async fn analyze_intent_context(
        &self,
        entities: &[Entity],
        directory_context: &DirectoryContext,
    ) -> Result<IntentContext, ContextError> {
        let mut likely_next_actions = Vec::new();
        let mut context_hints = Vec::new();
        let mut related_files = Vec::new();

        // 基于当前目录内容推断可能的下一步操作
        if directory_context.is_git_repo {
            likely_next_actions.extend([
                "git status".to_string(),
                "git add .".to_string(),
                "git commit".to_string(),
            ]);
            context_hints.push("这是一个Git仓库".to_string());
        }

        // 基于特殊文件推断操作
        for special_file in &directory_context.special_files {
            match special_file.file_type {
                SpecialFileType::PackageJson => {
                    likely_next_actions.extend([
                        "npm install".to_string(),
                        "npm start".to_string(),
                        "npm run build".to_string(),
                    ]);
                    context_hints.push("Node.js项目环境".to_string());
                }
                SpecialFileType::Dockerfile => {
                    likely_next_actions
                        .extend(["docker build .".to_string(), "docker run".to_string()]);
                    context_hints.push("Docker项目环境".to_string());
                }
                _ => {}
            }
        }

        // 基于实体内容添加相关文件
        for entity in entities {
            if let EntityType::FilePath = entity.entity_type {
                related_files.push(entity.value.clone());
            }
        }

        Ok(IntentContext {
            likely_next_actions,
            context_hints,
            related_files,
        })
    }
}

impl HistoryAnalyzer {
    fn new() -> Self {
        Self {
            pattern_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    fn analyze_patterns(&self, history: &[String]) -> Result<HistoryPatterns, ContextError> {
        let mut command_freq: HashMap<String, u32> = HashMap::new();

        // 分析命令频率
        for command in history {
            *command_freq.entry(command.clone()).or_insert(0) += 1;
        }

        let frequent_commands = command_freq
            .into_iter()
            .map(|(cmd, count)| CommandFrequency {
                command: cmd,
                count,
                last_used: Utc::now(), // 简化处理
                success_rate: 0.95,    // 简化处理
            })
            .collect();

        Ok(HistoryPatterns {
            frequent_commands,
            recent_patterns: Vec::new(),
            user_preferences: UserCommandPreferences {
                preferred_flags: HashMap::new(),
                command_aliases: HashMap::new(),
                working_patterns: Vec::new(),
            },
            error_patterns: Vec::new(),
        })
    }
}

impl EnvironmentParser {
    fn new() -> Self {
        Self {
            shell_detectors: HashMap::new(),
        }
    }

    fn parse(
        &self,
        environment: &HashMap<String, String>,
    ) -> Result<EnvironmentContext, ContextError> {
        let shell_type = environment
            .get("SHELL")
            .unwrap_or(&"bash".to_string())
            .clone();

        let path_dirs: Vec<String> = environment
            .get("PATH")
            .map(|path| path.split(':').map(|s| s.to_string()).collect())
            .unwrap_or_default();

        let mut developer_tools = Vec::new();

        // 检查常见的开发工具
        if path_dirs.iter().any(|dir| dir.contains("node")) {
            developer_tools.push(DeveloperTool {
                name: "Node.js".to_string(),
                version: None,
                path: "node".to_string(),
                is_available: true,
            });
        }

        Ok(EnvironmentContext {
            shell_type,
            shell_version: None,
            path_dirs,
            important_vars: environment.clone(),
            developer_tools,
        })
    }
}

impl ProjectDetector {
    fn new() -> Self {
        Self {
            detectors: Vec::new(),
        }
    }

    async fn detect_project_type(&self, path: &str) -> Result<ProjectContext, ContextError> {
        let path_obj = Path::new(path);

        // 检查 Node.js 项目
        if path_obj.join("package.json").exists() {
            return Ok(ProjectContext {
                project_type: ProjectType::NodeJs,
                language: Some("JavaScript".to_string()),
                framework: None,
                build_system: Some("npm".to_string()),
                package_manager: Some("npm".to_string()),
                dependencies: Vec::new(),
            });
        }

        // 检查 Rust 项目
        if path_obj.join("Cargo.toml").exists() {
            return Ok(ProjectContext {
                project_type: ProjectType::Rust,
                language: Some("Rust".to_string()),
                framework: None,
                build_system: Some("cargo".to_string()),
                package_manager: Some("cargo".to_string()),
                dependencies: Vec::new(),
            });
        }

        // 检查 Python 项目
        if path_obj.join("requirements.txt").exists() || path_obj.join("setup.py").exists() {
            return Ok(ProjectContext {
                project_type: ProjectType::Python,
                language: Some("Python".to_string()),
                framework: None,
                build_system: Some("pip".to_string()),
                package_manager: Some("pip".to_string()),
                dependencies: Vec::new(),
            });
        }

        // 默认为未知项目
        Ok(ProjectContext {
            project_type: ProjectType::Unknown,
            language: None,
            framework: None,
            build_system: None,
            package_manager: None,
            dependencies: Vec::new(),
        })
    }
}

impl Default for TerminalState {
    fn default() -> Self {
        Self {
            current_directory: "/".to_string(),
            command_history: Vec::new(),
            environment: HashMap::new(),
            shell_type: "bash".to_string(),
            user: "user".to_string(),
            hostname: "localhost".to_string(),
            platform: std::env::consts::OS.to_string(),
        }
    }
}

impl Default for ContextAnalyzer {
    fn default() -> Self {
        Self {
            terminal_state: Arc::new(RwLock::new(TerminalState::default())),
            history_analyzer: HistoryAnalyzer::new(),
            environment_parser: EnvironmentParser::new(),
            project_detector: ProjectDetector::new(),
        }
    }
}
