use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tauri::{command, State};
use tokio::sync::RwLock;

use crate::ai::chat_manager::{ChatContext, ChatManager};
use crate::ai::manager::AIServiceManager;
use crate::ai::types::AIError;

// 全局聊天管理器状态
pub type ChatManagerState = Arc<RwLock<Option<ChatManager>>>;

#[derive(Debug, Serialize, Deserialize)]
pub struct ChatResponse {
    pub success: bool,
    pub response: String,
    pub session_id: String,
    pub error: Option<String>,
}

#[command]
pub async fn start_chat_session(
    chat_state: State<'_, ChatManagerState>,
    ai_state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<String, String> {
    log::info!("Starting new chat session");

    // 获取AI服务管理器
    let ai_manager = ai_state.read().await;
    let provider = ai_manager
        .get_current_provider()
        .map_err(|e| format!("获取AI提供者失败: {}", e))?;

    // 创建新的聊天管理器
    let mut chat_manager = ChatManager::new();
    let session_id = chat_manager
        .start_session()
        .await
        .map_err(|e| format!("启动聊天会话失败: {}", e))?;

    // 保存到状态中
    let mut chat_state_guard = chat_state.write().await;
    *chat_state_guard = Some(chat_manager);

    log::info!("Chat session started successfully: {}", session_id);
    Ok(session_id)
}

#[command]
pub async fn send_chat_message(
    message: String,
    context: ChatContext,
    chat_state: State<'_, ChatManagerState>,
) -> Result<ChatResponse, String> {
    log::debug!("Sending chat message: {}", message);

    let mut chat_state_guard = chat_state.write().await;
    let chat_manager = chat_state_guard
        .as_mut()
        .ok_or("聊天会话未初始化，请先调用 start_chat_session")?;

    match chat_manager.send_message(&message, &context).await {
        Ok(response) => {
            log::debug!("Chat response generated successfully");
            Ok(ChatResponse {
                success: true,
                response,
                session_id: context.session_id,
                error: None,
            })
        }
        Err(e) => {
            log::error!("Chat message failed: {}", e);
            Ok(ChatResponse {
                success: false,
                response: String::new(),
                session_id: context.session_id,
                error: Some(e.to_string()),
            })
        }
    }
}

#[command]
pub async fn end_chat_session(chat_state: State<'_, ChatManagerState>) -> Result<(), String> {
    log::info!("Ending chat session");

    let mut chat_state_guard = chat_state.write().await;
    if let Some(chat_manager) = chat_state_guard.as_mut() {
        chat_manager
            .end_session()
            .await
            .map_err(|e| format!("结束聊天会话失败: {}", e))?;
    }

    *chat_state_guard = None;
    log::info!("Chat session ended successfully");
    Ok(())
}

#[command]
pub async fn ai_chat_with_context(
    message: String,
    context: serde_json::Value,
    chat_state: State<'_, ChatManagerState>,
    ai_state: State<'_, Arc<RwLock<AIServiceManager>>>,
) -> Result<String, String> {
    log::debug!("AI chat with context: {}", message);

    // 解析上下文
    let chat_context: ChatContext =
        serde_json::from_value(context).unwrap_or_else(|_| ChatContext::default());

    // 检查是否有活跃的聊天会话
    let mut chat_state_guard = chat_state.write().await;

    if chat_state_guard.is_none() {
        // 如果没有活跃会话，创建一个临时的
        let ai_manager = ai_state.read().await;
        let provider = ai_manager
            .get_current_provider()
            .map_err(|e| format!("获取AI提供者失败: {}", e))?;

        let mut chat_manager = ChatManager::new();
        chat_manager
            .start_session()
            .await
            .map_err(|e| format!("启动临时聊天会话失败: {}", e))?;

        *chat_state_guard = Some(chat_manager);
    }

    // 发送消息
    let chat_manager = chat_state_guard.as_mut().ok_or("聊天管理器不可用")?;

    let response = chat_manager
        .send_message(&message, &chat_context)
        .await
        .map_err(|e| format!("AI聊天失败: {}", e))?;

    log::debug!("AI chat response generated");
    Ok(response)
}

#[command]
pub async fn get_chat_history(chat_state: State<'_, ChatManagerState>) -> Result<String, String> {
    log::debug!("Getting chat history");

    let chat_state_guard = chat_state.read().await;
    let chat_manager = chat_state_guard.as_ref().ok_or("聊天会话未初始化")?;

    let history = chat_manager.get_conversation_history();
    Ok(history.export_history())
}

#[command]
pub async fn clear_chat_history(chat_state: State<'_, ChatManagerState>) -> Result<(), String> {
    log::info!("Clearing chat history");

    let mut chat_state_guard = chat_state.write().await;
    if let Some(chat_manager) = chat_state_guard.as_mut() {
        // 重新启动会话以清空历史
        chat_manager
            .start_session()
            .await
            .map_err(|e| format!("清空聊天历史失败: {}", e))?;
    }

    log::info!("Chat history cleared successfully");
    Ok(())
}
