use crate::ai::context_analyzer::EnrichedContext;
use crate::ai::types::*;
use std::collections::HashMap;

/// 智能建议引擎
pub struct SuggestionEngine {
    command_database: CommandDatabase,
    optimization_rules: OptimizationRules,
    learning_resources: LearningResourceDatabase,
    user_profiler: UserProfiler,
    safety_checker: SafetyChecker,
}

/// 命令数据库
pub struct CommandDatabase {
    commands: HashMap<String, CommandInfo>,
    categories: HashMap<CommandCategory, Vec<String>>,
    relationships: HashMap<String, Vec<String>>,
}

/// 命令信息
#[derive(Debug, Clone)]
pub struct CommandInfo {
    pub name: String,
    pub description: String,
    pub category: CommandCategory,
    pub common_flags: Vec<String>,
    pub examples: Vec<String>,
    pub alternatives: Vec<String>,
    pub complexity: u8, // 1-10
    pub risk_level: RiskLevel,
}

/// 优化规则
pub struct OptimizationRules {
    rules: Vec<OptimizationRule>,
    performance_hints: HashMap<String, Vec<PerformanceHint>>,
}

/// 优化规则
#[derive(Debug, Clone)]
pub struct OptimizationRule {
    pub pattern: String,
    pub replacement: String,
    pub reason: String,
    pub priority: u8,
    pub conditions: Vec<String>,
}

/// 性能提示
#[derive(Debug, Clone)]
pub struct PerformanceHint {
    pub hint_type: PerformanceHintType,
    pub description: String,
    pub example: String,
}

/// 性能提示类型
#[derive(Debug, Clone)]
pub enum PerformanceHintType {
    Parallel,
    Pipe,
    Flags,
    Alternative,
    Memory,
    Disk,
}

/// 学习资源数据库
pub struct LearningResourceDatabase {
    resources: HashMap<String, Vec<LearningResource>>,
    tutorials: HashMap<CommandCategory, Vec<Tutorial>>,
}

/// 学习资源
#[derive(Debug, Clone)]
pub struct LearningResource {
    pub title: String,
    pub url: String,
    pub resource_type: ResourceType,
    pub difficulty: Difficulty,
    pub estimated_time: String,
}

/// 资源类型
#[derive(Debug, Clone)]
pub enum ResourceType {
    Documentation,
    Tutorial,
    Video,
    Blog,
    CheatSheet,
    Interactive,
}

/// 难度等级
#[derive(Debug, Clone)]
pub enum Difficulty {
    Beginner,
    Intermediate,
    Advanced,
    Expert,
}

/// 教程
#[derive(Debug, Clone)]
pub struct Tutorial {
    pub name: String,
    pub steps: Vec<TutorialStep>,
    pub prerequisites: Vec<String>,
}

/// 教程步骤
#[derive(Debug, Clone)]
pub struct TutorialStep {
    pub description: String,
    pub command: String,
    pub explanation: String,
}

/// 用户画像分析器
pub struct UserProfiler {
    user_patterns: HashMap<String, UserPattern>,
    skill_levels: HashMap<CommandCategory, SkillLevel>,
}

/// 用户模式
#[derive(Debug, Clone)]
pub struct UserPattern {
    pub pattern_type: PatternType,
    pub frequency: u32,
    pub last_seen: chrono::DateTime<chrono::Utc>,
}

/// 模式类型
#[derive(Debug, Clone)]
pub enum PatternType {
    CommandSequence,
    WorkflowPattern,
    ErrorPattern,
    PreferencePattern,
}

/// 技能等级
#[derive(Debug, Clone)]
pub enum SkillLevel {
    Novice,
    Beginner,
    Intermediate,
    Advanced,
    Expert,
}

/// 安全检查器
pub struct SafetyChecker {
    dangerous_patterns: Vec<DangerousPattern>,
    security_rules: Vec<SecurityRule>,
}

/// 危险模式
#[derive(Debug, Clone)]
pub struct DangerousPattern {
    pub pattern: String,
    pub risk_level: RiskLevel,
    pub description: String,
    pub mitigation: String,
}

/// 安全规则
#[derive(Debug, Clone)]
pub struct SecurityRule {
    pub rule_type: SecurityRuleType,
    pub condition: String,
    pub action: SecurityAction,
}

/// 安全规则类型
#[derive(Debug, Clone)]
pub enum SecurityRuleType {
    PermissionCheck,
    PathValidation,
    ContentScan,
    NetworkAccess,
}

/// 安全动作
#[derive(Debug, Clone)]
pub enum SecurityAction {
    Warn,
    Block,
    RequireConfirmation,
    SuggestAlternative,
}

impl SuggestionEngine {
    pub fn new() -> Self {
        Self {
            command_database: CommandDatabase::new(),
            optimization_rules: OptimizationRules::new(),
            learning_resources: LearningResourceDatabase::new(),
            user_profiler: UserProfiler::new(),
            safety_checker: SafetyChecker::new(),
        }
    }

    /// 生成建议
    pub async fn generate_suggestions(
        &self,
        command: &GeneratedCommand,
        context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 1. 优化建议
        suggestions.extend(
            self.generate_optimization_suggestions(command, context)
                .await?,
        );

        // 2. 安全建议
        suggestions.extend(self.generate_safety_suggestions(command).await?);

        // 3. 相关命令建议
        suggestions.extend(self.generate_related_commands(command, context).await?);

        // 4. 学习建议
        suggestions.extend(self.generate_learning_suggestions(command, context).await?);

        // 5. 个性化建议
        suggestions.extend(
            self.generate_personalized_suggestions(command, context)
                .await?,
        );

        // 排序和过滤
        self.rank_and_filter_suggestions(suggestions, context).await
    }

    async fn generate_optimization_suggestions(
        &self,
        command: &GeneratedCommand,
        context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 检查是否有更高效的替代方案
        if let Some(alternatives) = self.optimization_rules.find_alternatives(&command.command) {
            for alt in alternatives {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::Optimization,
                    content: format!("考虑使用 '{}' 代替，因为{}", alt.replacement, alt.reason),
                    priority: alt.priority,
                    category: SuggestionCategory::Performance,
                });
            }
        }

        // 检查参数优化
        if let Some(param_opts) = self
            .analyze_parameter_optimizations(command, context)
            .await?
        {
            suggestions.extend(param_opts);
        }

        // 检查组合命令优化
        if let Some(pipeline_opts) = self
            .suggest_pipeline_optimizations(command, context)
            .await?
        {
            suggestions.extend(pipeline_opts);
        }

        // 检查性能提示
        suggestions.extend(self.generate_performance_hints(command)?);

        Ok(suggestions)
    }

    async fn generate_safety_suggestions(
        &self,
        command: &GeneratedCommand,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 检查危险操作
        if self
            .safety_checker
            .is_potentially_dangerous(&command.command)
        {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::SafetyTip,
                content: "此命令可能有风险，建议先在测试环境中验证".to_string(),
                priority: 9,
                category: SuggestionCategory::Safety,
            });

            // 提供更安全的替代方案
            if let Some(safer_alt) = self.safety_checker.find_safer_alternative(&command.command) {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::Alternative,
                    content: format!("更安全的做法：{}", safer_alt),
                    priority: 8,
                    category: SuggestionCategory::Safety,
                });
            }
        }

        // 提供备份建议
        if self.safety_checker.should_suggest_backup(&command.command) {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::SafetyTip,
                content: "建议先备份相关文件：cp file file.bak".to_string(),
                priority: 7,
                category: SuggestionCategory::Safety,
            });
        }

        // 权限检查建议
        if self
            .safety_checker
            .requires_elevated_permissions(&command.command)
        {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::SafetyTip,
                content: "此命令需要管理员权限，请确保您了解其影响".to_string(),
                priority: 8,
                category: SuggestionCategory::Safety,
            });
        }

        Ok(suggestions)
    }

    async fn generate_related_commands(
        &self,
        command: &GeneratedCommand,
        context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 基于命令类别查找相关命令
        if let Some(cmd_info) = self.command_database.get_command_info(&command.command) {
            if let Some(related) = self
                .command_database
                .get_related_commands(&cmd_info.category)
            {
                for related_cmd in related.iter().take(3) {
                    suggestions.push(CommandSuggestion {
                        suggestion_type: SuggestionType::RelatedCommand,
                        content: format!("相关命令：{}", related_cmd),
                        priority: 5,
                        category: SuggestionCategory::Alternative,
                    });
                }
            }
        }

        // 基于上下文推荐
        if context.directory.is_git_repo {
            if !command.command.starts_with("git") {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::RelatedCommand,
                    content: "在Git仓库中，您可能还需要：git status".to_string(),
                    priority: 6,
                    category: SuggestionCategory::Alternative,
                });
            }
        }

        // 基于项目类型推荐
        match &context.project.project_type {
            crate::ai::context_analyzer::ProjectType::NodeJs => {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::RelatedCommand,
                    content: "Node.js项目常用：npm install, npm start, npm test".to_string(),
                    priority: 4,
                    category: SuggestionCategory::Alternative,
                });
            }
            crate::ai::context_analyzer::ProjectType::Rust => {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::RelatedCommand,
                    content: "Rust项目常用：cargo build, cargo run, cargo test".to_string(),
                    priority: 4,
                    category: SuggestionCategory::Alternative,
                });
            }
            _ => {}
        }

        Ok(suggestions)
    }

    async fn generate_learning_suggestions(
        &self,
        command: &GeneratedCommand,
        _context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 基于命令复杂度提供学习建议
        if let Some(cmd_info) = self.command_database.get_command_info(&command.command) {
            if cmd_info.complexity > 6 {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::LearningTip,
                    content: format!("这是一个复杂命令，建议查看文档：man {}", cmd_info.name),
                    priority: 3,
                    category: SuggestionCategory::Learning,
                });
            }

            // 提供示例
            if !cmd_info.examples.is_empty() {
                let example = &cmd_info.examples[0];
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::LearningTip,
                    content: format!("示例用法：{}", example),
                    priority: 4,
                    category: SuggestionCategory::Learning,
                });
            }
        }

        // 提供学习资源
        if let Some(resources) = self.learning_resources.get_resources(&command.command) {
            for resource in resources.iter().take(2) {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::LearningTip,
                    content: format!("学习资源：{} ({})", resource.title, resource.url),
                    priority: 2,
                    category: SuggestionCategory::Learning,
                });
            }
        }

        Ok(suggestions)
    }

    async fn generate_personalized_suggestions(
        &self,
        command: &GeneratedCommand,
        context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 基于用户历史偏好
        if let Some(_preferences) = self.user_profiler.analyze_preferences(&context.history) {
            // TODO: 实现基于用户偏好的建议
        }

        // 基于工作模式
        if let Some(workflow) = self.user_profiler.detect_workflow_pattern(&context.history) {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::Optimization,
                content: format!(
                    "根据您的工作模式，接下来可能需要：{}",
                    workflow.next_likely_command
                ),
                priority: 5,
                category: SuggestionCategory::BestPractice,
            });
        }

        Ok(suggestions)
    }

    async fn analyze_parameter_optimizations(
        &self,
        command: &GeneratedCommand,
        _context: &EnrichedContext,
    ) -> Result<Option<Vec<CommandSuggestion>>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 检查是否可以添加有用的参数
        if command.command.starts_with("ls") && !command.command.contains("-") {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::Optimization,
                content: "添加 -la 参数显示详细信息和隐藏文件".to_string(),
                priority: 4,
                category: SuggestionCategory::Performance,
            });
        }

        if command.command.starts_with("grep") && !command.command.contains("-r") {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::Optimization,
                content: "添加 -r 参数递归搜索目录".to_string(),
                priority: 5,
                category: SuggestionCategory::Performance,
            });
        }

        Ok(if suggestions.is_empty() {
            None
        } else {
            Some(suggestions)
        })
    }

    async fn suggest_pipeline_optimizations(
        &self,
        command: &GeneratedCommand,
        _context: &EnrichedContext,
    ) -> Result<Option<Vec<CommandSuggestion>>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 建议管道优化
        if command.command.contains("cat") && command.command.contains("grep") {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::Optimization,
                content: "可以直接使用 grep 而不需要 cat".to_string(),
                priority: 6,
                category: SuggestionCategory::Performance,
            });
        }

        Ok(if suggestions.is_empty() {
            None
        } else {
            Some(suggestions)
        })
    }

    fn generate_performance_hints(
        &self,
        command: &GeneratedCommand,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        if let Some(hints) = self
            .optimization_rules
            .get_performance_hints(&command.command)
        {
            for hint in hints {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::Optimization,
                    content: hint.description.clone(),
                    priority: 3,
                    category: SuggestionCategory::Performance,
                });
            }
        }

        Ok(suggestions)
    }

    async fn rank_and_filter_suggestions(
        &self,
        mut suggestions: Vec<CommandSuggestion>,
        _context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        // 按优先级排序
        suggestions.sort_by(|a, b| b.priority.cmp(&a.priority));

        // 限制数量并去重
        suggestions.dedup_by(|a, b| a.content == b.content);
        suggestions.truncate(10);

        Ok(suggestions)
    }
}

// 实现各个组件
impl CommandDatabase {
    fn new() -> Self {
        let mut commands = HashMap::new();
        let mut categories = HashMap::new();
        let mut relationships = HashMap::new();

        // 添加一些基础命令信息
        Self::populate_basic_commands(&mut commands, &mut categories, &mut relationships);

        Self {
            commands,
            categories,
            relationships,
        }
    }

    fn populate_basic_commands(
        commands: &mut HashMap<String, CommandInfo>,
        categories: &mut HashMap<CommandCategory, Vec<String>>,
        relationships: &mut HashMap<String, Vec<String>>,
    ) {
        // 文件系统命令
        let file_commands = vec!["ls", "cd", "cp", "mv", "rm", "mkdir", "rmdir"];
        categories.insert(
            CommandCategory::FileSystem,
            file_commands.iter().map(|s| s.to_string()).collect(),
        );

        for &cmd in &file_commands {
            commands.insert(
                cmd.to_string(),
                CommandInfo {
                    name: cmd.to_string(),
                    description: format!("{} 文件系统操作", cmd),
                    category: CommandCategory::FileSystem,
                    common_flags: vec!["-l".to_string(), "-a".to_string()],
                    examples: vec![format!("{} example", cmd)],
                    alternatives: vec![],
                    complexity: 3,
                    risk_level: if cmd == "rm" {
                        RiskLevel::High
                    } else {
                        RiskLevel::Low
                    },
                },
            );
        }

        // 建立关系
        relationships.insert("ls".to_string(), vec!["cd".to_string(), "cat".to_string()]);
        relationships.insert("cd".to_string(), vec!["ls".to_string(), "pwd".to_string()]);
    }

    fn get_command_info(&self, command: &str) -> Option<&CommandInfo> {
        let cmd_name = command.split_whitespace().next()?;
        self.commands.get(cmd_name)
    }

    fn get_related_commands(&self, category: &CommandCategory) -> Option<&Vec<String>> {
        self.categories.get(category)
    }
}

impl OptimizationRules {
    fn new() -> Self {
        let mut rules = Vec::new();
        let mut performance_hints = HashMap::new();

        // 添加基础优化规则
        rules.push(OptimizationRule {
            pattern: "cat .* | grep".to_string(),
            replacement: "grep".to_string(),
            reason: "直接使用grep更高效".to_string(),
            priority: 7,
            conditions: vec![],
        });

        // 添加性能提示
        performance_hints.insert(
            "find".to_string(),
            vec![PerformanceHint {
                hint_type: PerformanceHintType::Flags,
                description: "使用 -name 参数可以显著提高搜索速度".to_string(),
                example: "find . -name '*.txt'".to_string(),
            }],
        );

        Self {
            rules,
            performance_hints,
        }
    }

    fn find_alternatives(&self, command: &str) -> Option<Vec<&OptimizationRule>> {
        Some(
            self.rules
                .iter()
                .filter(|rule| command.contains(&rule.pattern))
                .collect(),
        )
    }

    fn get_performance_hints(&self, command: &str) -> Option<&Vec<PerformanceHint>> {
        let cmd_name = command.split_whitespace().next()?;
        self.performance_hints.get(cmd_name)
    }
}

impl LearningResourceDatabase {
    fn new() -> Self {
        let mut resources = HashMap::new();
        let tutorials = HashMap::new();

        // 添加一些基础学习资源
        resources.insert(
            "git".to_string(),
            vec![LearningResource {
                title: "Git官方文档".to_string(),
                url: "https://git-scm.com/doc".to_string(),
                resource_type: ResourceType::Documentation,
                difficulty: Difficulty::Intermediate,
                estimated_time: "2小时".to_string(),
            }],
        );

        Self {
            resources,
            tutorials,
        }
    }

    fn get_resources(&self, command: &str) -> Option<&Vec<LearningResource>> {
        let cmd_name = command.split_whitespace().next()?;
        self.resources.get(cmd_name)
    }
}

impl UserProfiler {
    fn new() -> Self {
        Self {
            user_patterns: HashMap::new(),
            skill_levels: HashMap::new(),
        }
    }

    fn analyze_preferences(
        &self,
        history: &crate::ai::context_analyzer::HistoryPatterns,
    ) -> Option<UserPreferences> {
        let mut preferred_flags = HashMap::new();

        // 分析用户偏好的参数
        for freq_cmd in &history.frequent_commands {
            if freq_cmd.command.contains(" -") {
                // 简化的参数分析
                if freq_cmd.command.contains("-l") {
                    preferred_flags.insert("-l".to_string(), 0.8);
                }
                if freq_cmd.command.contains("-a") {
                    preferred_flags.insert("-a".to_string(), 0.7);
                }
            }
        }

        Some(UserPreferences {
            preferred_commands: HashMap::new(),
            risk_tolerance: RiskLevel::Medium,
            confirmation_required: true,
            language: "zh".to_string(),
        })
    }

    fn detect_workflow_pattern(
        &self,
        _history: &crate::ai::context_analyzer::HistoryPatterns,
    ) -> Option<WorkflowPattern> {
        // 简化的工作流模式检测
        Some(WorkflowPattern {
            pattern_name: "开发工作流".to_string(),
            next_likely_command: "git status".to_string(),
            confidence: 0.6,
        })
    }
}

#[derive(Debug, Clone)]
pub struct WorkflowPattern {
    pub pattern_name: String,
    pub next_likely_command: String,
    pub confidence: f32,
}

impl SafetyChecker {
    fn new() -> Self {
        let dangerous_patterns = vec![
            DangerousPattern {
                pattern: "rm -rf".to_string(),
                risk_level: RiskLevel::Critical,
                description: "强制递归删除，可能导致数据丢失".to_string(),
                mitigation: "使用 rm -i 进行交互式删除".to_string(),
            },
            DangerousPattern {
                pattern: "sudo".to_string(),
                risk_level: RiskLevel::Medium,
                description: "需要管理员权限的操作".to_string(),
                mitigation: "确保理解命令的影响".to_string(),
            },
        ];

        Self {
            dangerous_patterns,
            security_rules: vec![],
        }
    }

    fn is_potentially_dangerous(&self, command: &str) -> bool {
        self.dangerous_patterns
            .iter()
            .any(|pattern| command.contains(&pattern.pattern))
    }

    fn find_safer_alternative(&self, command: &str) -> Option<String> {
        for pattern in &self.dangerous_patterns {
            if command.contains(&pattern.pattern) {
                return Some(pattern.mitigation.clone());
            }
        }
        None
    }

    fn should_suggest_backup(&self, command: &str) -> bool {
        command.contains("rm") || command.contains("mv") || command.contains("cp")
    }

    fn requires_elevated_permissions(&self, command: &str) -> bool {
        command.starts_with("sudo") || command.contains("chmod 777")
    }
}

impl Default for SuggestionEngine {
    fn default() -> Self {
        Self::new()
    }
}
