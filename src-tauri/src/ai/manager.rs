use chrono::Utc;
use serde_json;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::ai::provider::*;
use crate::ai::types::*;

/// AI 缓存项
#[derive(Debu<PERSON>, <PERSON>lone)]
struct CacheItem {
    response: AIResponse,
    created_at: chrono::DateTime<chrono::Utc>,
    ttl_hours: u32,
}

impl CacheItem {
    fn new(response: AIResponse, ttl_hours: u32) -> Self {
        Self {
            response,
            created_at: Utc::now(),
            ttl_hours,
        }
    }

    fn is_expired(&self) -> bool {
        let now = Utc::now();
        let duration = now.signed_duration_since(self.created_at);
        duration.num_hours() > self.ttl_hours as i64
    }
}

/// AI 缓存
pub struct AICache {
    items: HashMap<String, CacheItem>,
    max_size: usize,
}

impl AICache {
    pub fn new(max_size: usize) -> Self {
        Self {
            items: HashMap::new(),
            max_size,
        }
    }

    pub fn get(&mut self, key: &str) -> Option<AIResponse> {
        if let Some(item) = self.items.get(key) {
            if !item.is_expired() {
                return Some(item.response.clone());
            } else {
                self.items.remove(key);
            }
        }
        None
    }

    pub fn put(&mut self, key: String, response: AIResponse, ttl_hours: u32) {
        // 如果缓存已满，删除最旧的项
        if self.items.len() >= self.max_size {
            if let Some(oldest_key) = self.find_oldest_key() {
                self.items.remove(&oldest_key);
            }
        }

        let item = CacheItem::new(response, ttl_hours);
        self.items.insert(key, item);
    }

    fn find_oldest_key(&self) -> Option<String> {
        self.items
            .iter()
            .min_by_key(|(_, item)| item.created_at)
            .map(|(key, _)| key.clone())
    }

    pub fn clear(&mut self) {
        self.items.clear();
    }

    pub fn size(&self) -> usize {
        self.items.len()
    }
}

/// AI 服务管理器
pub struct AIServiceManager {
    providers: HashMap<String, Box<dyn ModelProvider>>,
    current_provider: Option<String>,
    config: AIConfig,
    cache: Arc<RwLock<AICache>>,
}

impl AIServiceManager {
    pub fn new(config: AIConfig) -> Self {
        let cache_size = 1000; // 默认缓存大小
        Self {
            providers: HashMap::new(),
            current_provider: None,
            config,
            cache: Arc::new(RwLock::new(AICache::new(cache_size))),
        }
    }

    /// 初始化服务管理器
    pub async fn initialize(&mut self) -> Result<(), AIError> {
        log::info!("Initializing AI service manager");

        // 初始化配置的提供者
        for (name, provider_config) in &self.config.providers {
            log::info!("Initializing provider: {}", name);

            // 处理环境变量配置
            let mut config_with_env = provider_config.clone();
            self.load_env_config(&mut config_with_env);

            match self.create_provider(&config_with_env).await {
                Ok(mut provider) => {
                    if let Err(e) = provider.initialize(config_with_env.clone()).await {
                        log::warn!("Failed to initialize provider {}: {}", name, e);
                        continue;
                    }
                    self.providers.insert(name.clone(), provider);
                    log::info!("Provider {} initialized successfully", name);
                }
                Err(e) => {
                    log::warn!("Failed to create provider {}: {}", name, e);
                }
            }
        }

        // 设置默认提供者
        if self.providers.contains_key(&self.config.default_provider) {
            self.current_provider = Some(self.config.default_provider.clone());
            log::info!("Set default provider: {}", self.config.default_provider);
        } else {
            // 如果默认提供者不可用，选择第一个可用的提供者
            if let Some(provider_name) = self.providers.keys().next() {
                self.current_provider = Some(provider_name.clone());
                log::info!("Default provider not available, using: {}", provider_name);
            } else {
                return Err(AIError::ConfigError("No providers available".to_string()));
            }
        }

        log::info!(
            "AI service manager initialized with {} providers",
            self.providers.len()
        );
        Ok(())
    }

    /// 处理自然语言命令
    pub async fn process_natural_language(
        &self,
        input: &str,
        context: &AIContext,
    ) -> Result<AIResponse, AIError> {
        let start_time = std::time::Instant::now();

        if input.trim().is_empty() {
            return Err(AIError::InvalidInput("Input cannot be empty".to_string()));
        }

        let provider = self.get_current_provider()?;

        // 构建提示词
        let prompt = self.build_command_prompt(input, context);

        // 尝试从缓存获取
        if self.config.cache_enabled {
            let cache_key = self.generate_cache_key(&prompt);
            let mut cache = self.cache.write().await;
            if let Some(mut cached_response) = cache.get(&cache_key) {
                cached_response.metadata.cached = true;
                log::debug!("Cache hit for input: {}", input);
                return Ok(cached_response);
            }
        }

        // 调用模型生成
        let generation_options = self.get_generation_options();
        let response = provider.generate(&prompt, &generation_options).await?;

        // 解析响应
        let mut ai_response =
            self.parse_command_response(&response, input, provider.get_model_info())?;

        // 设置元数据
        ai_response.metadata.processing_time_ms = start_time.elapsed().as_millis() as u64;
        ai_response.metadata.cached = false;

        // 缓存结果
        if self.config.cache_enabled {
            let cache_key = self.generate_cache_key(&prompt);
            let mut cache = self.cache.write().await;
            cache.put(cache_key, ai_response.clone(), 24); // 24小时TTL
        }

        log::info!(
            "Processed natural language command: {} -> {}",
            input,
            ai_response.content
        );
        Ok(ai_response)
    }

    /// 解释命令
    pub async fn explain_command(&self, command: &str) -> Result<String, AIError> {
        if command.trim().is_empty() {
            return Err(AIError::InvalidInput("Command cannot be empty".to_string()));
        }

        let provider = self.get_current_provider()?;

        let prompt = self.build_explanation_prompt(command);
        let generation_options = self.get_generation_options();

        let response = provider.generate(&prompt, &generation_options).await?;

        // 对于解释功能，直接返回生成的文本
        Ok(response.trim().to_string())
    }

    /// AI 聊天
    pub async fn chat(&self, message: &str, context: &ChatContext) -> Result<String, AIError> {
        if message.trim().is_empty() {
            return Err(AIError::InvalidInput("Message cannot be empty".to_string()));
        }

        let provider = self.get_current_provider()?;

        let prompt = self.build_chat_prompt(message, context);
        let generation_options = self.get_generation_options();

        let response = provider.generate(&prompt, &generation_options).await?;

        Ok(response.trim().to_string())
    }

    /// 预测意图
    pub async fn predict_intent(&self, input: &str) -> Result<Intent, AIError> {
        if input.trim().is_empty() {
            return Err(AIError::InvalidInput("Input cannot be empty".to_string()));
        }

        // 简单的意图预测实现
        let input_lower = input.to_lowercase();

        let intent_type = if input_lower.contains("文件")
            || input_lower.contains("目录")
            || input_lower.contains("file")
            || input_lower.contains("directory")
        {
            IntentType::FileOperation
        } else if input_lower.contains("进程")
            || input_lower.contains("process")
            || input_lower.contains("kill")
        {
            IntentType::ProcessControl
        } else if input_lower.contains("git") || input_lower.contains("仓库") {
            IntentType::GitOperation
        } else if input_lower.contains("网络")
            || input_lower.contains("ping")
            || input_lower.contains("curl")
        {
            IntentType::NetworkOperation
        } else if input_lower.contains("系统")
            || input_lower.contains("磁盘")
            || input_lower.contains("内存")
        {
            IntentType::SystemInfo
        } else {
            IntentType::Unknown
        };

        Ok(Intent {
            intent_type,
            confidence: 0.7,
            context_dependent: false,
        })
    }

    /// 切换提供者
    pub async fn switch_provider(&mut self, provider_name: &str) -> Result<(), AIError> {
        if !self.providers.contains_key(provider_name) {
            return Err(AIError::ProviderNotAvailable(provider_name.to_string()));
        }

        // 检查提供者健康状态
        if let Some(provider) = self.providers.get(provider_name) {
            provider.health_check().await?;
        }

        self.current_provider = Some(provider_name.to_string());
        log::info!("Switched to provider: {}", provider_name);
        Ok(())
    }

    /// 获取可用模型列表
    pub async fn get_available_models(&self) -> Result<Vec<ModelInfo>, AIError> {
        let mut models = Vec::new();

        for (provider_name, provider) in &self.providers {
            if provider.is_available() {
                let mut model_info = provider.get_model_info();
                model_info.provider = provider_name.clone();
                models.push(model_info);
            }
        }

        Ok(models)
    }

    /// 更新配置
    pub async fn update_config(&mut self, new_config: AIConfig) -> Result<(), AIError> {
        // 验证配置
        if new_config.providers.is_empty() {
            return Err(AIError::ConfigError(
                "At least one provider must be configured".to_string(),
            ));
        }

        if !new_config
            .providers
            .contains_key(&new_config.default_provider)
        {
            return Err(AIError::ConfigError(
                "Default provider must exist in providers list".to_string(),
            ));
        }

        self.config = new_config;

        // 重新初始化
        self.providers.clear();
        self.current_provider = None;

        self.initialize().await?;

        log::info!("AI configuration updated");
        Ok(())
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<(), AIError> {
        if self.providers.is_empty() {
            return Err(AIError::ServiceUnavailable);
        }

        let provider = self.get_current_provider()?;
        provider.health_check().await
    }

    /// 获取当前提供者
    pub fn get_current_provider(&self) -> Result<&Box<dyn ModelProvider>, AIError> {
        let provider_name = self
            .current_provider
            .as_ref()
            .ok_or_else(|| AIError::ProviderNotAvailable("No current provider set".to_string()))?;

        self.providers
            .get(provider_name)
            .ok_or_else(|| AIError::ProviderNotAvailable(provider_name.clone()))
    }

    /// 创建提供者实例
    async fn create_provider(
        &self,
        config: &ProviderConfig,
    ) -> Result<Box<dyn ModelProvider>, AIError> {
        match config.provider_type {
            ProviderType::Mock => Ok(Box::new(MockProvider::new())),
            ProviderType::Local => Ok(Box::new(LocalModelProvider::new())),
            ProviderType::OpenAI => Ok(Box::new(OpenAIProvider::new())),
            ProviderType::Claude => Ok(Box::new(ClaudeProvider::new())),
            ProviderType::Deepseek => Ok(Box::new(DeepseekProvider::new())),
            ProviderType::Gemini => Err(AIError::InitializationFailed(
                "Gemini provider not implemented yet".to_string(),
            )),
        }
    }

    /// 构建命令生成提示词
    fn build_command_prompt(&self, input: &str, context: &AIContext) -> String {
        format!(
            r#"你是一个专业的命令行助手。根据用户的自然语言描述，生成对应的命令行命令。

当前环境信息：
- 操作系统: {}
- Shell: {}
- 当前目录: {}
- 最近命令: {:?}

用户请求: {}

请生成准确的命令，并提供简要说明。如果是危险操作，请明确标注风险等级。

输出格式 (严格按照JSON格式):
```json
{{
    "command": "具体命令",
    "description": "命令说明",
    "risk_level": "low|medium|high",
    "requires_confirmation": true/false
}}
```"#,
            std::env::consts::OS,
            context.shell_type,
            context.current_directory,
            context
                .command_history
                .iter()
                .rev()
                .take(3)
                .collect::<Vec<_>>(),
            input
        )
    }

    /// 构建命令解释提示词
    fn build_explanation_prompt(&self, command: &str) -> String {
        format!(
            r#"请详细解释以下命令的作用、参数含义和潜在风险：

命令: {}

请提供清晰、详细的解释，包括：
1. 命令的主要功能
2. 各个参数的含义
3. 可能的风险或注意事项
4. 使用示例（如果适用）"#,
            command
        )
    }

    /// 构建聊天提示词
    fn build_chat_prompt(&self, message: &str, context: &ChatContext) -> String {
        let mut prompt =
            String::from("你是一个友好的AI助手，专门帮助用户解决命令行和系统管理相关的问题。\n\n");

        // 添加历史对话
        if !context.history.is_empty() {
            prompt.push_str("对话历史:\n");
            for msg in context.history.iter().rev().take(5).rev() {
                prompt.push_str(&format!("{}: {}\n", msg.role, msg.content));
            }
            prompt.push_str("\n");
        }

        prompt.push_str(&format!("用户: {}\n助手: ", message));
        prompt
    }

    /// 生成缓存键
    fn generate_cache_key(&self, prompt: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        prompt.hash(&mut hasher);
        format!("ai_cache_{}", hasher.finish())
    }

    /// 获取生成选项
    fn get_generation_options(&self) -> GenerationOptions {
        GenerationOptions {
            max_tokens: Some(1024),
            temperature: Some(0.7),
            top_p: Some(0.9),
            stop_sequences: vec!["```".to_string()],
            stream: false,
        }
    }

    /// 解析命令响应
    fn parse_command_response(
        &self,
        response: &str,
        _original_input: &str,
        model_info: ModelInfo,
    ) -> Result<AIResponse, AIError> {
        // 尝试解析JSON响应
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(response) {
            let command = parsed
                .get("command")
                .and_then(|v| v.as_str())
                .unwrap_or("echo 'Failed to parse command'")
                .to_string();

            let description = parsed
                .get("description")
                .and_then(|v| v.as_str())
                .unwrap_or("Generated command")
                .to_string();

            let risk_level = match parsed.get("risk_level").and_then(|v| v.as_str()) {
                Some("low") => RiskLevel::Low,
                Some("medium") => RiskLevel::Medium,
                Some("high") => RiskLevel::High,
                Some("critical") => RiskLevel::Critical,
                _ => RiskLevel::Medium,
            };

            let requires_confirmation = parsed
                .get("requires_confirmation")
                .and_then(|v| v.as_bool())
                .unwrap_or(true);

            let suggested_command = SuggestedCommand {
                command: command.clone(),
                description: description.clone(),
                risk_level: risk_level.clone(),
                estimated_duration: None,
            };

            Ok(AIResponse {
                response_type: ResponseType::Command,
                content: command,
                confidence: 0.8,
                suggested_commands: vec![suggested_command],
                requires_confirmation,
                metadata: ResponseMetadata {
                    provider: model_info.provider,
                    model: model_info.name,
                    timestamp: Utc::now(),
                    processing_time_ms: 0, // 将在上层设置
                    cached: false,
                },
            })
        } else {
            // 如果不能解析为JSON，返回原始响应
            Ok(AIResponse {
                response_type: ResponseType::Command,
                content: response.to_string(),
                confidence: 0.5,
                suggested_commands: vec![],
                requires_confirmation: true,
                metadata: ResponseMetadata {
                    provider: model_info.provider,
                    model: model_info.name,
                    timestamp: Utc::now(),
                    processing_time_ms: 0,
                    cached: false,
                },
            })
        }
    }

    /// 从环境变量加载配置
    fn load_env_config(&self, config: &mut ProviderConfig) {
        match config.provider_type {
            ProviderType::Deepseek => {
                // 如果没有设置 API key，尝试从环境变量获取
                if config.api_key.is_none() {
                    if let Ok(api_key) = std::env::var("DEEPSEEK_API_KEY") {
                        if !api_key.is_empty() {
                            config.api_key = Some(api_key);
                            log::info!("Loaded Deepseek API key from environment variable");
                        }
                    }
                }

                // 如果没有设置 endpoint，使用默认值
                if config.api_endpoint.is_none() {
                    config.api_endpoint = Some("https://api.deepseek.com".to_string());
                }
            }
            ProviderType::OpenAI => {
                if config.api_key.is_none() {
                    if let Ok(api_key) = std::env::var("OPENAI_API_KEY") {
                        if !api_key.is_empty() {
                            config.api_key = Some(api_key);
                            log::info!("Loaded OpenAI API key from environment variable");
                        }
                    }
                }
            }
            ProviderType::Claude => {
                if config.api_key.is_none() {
                    if let Ok(api_key) = std::env::var("ANTHROPIC_API_KEY") {
                        if !api_key.is_empty() {
                            config.api_key = Some(api_key);
                            log::info!("Loaded Claude API key from environment variable");
                        }
                    }
                }
            }
            _ => {
                // 其他提供者暂不处理环境变量
            }
        }
    }
}

impl Default for AIServiceManager {
    fn default() -> Self {
        Self::new(AIConfig::default())
    }
}
