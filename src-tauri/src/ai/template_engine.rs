use crate::ai::types::*;
use regex::Regex;
use std::collections::HashMap;

/// 命令模板引擎
pub struct TemplateEngine {
    templates: HashMap<String, CommandTemplate>,
    pattern_matcher: PatternMatcher,
    parameter_resolver: ParameterResolver,
}

/// 模板匹配结果
#[derive(Debug, Clone)]
pub struct TemplateMatch {
    pub template: CommandTemplate,
    pub score: f32,
    pub parameter_mappings: HashMap<String, String>,
}

/// 模式匹配器
pub struct PatternMatcher {
    compiled_patterns: HashMap<String, Vec<Regex>>,
}

/// 参数解析器
pub struct ParameterResolver {
    extractors: HashMap<ParameterType, Vec<Regex>>,
}

impl TemplateEngine {
    pub fn new() -> Self {
        let templates = Self::load_default_templates();
        let pattern_matcher = PatternMatcher::new(&templates);
        let parameter_resolver = ParameterResolver::new();

        Self {
            templates,
            pattern_matcher,
            parameter_resolver,
        }
    }

    /// 匹配模板
    pub fn match_templates(&self, input: &str, entities: &[Entity]) -> Vec<TemplateMatch> {
        let mut matches = Vec::new();

        for template in self.templates.values() {
            if let Some(score) = self.calculate_match_score(input, template, entities) {
                let parameter_mappings = self.map_parameters(template, entities, input);
                matches.push(TemplateMatch {
                    template: template.clone(),
                    score,
                    parameter_mappings,
                });
            }
        }

        matches.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        matches
    }

    /// 生成命令
    pub fn generate_command(&self, template_match: &TemplateMatch) -> Result<String, NLPError> {
        let mut command = template_match.template.command_template.clone();

        for (param_name, param_value) in &template_match.parameter_mappings {
            let placeholder = format!("{{{}}}", param_name);
            command = command.replace(&placeholder, param_value);
        }

        // 填充默认值
        for param in &template_match.template.parameters {
            let placeholder = format!("{{{}}}", param.name);
            if command.contains(&placeholder) {
                if let Some(default) = &param.default_value {
                    command = command.replace(&placeholder, default);
                } else if param.required {
                    return Err(NLPError::TemplateMatchingFailed(format!(
                        "Required parameter '{}' not found",
                        param.name
                    )));
                } else {
                    command = command.replace(&placeholder, "");
                }
            }
        }

        // 清理多余的空格
        command = command.split_whitespace().collect::<Vec<&str>>().join(" ");

        Ok(command)
    }

    fn calculate_match_score(
        &self,
        input: &str,
        template: &CommandTemplate,
        entities: &[Entity],
    ) -> Option<f32> {
        let input_lower = input.to_lowercase();
        let mut score = 0.0;
        let mut total_patterns = template.patterns.len() as f32;

        // 模式匹配得分
        for pattern in &template.patterns {
            if let Ok(regex) = Regex::new(&pattern.to_lowercase()) {
                if regex.is_match(&input_lower) {
                    score += 1.0;
                }
            } else if input_lower.contains(&pattern.to_lowercase()) {
                score += 0.8;
            }
        }

        // 实体匹配得分
        let required_params: Vec<_> = template.parameters.iter().filter(|p| p.required).collect();

        let mut entity_score = 0.0;
        for param in &required_params {
            if self.find_matching_entity(param, entities).is_some() {
                entity_score += 1.0;
            }
        }

        if !required_params.is_empty() {
            score += entity_score / required_params.len() as f32;
            total_patterns += 1.0;
        }

        if total_patterns == 0.0 {
            return None;
        }

        let final_score = score / total_patterns;
        if final_score > 0.3 {
            Some(final_score)
        } else {
            None
        }
    }

    fn map_parameters(
        &self,
        template: &CommandTemplate,
        entities: &[Entity],
        input: &str,
    ) -> HashMap<String, String> {
        let mut mappings = HashMap::new();

        for param in &template.parameters {
            if let Some(value) = self.extract_parameter_value(param, entities, input) {
                mappings.insert(param.name.clone(), value);
            }
        }

        mappings
    }

    fn extract_parameter_value(
        &self,
        param: &TemplateParameter,
        entities: &[Entity],
        input: &str,
    ) -> Option<String> {
        // 首先尝试从实体中匹配
        if let Some(entity) = self.find_matching_entity(param, entities) {
            return Some(entity.value.clone());
        }

        // 然后尝试使用提取模式
        for pattern in &param.extraction_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(input) {
                    if let Some(matched) = captures.get(1).or_else(|| captures.get(0)) {
                        return Some(matched.as_str().to_string());
                    }
                }
            }
        }

        None
    }

    fn find_matching_entity<'a>(
        &self,
        param: &TemplateParameter,
        entities: &'a [Entity],
    ) -> Option<&'a Entity> {
        entities
            .iter()
            .find(|entity| match (&param.param_type, &entity.entity_type) {
                (ParameterType::Path, EntityType::FilePath) => true,
                (ParameterType::Path, EntityType::DirectoryPath) => true,
                (ParameterType::Number, EntityType::Number) => true,
                (ParameterType::Host, EntityType::Hostname) => true,
                (ParameterType::Host, EntityType::IpAddress) => true,
                (ParameterType::ProcessId, EntityType::ProcessId) => true,
                (ParameterType::Port, EntityType::Port) => true,
                (ParameterType::Url, EntityType::Url) => true,
                _ => false,
            })
    }

    fn load_default_templates() -> HashMap<String, CommandTemplate> {
        let mut templates = HashMap::new();

        // 文件操作模板
        templates.extend(Self::create_file_operation_templates());

        // 进程管理模板
        templates.extend(Self::create_process_management_templates());

        // 网络操作模板
        templates.extend(Self::create_network_operation_templates());

        // Git 操作模板
        templates.extend(Self::create_git_operation_templates());

        // 系统信息模板
        templates.extend(Self::create_system_info_templates());

        templates
    }

    fn create_file_operation_templates() -> HashMap<String, CommandTemplate> {
        let mut templates = HashMap::new();

        // 列出文件
        templates.insert(
            "list_files".to_string(),
            CommandTemplate {
                id: "list_files".to_string(),
                name: "列出文件".to_string(),
                description: "列出目录中的文件和文件夹".to_string(),
                patterns: vec![
                    "列出.*文件".to_string(),
                    "显示.*目录.*内容".to_string(),
                    "查看.*文件夹".to_string(),
                    "list.*files".to_string(),
                    "show.*directory".to_string(),
                    "ls.*".to_string(),
                ],
                command_template: "ls {flags} {path}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "flags".to_string(),
                        param_type: ParameterType::Flags,
                        required: false,
                        default_value: Some("-la".to_string()),
                        validation_rules: vec![],
                        extraction_patterns: vec![
                            "详细".to_string(),
                            "隐藏".to_string(),
                            "大小".to_string(),
                            "recursive".to_string(),
                        ],
                    },
                    TemplateParameter {
                        name: "path".to_string(),
                        param_type: ParameterType::Path,
                        required: false,
                        default_value: Some(".".to_string()),
                        validation_rules: vec![ValidationRule::PathExists],
                        extraction_patterns: vec![
                            r"(/[\w\./\-]+)".to_string(),
                            r"([\w\./\-]+)".to_string(),
                        ],
                    },
                ],
                examples: vec![TemplateExample {
                    input: "列出当前目录下的所有文件".to_string(),
                    output: "ls -la".to_string(),
                    description: "显示详细信息和隐藏文件".to_string(),
                }],
                category: CommandCategory::FileSystem,
                risk_level: RiskLevel::Low,
            },
        );

        // 复制文件
        templates.insert(
            "copy_files".to_string(),
            CommandTemplate {
                id: "copy_files".to_string(),
                name: "复制文件".to_string(),
                description: "复制文件或目录".to_string(),
                patterns: vec![
                    "复制.*文件".to_string(),
                    "拷贝.*".to_string(),
                    "copy.*".to_string(),
                    "cp.*".to_string(),
                ],
                command_template: "cp {flags} {source} {destination}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "flags".to_string(),
                        param_type: ParameterType::Flags,
                        required: false,
                        default_value: Some("-r".to_string()),
                        validation_rules: vec![],
                        extraction_patterns: vec!["递归".to_string(), "recursive".to_string()],
                    },
                    TemplateParameter {
                        name: "source".to_string(),
                        param_type: ParameterType::Path,
                        required: true,
                        default_value: None,
                        validation_rules: vec![ValidationRule::PathExists],
                        extraction_patterns: vec![
                            r"从\s*([^\s]+)".to_string(),
                            r"复制\s*([^\s]+)".to_string(),
                        ],
                    },
                    TemplateParameter {
                        name: "destination".to_string(),
                        param_type: ParameterType::Path,
                        required: true,
                        default_value: None,
                        validation_rules: vec![],
                        extraction_patterns: vec![
                            r"到\s*([^\s]+)".to_string(),
                            r"目标\s*([^\s]+)".to_string(),
                        ],
                    },
                ],
                examples: vec![TemplateExample {
                    input: "复制 file.txt 到 backup/".to_string(),
                    output: "cp -r file.txt backup/".to_string(),
                    description: "递归复制文件到目标目录".to_string(),
                }],
                category: CommandCategory::FileSystem,
                risk_level: RiskLevel::Low,
            },
        );

        // 删除文件
        templates.insert(
            "remove_files".to_string(),
            CommandTemplate {
                id: "remove_files".to_string(),
                name: "删除文件".to_string(),
                description: "删除文件或目录".to_string(),
                patterns: vec![
                    "删除.*文件".to_string(),
                    "移除.*".to_string(),
                    "remove.*".to_string(),
                    "rm.*".to_string(),
                    "delete.*".to_string(),
                ],
                command_template: "rm {flags} {path}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "flags".to_string(),
                        param_type: ParameterType::Flags,
                        required: false,
                        default_value: Some("-i".to_string()),
                        validation_rules: vec![],
                        extraction_patterns: vec![
                            "强制".to_string(),
                            "递归".to_string(),
                            "force".to_string(),
                            "recursive".to_string(),
                        ],
                    },
                    TemplateParameter {
                        name: "path".to_string(),
                        param_type: ParameterType::Path,
                        required: true,
                        default_value: None,
                        validation_rules: vec![ValidationRule::PathExists],
                        extraction_patterns: vec![
                            r"删除\s*([^\s]+)".to_string(),
                            r"移除\s*([^\s]+)".to_string(),
                        ],
                    },
                ],
                examples: vec![TemplateExample {
                    input: "删除 temp.txt 文件".to_string(),
                    output: "rm -i temp.txt".to_string(),
                    description: "交互式删除文件".to_string(),
                }],
                category: CommandCategory::FileSystem,
                risk_level: RiskLevel::High,
            },
        );

        templates
    }

    fn create_process_management_templates() -> HashMap<String, CommandTemplate> {
        let mut templates = HashMap::new();

        // 查看进程
        templates.insert(
            "list_processes".to_string(),
            CommandTemplate {
                id: "list_processes".to_string(),
                name: "查看进程".to_string(),
                description: "显示正在运行的进程".to_string(),
                patterns: vec![
                    "查看.*进程".to_string(),
                    "显示.*进程".to_string(),
                    "list.*process".to_string(),
                    "ps.*".to_string(),
                    "进程列表".to_string(),
                ],
                command_template: "ps {flags}".to_string(),
                parameters: vec![TemplateParameter {
                    name: "flags".to_string(),
                    param_type: ParameterType::Flags,
                    required: false,
                    default_value: Some("aux".to_string()),
                    validation_rules: vec![],
                    extraction_patterns: vec![
                        "详细".to_string(),
                        "全部".to_string(),
                        "all".to_string(),
                    ],
                }],
                examples: vec![TemplateExample {
                    input: "查看所有进程".to_string(),
                    output: "ps aux".to_string(),
                    description: "显示所有用户的详细进程信息".to_string(),
                }],
                category: CommandCategory::Process,
                risk_level: RiskLevel::Low,
            },
        );

        // 终止进程
        templates.insert(
            "kill_process".to_string(),
            CommandTemplate {
                id: "kill_process".to_string(),
                name: "终止进程".to_string(),
                description: "终止指定的进程".to_string(),
                patterns: vec![
                    "杀死.*进程".to_string(),
                    "终止.*进程".to_string(),
                    "停止.*进程".to_string(),
                    "kill.*".to_string(),
                ],
                command_template: "kill {signal} {pid}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "signal".to_string(),
                        param_type: ParameterType::Signal,
                        required: false,
                        default_value: Some("-TERM".to_string()),
                        validation_rules: vec![ValidationRule::ValidSignal],
                        extraction_patterns: vec![
                            "强制".to_string(),
                            "立即".to_string(),
                            "force".to_string(),
                        ],
                    },
                    TemplateParameter {
                        name: "pid".to_string(),
                        param_type: ParameterType::ProcessId,
                        required: true,
                        default_value: None,
                        validation_rules: vec![ValidationRule::ValidNumber],
                        extraction_patterns: vec![
                            r"进程\s*(\d+)".to_string(),
                            r"PID\s*(\d+)".to_string(),
                        ],
                    },
                ],
                examples: vec![TemplateExample {
                    input: "终止进程 1234".to_string(),
                    output: "kill -TERM 1234".to_string(),
                    description: "优雅地终止进程".to_string(),
                }],
                category: CommandCategory::Process,
                risk_level: RiskLevel::Medium,
            },
        );

        templates
    }

    fn create_network_operation_templates() -> HashMap<String, CommandTemplate> {
        let mut templates = HashMap::new();

        // Ping 测试
        templates.insert(
            "ping_host".to_string(),
            CommandTemplate {
                id: "ping_host".to_string(),
                name: "网络ping测试".to_string(),
                description: "测试网络连接".to_string(),
                patterns: vec![
                    "ping.*".to_string(),
                    "测试.*连接".to_string(),
                    "检查.*网络".to_string(),
                    "网络测试".to_string(),
                ],
                command_template: "ping {flags} {host}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "flags".to_string(),
                        param_type: ParameterType::Flags,
                        required: false,
                        default_value: Some("-c 4".to_string()),
                        validation_rules: vec![],
                        extraction_patterns: vec![],
                    },
                    TemplateParameter {
                        name: "host".to_string(),
                        param_type: ParameterType::Host,
                        required: true,
                        default_value: None,
                        validation_rules: vec![ValidationRule::ValidHost],
                        extraction_patterns: vec![
                            r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b".to_string(),
                            r"\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b".to_string(),
                        ],
                    },
                ],
                examples: vec![TemplateExample {
                    input: "ping google.com".to_string(),
                    output: "ping -c 4 google.com".to_string(),
                    description: "测试到 Google 的网络连接".to_string(),
                }],
                category: CommandCategory::Network,
                risk_level: RiskLevel::Low,
            },
        );

        templates
    }

    fn create_git_operation_templates() -> HashMap<String, CommandTemplate> {
        let mut templates = HashMap::new();

        // Git 状态
        templates.insert(
            "git_status".to_string(),
            CommandTemplate {
                id: "git_status".to_string(),
                name: "Git状态".to_string(),
                description: "查看Git仓库状态".to_string(),
                patterns: vec![
                    "git.*状态".to_string(),
                    "git.*status".to_string(),
                    "查看.*git".to_string(),
                    "仓库状态".to_string(),
                ],
                command_template: "git status".to_string(),
                parameters: vec![],
                examples: vec![TemplateExample {
                    input: "查看git状态".to_string(),
                    output: "git status".to_string(),
                    description: "显示当前Git仓库的状态".to_string(),
                }],
                category: CommandCategory::Git,
                risk_level: RiskLevel::Low,
            },
        );

        templates
    }

    fn create_system_info_templates() -> HashMap<String, CommandTemplate> {
        let mut templates = HashMap::new();

        // 磁盘使用情况
        templates.insert(
            "disk_usage".to_string(),
            CommandTemplate {
                id: "disk_usage".to_string(),
                name: "磁盘使用情况".to_string(),
                description: "查看磁盘空间使用情况".to_string(),
                patterns: vec![
                    "磁盘.*使用".to_string(),
                    "硬盘.*空间".to_string(),
                    "disk.*usage".to_string(),
                    "df.*".to_string(),
                ],
                command_template: "df {flags}".to_string(),
                parameters: vec![TemplateParameter {
                    name: "flags".to_string(),
                    param_type: ParameterType::Flags,
                    required: false,
                    default_value: Some("-h".to_string()),
                    validation_rules: vec![],
                    extraction_patterns: vec!["人类可读".to_string(), "human".to_string()],
                }],
                examples: vec![TemplateExample {
                    input: "查看磁盘使用情况".to_string(),
                    output: "df -h".to_string(),
                    description: "以人类可读格式显示磁盘使用情况".to_string(),
                }],
                category: CommandCategory::System,
                risk_level: RiskLevel::Low,
            },
        );

        templates
    }
}

impl PatternMatcher {
    fn new(templates: &HashMap<String, CommandTemplate>) -> Self {
        let mut compiled_patterns = HashMap::new();

        for (id, template) in templates {
            let mut regexes = Vec::new();
            for pattern in &template.patterns {
                if let Ok(regex) = Regex::new(&pattern.to_lowercase()) {
                    regexes.push(regex);
                }
            }
            compiled_patterns.insert(id.clone(), regexes);
        }

        Self { compiled_patterns }
    }
}

impl ParameterResolver {
    fn new() -> Self {
        let mut extractors = HashMap::new();

        // 文件路径提取器
        extractors.insert(
            ParameterType::Path,
            vec![
                Regex::new(r"(/[\w\./\-]+)").unwrap(),
                Regex::new(r"([\w\./\-]+\.\w+)").unwrap(),
                Regex::new(r"(\.[\w/\-]+)").unwrap(),
            ],
        );

        // 数字提取器
        extractors.insert(ParameterType::Number, vec![Regex::new(r"(\d+)").unwrap()]);

        // 主机名提取器
        extractors.insert(
            ParameterType::Host,
            vec![
                Regex::new(r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b").unwrap(),
                Regex::new(r"\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b").unwrap(),
            ],
        );

        Self { extractors }
    }
}

impl Default for TemplateEngine {
    fn default() -> Self {
        Self::new()
    }
}
