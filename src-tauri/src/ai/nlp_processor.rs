// Natural Language Processing utilities
// This module provides NLP functionality for command processing

use crate::ai::context_analyzer::{ContextA<PERSON>y<PERSON>, EnrichedContext};
use crate::ai::manager::AIServiceManager;
use crate::ai::suggestion_engine::SuggestionEngine;
use crate::ai::template_engine::{TemplateEngine, TemplateMatch};
use crate::ai::types::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 增强的 NLP 处理器
pub struct NLPProcessor {
    ai_service: Arc<AIServiceManager>,
    template_engine: TemplateEngine,
    rule_engine: RuleEngine,
    context_analyzer: ContextAnalyzer,
    suggestion_engine: SuggestionEngine,
    command_validator: CommandValidator,
    cache: Arc<RwLock<NLPCache>>,

    // Legacy fields for backward compatibility
    command_patterns: HashMap<String, Vec<String>>,
    dangerous_commands: Vec<String>,
}

/// 规则引擎
pub struct RuleEngine {
    rules: Vec<NLPRule>,
    intent_patterns: HashMap<IntentType, Vec<String>>,
}

impl Default for RuleEngine {
    fn default() -> Self {
        Self {
            rules: Vec::new(),
            intent_patterns: HashMap::new(),
        }
    }
}

impl RuleEngine {
    pub fn match_intent(&self, _tokens: &[String]) -> Option<Intent> {
        // 简单的规则匹配实现
        None
    }
}

/// NLP 规则
#[derive(Debug, Clone)]
pub struct NLPRule {
    pub pattern: String,
    pub intent: IntentType,
    pub confidence: f32,
    pub requirements: Vec<String>,
}

/// 命令验证器
pub struct CommandValidator {
    validators: Vec<Box<dyn Fn(&str) -> Result<(), String> + Send + Sync>>,
    risk_checker: RiskChecker,
}

impl Default for CommandValidator {
    fn default() -> Self {
        Self {
            validators: Vec::new(),
            risk_checker: RiskChecker::default(),
        }
    }
}

impl CommandValidator {
    pub fn validate(&self, _command: &str) -> Result<(), String> {
        // 简单的验证实现
        Ok(())
    }
}

/// 风险检查器
pub struct RiskChecker {
    risk_patterns: HashMap<RiskLevel, Vec<String>>,
}

impl Default for RiskChecker {
    fn default() -> Self {
        Self {
            risk_patterns: HashMap::new(),
        }
    }
}

impl RiskChecker {
    pub fn assess_risk(&self, _command: &str) -> RiskLevel {
        // 简单的风险评估实现
        RiskLevel::Low
    }
}

/// NLP 缓存
pub struct NLPCache {
    command_cache: HashMap<String, CachedNLPResult>,
    intent_cache: HashMap<String, Intent>,
    entity_cache: HashMap<String, Vec<Entity>>,
}

impl Default for NLPCache {
    fn default() -> Self {
        Self {
            command_cache: HashMap::new(),
            intent_cache: HashMap::new(),
            entity_cache: HashMap::new(),
        }
    }
}

/// 缓存的 NLP 结果
#[derive(Debug, Clone)]
pub struct CachedNLPResult {
    pub result: NLPResponse,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub hit_count: u32,
}

impl NLPProcessor {
    pub async fn new(ai_service: Arc<AIServiceManager>) -> Self {
        Self {
            ai_service,
            template_engine: TemplateEngine::new(),
            rule_engine: RuleEngine::default(),
            context_analyzer: ContextAnalyzer::new().await,
            suggestion_engine: SuggestionEngine::new(),
            command_validator: CommandValidator::default(),
            cache: Arc::new(RwLock::new(NLPCache::default())),
            command_patterns: Self::load_command_patterns(),
            dangerous_commands: Self::load_dangerous_commands(),
        }
    }

    /// 处理自然语言输入的主要方法
    pub async fn process_natural_language(
        &self,
        input: &str,
        context: &TerminalContext,
    ) -> Result<NLPResponse, NLPError> {
        let start_time = std::time::Instant::now();

        // 1. 检查缓存
        if let Some(cached_result) = self.check_cache(input).await {
            return Ok(cached_result.result);
        }

        // 2. 输入预处理
        let cleaned_input = self.preprocess_input(input).await;
        let intent = self.detect_intent(&cleaned_input).await?;
        let entities = self.extract_entities(&cleaned_input, &intent).await?;

        // 3. 更新上下文分析器状态
        self.context_analyzer.update_terminal_state(context).await?;

        // 转换实体类型
        let types_entities: Vec<crate::ai::types::Entity> = entities
            .iter()
            .map(|e| {
                crate::ai::types::Entity {
                    entity_type: match e.entity_type {
                        EntityType::FilePath => crate::ai::types::EntityType::FilePath,
                        EntityType::DirectoryPath => crate::ai::types::EntityType::DirectoryPath,
                        EntityType::Command => crate::ai::types::EntityType::Command,
                        EntityType::Number => crate::ai::types::EntityType::Number,
                        EntityType::Url => crate::ai::types::EntityType::Url,
                        EntityType::IpAddress => crate::ai::types::EntityType::IpAddress,
                        EntityType::ProcessId => crate::ai::types::EntityType::ProcessId,
                        _ => crate::ai::types::EntityType::FilePath, // 默认
                    },
                    value: e.value.clone(),
                    confidence: e.confidence,
                    start_index: e.start_index,
                    end_index: e.end_index,
                    context: None,
                }
            })
            .collect();

        let enriched_context = self
            .context_analyzer
            .analyze_context(context, &types_entities)
            .await?;

        // 4. 命令生成策略
        let generation_result = match intent.confidence {
            conf if conf > 0.8 => {
                // 高置信度：使用模板匹配 + AI 增强
                self.template_based_generation(&intent, &entities, &enriched_context)
                    .await?
            }
            conf if conf > 0.5 => {
                // 中等置信度：AI 模型主导 + 规则验证
                self.ai_model_generation(&cleaned_input, &enriched_context)
                    .await?
            }
            _ => {
                // 低置信度：多策略融合
                self.hybrid_generation(&cleaned_input, &intent, &entities, &enriched_context)
                    .await?
            }
        };

        // 5. 后处理和验证
        let validated_command = self.validate_command(&generation_result).await?;
        let explanation = self.generate_explanation(&validated_command).await?;
        let suggestions = self
            .suggestion_engine
            .generate_suggestions(&validated_command, &enriched_context)
            .await?;
        let risk_assessment = self.assess_risk(&validated_command).await?;

        let processing_time = start_time.elapsed();
        let response = NLPResponse {
            generated_command: validated_command,
            explanation,
            suggestions,
            risk_assessment,
            confidence: generation_result.confidence,
            metadata: NLPMetadata {
                processing_time_ms: processing_time.as_millis() as u64,
                strategy_used: generation_result.strategy,
                model_version: "1.0.0".to_string(),
                template_matches: generation_result.template_matches,
                intent_confidence: intent.confidence,
            },
        };

        // 6. 缓存结果
        self.cache_result(input, &response).await;

        Ok(response)
    }

    async fn check_cache(&self, input: &str) -> Option<CachedNLPResult> {
        let cache = self.cache.read().await;
        cache.command_cache.get(input).cloned()
    }

    async fn cache_result(&self, input: &str, response: &NLPResponse) {
        let mut cache = self.cache.write().await;
        cache.command_cache.insert(
            input.to_string(),
            CachedNLPResult {
                result: response.clone(),
                timestamp: chrono::Utc::now(),
                hit_count: 1,
            },
        );
    }

    async fn preprocess_input(&self, input: &str) -> CleanedInput {
        CleanedInput {
            original: input.to_string(),
            normalized: self.normalize_text(input),
            tokens: self.tokenize(input),
            sanitized: self.sanitize_input(input),
        }
    }

    fn normalize_text(&self, input: &str) -> String {
        // 标准化文本：转小写，去除多余空格等
        input
            .to_lowercase()
            .trim()
            .split_whitespace()
            .collect::<Vec<_>>()
            .join(" ")
    }

    fn tokenize(&self, input: &str) -> Vec<String> {
        // 简单的分词
        input.split_whitespace().map(|s| s.to_string()).collect()
    }

    fn sanitize_input(&self, input: &str) -> String {
        // 去除潜在的危险字符
        input
            .chars()
            .filter(|c| c.is_alphanumeric() || c.is_whitespace() || ".-_/".contains(*c))
            .collect()
    }

    async fn detect_intent(&self, input: &CleanedInput) -> Result<Intent, NLPError> {
        // 首先尝试规则引擎
        if let Some(rule_intent) = self.rule_engine.match_intent(&input.tokens) {
            return Ok(rule_intent);
        }

        // 然后尝试 AI 模型
        match self.ai_service.predict_intent(&input.normalized).await {
            Ok(ai_intent) => Ok(ai_intent),
            Err(_) => {
                // 回退到简单的关键词匹配
                Ok(self.fallback_intent_detection(&input.normalized))
            }
        }
    }

    fn fallback_intent_detection(&self, input: &str) -> Intent {
        let input_lower = input.to_lowercase();

        let intent_type = if input_lower.contains("文件")
            || input_lower.contains("目录")
            || input_lower.contains("file")
        {
            IntentType::FileOperation
        } else if input_lower.contains("进程")
            || input_lower.contains("process")
            || input_lower.contains("kill")
        {
            IntentType::ProcessControl
        } else if input_lower.contains("git") || input_lower.contains("仓库") {
            IntentType::GitOperation
        } else if input_lower.contains("网络")
            || input_lower.contains("ping")
            || input_lower.contains("curl")
        {
            IntentType::NetworkOperation
        } else if input_lower.contains("系统")
            || input_lower.contains("磁盘")
            || input_lower.contains("内存")
        {
            IntentType::SystemInfo
        } else {
            IntentType::Unknown
        };

        Intent {
            intent_type,
            confidence: 0.6,
            context_dependent: false,
        }
    }

    async fn extract_entities(
        &self,
        input: &CleanedInput,
        _intent: &Intent,
    ) -> Result<Vec<Entity>, NLPError> {
        let mut entities = Vec::new();

        // 简单的实体提取
        for (i, token) in input.tokens.iter().enumerate() {
            // 检查文件路径
            if token.contains('/') || token.contains('\\') {
                entities.push(Entity {
                    entity_type: EntityType::FilePath,
                    value: token.clone(),
                    confidence: 0.8,
                    start_index: i,
                    end_index: i + 1,
                    context: None,
                });
            }

            // 检查数字
            if token.parse::<i32>().is_ok() {
                entities.push(Entity {
                    entity_type: EntityType::Number,
                    value: token.clone(),
                    confidence: 0.9,
                    start_index: i,
                    end_index: i + 1,
                    context: None,
                });
            }

            // 检查 IP 地址
            if self.is_ip_address(token) {
                entities.push(Entity {
                    entity_type: EntityType::IpAddress,
                    value: token.clone(),
                    confidence: 0.95,
                    start_index: i,
                    end_index: i + 1,
                    context: None,
                });
            }

            // 检查主机名
            if self.is_hostname(token) {
                entities.push(Entity {
                    entity_type: EntityType::Hostname,
                    value: token.clone(),
                    confidence: 0.7,
                    start_index: i,
                    end_index: i + 1,
                    context: None,
                });
            }
        }

        Ok(entities)
    }

    fn is_ip_address(&self, token: &str) -> bool {
        use std::net::Ipv4Addr;
        token.parse::<Ipv4Addr>().is_ok()
    }

    fn is_hostname(&self, token: &str) -> bool {
        token.contains('.') && !token.contains('/') && token.len() > 3
    }

    async fn template_based_generation(
        &self,
        _intent: &Intent,
        entities: &[Entity],
        _context: &EnrichedContext,
    ) -> Result<GenerationResult, NLPError> {
        // 使用模板引擎匹配模板
        let matches = self
            .template_engine
            .match_templates(&_intent.intent_type.to_string(), entities);

        if let Some(best_match) = matches.first() {
            let command = self
                .template_engine
                .generate_command(best_match)
                .map_err(|e| NLPError::TemplateMatchingFailed(e.to_string()))?;

            Ok(GenerationResult {
                command,
                confidence: best_match.score,
                strategy: ProcessingStrategy::TemplateMatching,
                template_matches: matches.len() as u32,
            })
        } else {
            Err(NLPError::TemplateMatchingFailed(
                "No suitable template found".to_string(),
            ))
        }
    }

    async fn ai_model_generation(
        &self,
        input: &CleanedInput,
        context: &EnrichedContext,
    ) -> Result<GenerationResult, NLPError> {
        // 构建 AI 模型的提示
        let prompt = self.build_ai_prompt(input, context);

        // 调用 AI 服务
        let ai_context = AIContext {
            session_id: "nlp_session".to_string(),
            current_directory: context.base.current_directory.clone(),
            shell_type: context.base.shell_type.clone(),
            environment: context.base.environment_variables.clone(),
            command_history: context
                .base
                .command_history
                .iter()
                .map(|h| h.command.clone())
                .collect(),
            user_preferences: UserPreferences::default(),
        };

        match self
            .ai_service
            .process_natural_language(&prompt, &ai_context)
            .await
        {
            Ok(response) => Ok(GenerationResult {
                command: response.content,
                confidence: 0.8,
                strategy: ProcessingStrategy::AIGeneration,
                template_matches: 0,
            }),
            Err(e) => Err(NLPError::AIServiceError(e)),
        }
    }

    async fn hybrid_generation(
        &self,
        input: &CleanedInput,
        intent: &Intent,
        entities: &[Entity],
        context: &EnrichedContext,
    ) -> Result<GenerationResult, NLPError> {
        // 尝试模板匹配
        if let Ok(template_result) = self
            .template_based_generation(intent, entities, context)
            .await
        {
            if template_result.confidence > 0.5 {
                return Ok(template_result);
            }
        }

        // 回退到 AI 生成
        self.ai_model_generation(input, context).await
    }

    fn build_ai_prompt(&self, input: &CleanedInput, context: &EnrichedContext) -> String {
        let mut prompt = format!("用户输入：{}\n", input.original);
        prompt.push_str(&format!("当前目录：{}\n", context.base.current_directory));
        prompt.push_str(&format!("Shell类型：{}\n", context.base.shell_type));

        if context.directory.is_git_repo {
            prompt.push_str("当前目录是Git仓库\n");
        }

        if !context.directory.special_files.is_empty() {
            prompt.push_str("特殊文件：");
            for file in &context.directory.special_files {
                prompt.push_str(&format!("{} ", file.name));
            }
            prompt.push('\n');
        }

        prompt.push_str("请生成对应的命令行命令：");
        prompt
    }

    async fn validate_command(
        &self,
        result: &GenerationResult,
    ) -> Result<GeneratedCommand, NLPError> {
        // 使用命令验证器
        self.command_validator.validate(&result.command)?;

        // 解析命令部分
        let parsed_parts = self.parse_command_parts(&result.command);

        Ok(GeneratedCommand {
            command: result.command.clone(),
            parsed_parts,
            execution_context: ExecutionContext {
                working_directory: ".".to_string(),
                required_permissions: vec![],
                expected_output_type: OutputType::Text,
                side_effects: vec![],
            },
            requires_confirmation: self.requires_confirmation(&result.command),
        })
    }

    fn parse_command_parts(&self, command: &str) -> Vec<CommandPart> {
        let parts: Vec<&str> = command.split_whitespace().collect();
        let mut command_parts = Vec::new();

        for (i, part) in parts.iter().enumerate() {
            let part_type = if i == 0 {
                CommandPartType::Program
            } else if part.starts_with('-') {
                CommandPartType::Flag
            } else if part.contains('|') {
                CommandPartType::Pipe
            } else if part.contains('>') || part.contains('<') {
                CommandPartType::Redirect
            } else {
                CommandPartType::Argument
            };

            command_parts.push(CommandPart {
                part_type,
                value: part.to_string(),
                description: format!("Command part: {}", part),
            });
        }

        command_parts
    }

    fn requires_confirmation(&self, command: &str) -> bool {
        // 检查是否需要用户确认
        self.dangerous_commands
            .iter()
            .any(|dangerous| command.contains(dangerous))
    }

    async fn generate_explanation(
        &self,
        command: &GeneratedCommand,
    ) -> Result<CommandExplanation, NLPError> {
        Ok(CommandExplanation {
            summary: format!("执行命令: {}", command.command),
            detailed_breakdown: command
                .parsed_parts
                .iter()
                .map(|part| ParameterExplanation {
                    parameter: part.value.clone(),
                    description: part.description.clone(),
                    example: None,
                    is_optional: !matches!(part.part_type, CommandPartType::Program),
                })
                .collect(),
            expected_output: "命令执行结果".to_string(),
            potential_side_effects: command.execution_context.side_effects.clone(),
        })
    }

    async fn assess_risk(&self, command: &GeneratedCommand) -> Result<RiskAssessment, NLPError> {
        let risk_level = self
            .command_validator
            .risk_checker
            .assess_risk(&command.command);
        let risk_factors = self.analyze_risk_factors(&command.command);

        Ok(RiskAssessment {
            risk_level,
            risk_factors,
            mitigation_suggestions: vec!["请谨慎执行命令".to_string()],
            requires_confirmation: command.requires_confirmation,
        })
    }

    fn analyze_risk_factors(&self, command: &str) -> Vec<RiskFactor> {
        let mut factors = Vec::new();

        if command.contains("rm") {
            factors.push(RiskFactor {
                factor_type: RiskFactorType::DataLoss,
                description: "可能删除文件".to_string(),
                severity: 7,
            });
        }

        if command.contains("sudo") {
            factors.push(RiskFactor {
                factor_type: RiskFactorType::PermissionEscalation,
                description: "需要管理员权限".to_string(),
                severity: 6,
            });
        }

        factors
    }

    /// Analyze input text for command intent
    pub fn analyze_intent(&self, input: &str) -> CommandIntent {
        let input_lower = input.to_lowercase();

        // Check for file operations
        if input_lower.contains("file") || input_lower.contains("directory") {
            return CommandIntent::FileOperation;
        }

        // Check for process operations
        if input_lower.contains("process")
            || input_lower.contains("kill")
            || input_lower.contains("run")
        {
            return CommandIntent::ProcessOperation;
        }

        // Check for git operations
        if input_lower.contains("git")
            || input_lower.contains("commit")
            || input_lower.contains("push")
        {
            return CommandIntent::GitOperation;
        }

        // Check for network operations
        if input_lower.contains("download")
            || input_lower.contains("curl")
            || input_lower.contains("wget")
        {
            return CommandIntent::NetworkOperation;
        }

        CommandIntent::General
    }

    /// Extract entities from text
    pub fn extract_entities_legacy(&self, input: &str) -> Vec<Entity> {
        let mut entities = Vec::new();

        // Simple entity extraction (can be enhanced with more sophisticated NLP)
        let words: Vec<&str> = input.split_whitespace().collect();

        for (i, word) in words.iter().enumerate() {
            // Check for file paths
            if word.contains('/') || word.contains('\\') {
                entities.push(Entity {
                    entity_type: EntityType::FilePath,
                    value: word.to_string(),
                    confidence: 0.8,
                    start_index: i,
                    end_index: i + 1,
                    context: None,
                });
            }

            // Check for numbers
            if word.parse::<i32>().is_ok() {
                entities.push(Entity {
                    entity_type: EntityType::Number,
                    value: word.to_string(),
                    confidence: 0.9,
                    start_index: i,
                    end_index: i + 1,
                    context: None,
                });
            }
        }

        entities
    }

    /// Check if a command is potentially dangerous
    pub fn is_dangerous_command(&self, command: &str) -> bool {
        let command_lower = command.to_lowercase();

        for dangerous in &self.dangerous_commands {
            if command_lower.contains(dangerous) {
                return true;
            }
        }

        false
    }

    /// Suggest safer alternatives for dangerous commands
    pub fn suggest_safer_alternative(&self, command: &str) -> Option<String> {
        let command_lower = command.to_lowercase();

        if command_lower.contains("rm -rf") {
            return Some(
                "Consider using 'rm -i' for interactive deletion or move files to trash first"
                    .to_string(),
            );
        }

        if command_lower.contains("sudo rm") {
            return Some(
                "Be very careful with sudo rm. Consider backing up files first".to_string(),
            );
        }

        if command_lower.contains("chmod 777") {
            return Some("chmod 777 gives full permissions to everyone. Consider more restrictive permissions".to_string());
        }

        None
    }

    fn load_command_patterns() -> HashMap<String, Vec<String>> {
        let mut patterns = HashMap::new();

        patterns.insert(
            "list_files".to_string(),
            vec![
                "ls".to_string(),
                "dir".to_string(),
                "show files".to_string(),
                "list directory".to_string(),
            ],
        );

        patterns.insert(
            "change_directory".to_string(),
            vec![
                "cd".to_string(),
                "go to".to_string(),
                "navigate to".to_string(),
                "change directory".to_string(),
            ],
        );

        patterns.insert(
            "copy_files".to_string(),
            vec![
                "cp".to_string(),
                "copy".to_string(),
                "duplicate".to_string(),
            ],
        );

        patterns
    }

    fn load_dangerous_commands() -> Vec<String> {
        vec![
            "rm -rf".to_string(),
            "sudo rm".to_string(),
            "mkfs".to_string(),
            "dd if=".to_string(),
            "chmod 777".to_string(),
            "chown -R".to_string(),
            ":(){ :|:& };:".to_string(), // Fork bomb
            "format".to_string(),
            "fdisk".to_string(),
        ]
    }
}

impl Default for NLPProcessor {
    fn default() -> Self {
        // 创建一个简单的默认实现，不使用AI服务
        Self {
            ai_service: Arc::new(AIServiceManager::default()),
            template_engine: TemplateEngine::new(),
            rule_engine: RuleEngine::default(),
            context_analyzer: ContextAnalyzer::default(),
            suggestion_engine: SuggestionEngine::new(),
            command_validator: CommandValidator::default(),
            cache: Arc::new(RwLock::new(NLPCache::default())),
            command_patterns: Self::load_command_patterns(),
            dangerous_commands: Self::load_dangerous_commands(),
        }
    }
}

/// Command intent classification
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CommandIntent {
    FileOperation,
    ProcessOperation,
    GitOperation,
    NetworkOperation,
    SystemInfo,
    General,
}

/// Command transformation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandTransformation {
    pub original_input: String,
    pub generated_command: String,
    pub confidence: f32,
    pub intent: CommandIntent,
    pub entities: Vec<Entity>,
    pub is_dangerous: bool,
    pub safety_warning: Option<String>,
    pub safer_alternative: Option<String>,
}

impl NLPProcessor {
    /// Transform natural language to command
    pub fn transform_to_command(&self, input: &str) -> CommandTransformation {
        let intent = self.analyze_intent(input);
        let entities = self.extract_entities_legacy(input);

        // Generate command based on intent (simplified)
        let generated_command = self.generate_command_for_intent(&intent, input);
        let is_dangerous = self.is_dangerous_command(&generated_command);
        let safety_warning = if is_dangerous {
            Some("This command may be dangerous. Please review before executing.".to_string())
        } else {
            None
        };
        let safer_alternative = self.suggest_safer_alternative(&generated_command);

        CommandTransformation {
            original_input: input.to_string(),
            generated_command,
            confidence: 0.7, // Placeholder confidence
            intent,
            entities,
            is_dangerous,
            safety_warning,
            safer_alternative,
        }
    }

    fn generate_command_for_intent(&self, intent: &CommandIntent, input: &str) -> String {
        match intent {
            CommandIntent::FileOperation => {
                if input.to_lowercase().contains("list") {
                    "ls -la".to_string()
                } else if input.to_lowercase().contains("copy") {
                    "cp".to_string()
                } else {
                    "ls".to_string()
                }
            }
            CommandIntent::ProcessOperation => {
                if input.to_lowercase().contains("list") {
                    "ps aux".to_string()
                } else {
                    "ps".to_string()
                }
            }
            CommandIntent::GitOperation => {
                if input.to_lowercase().contains("status") {
                    "git status".to_string()
                } else if input.to_lowercase().contains("commit") {
                    "git commit".to_string()
                } else {
                    "git".to_string()
                }
            }
            CommandIntent::NetworkOperation => {
                if input.to_lowercase().contains("download") {
                    "curl".to_string()
                } else {
                    "ping".to_string()
                }
            }
            CommandIntent::SystemInfo => "uname -a".to_string(),
            CommandIntent::General => {
                // Fallback to echo for safety
                format!("echo '{}'", input)
            }
        }
    }
}
