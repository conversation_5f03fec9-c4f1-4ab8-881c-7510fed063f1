// Storage management
// This module handles data persistence and file operations

use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};

/// Storage manager for handling data persistence
#[derive(Debug, <PERSON>lone)]
pub struct StorageManager {
    data_directory: PathBuf,
}

impl StorageManager {
    pub fn new(data_dir: &str) -> Result<Self, super::SystemError> {
        let data_directory = PathBuf::from(data_dir);

        // Create data directory if it doesn't exist
        if !data_directory.exists() {
            fs::create_dir_all(&data_directory)?;
        }

        Ok(Self { data_directory })
    }

    /// Save data to a file
    pub fn save<T: Serialize>(&self, filename: &str, data: &T) -> Result<(), super::SystemError> {
        let file_path = self.data_directory.join(filename);

        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent)?;
        }

        let json_data = serde_json::to_string_pretty(data)
            .map_err(|e| super::SystemError::StorageError(e.to_string()))?;

        fs::write(file_path, json_data)?;
        Ok(())
    }

    /// Load data from a file
    pub fn load<T: for<'de> Deserialize<'de>>(
        &self,
        filename: &str,
    ) -> Result<T, super::SystemError> {
        let file_path = self.data_directory.join(filename);

        if !file_path.exists() {
            return Err(super::SystemError::StorageError(format!(
                "File {} does not exist",
                filename
            )));
        }

        let content = fs::read_to_string(file_path)?;
        let data: T = serde_json::from_str(&content)
            .map_err(|e| super::SystemError::StorageError(e.to_string()))?;

        Ok(data)
    }

    /// Check if a file exists
    pub fn exists(&self, filename: &str) -> bool {
        self.data_directory.join(filename).exists()
    }

    /// Delete a file
    pub fn delete(&self, filename: &str) -> Result<(), super::SystemError> {
        let file_path = self.data_directory.join(filename);

        if file_path.exists() {
            fs::remove_file(file_path)?;
        }

        Ok(())
    }

    /// List all files in the data directory
    pub fn list_files(&self) -> Result<Vec<String>, super::SystemError> {
        let mut files = Vec::new();

        for entry in fs::read_dir(&self.data_directory)? {
            let entry = entry?;
            if entry.file_type()?.is_file() {
                if let Some(filename) = entry.file_name().to_str() {
                    files.push(filename.to_string());
                }
            }
        }

        Ok(files)
    }

    /// Get the data directory path
    pub fn get_data_directory(&self) -> &Path {
        &self.data_directory
    }
}
