use serde::{Deserialize, Serialize};
use tauri::command;

/// 剪贴板操作错误
#[derive(Debug, thiserror::Error)]
pub enum ClipboardError {
    #[error("剪贴板访问失败: {0}")]
    AccessError(String),

    #[error("剪贴板内容为空")]
    EmptyContent,

    #[error("不支持的内容类型")]
    UnsupportedFormat,

    #[error("系统错误: {0}")]
    SystemError(String),
}

/// 剪贴板内容类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ClipboardContent {
    Text(String),
    Html(String),
    Image(Vec<u8>),
    Files(Vec<String>),
}

/// 剪贴板管理器
#[derive(Debug)]
pub struct ClipboardManager {
    #[cfg(target_os = "windows")]
    _phantom: std::marker::PhantomData<()>,
}

impl ClipboardManager {
    pub fn new() -> Self {
        Self {
            #[cfg(target_os = "windows")]
            _phantom: std::marker::PhantomData,
        }
    }

    /// 读取剪贴板文本内容
    pub fn read_text(&self) -> Result<String, ClipboardError> {
        #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
        {
            use arboard::Clipboard;

            let mut clipboard =
                Clipboard::new().map_err(|e| ClipboardError::AccessError(e.to_string()))?;

            clipboard
                .get_text()
                .map_err(|e| ClipboardError::AccessError(e.to_string()))
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Err(ClipboardError::SystemError("不支持的操作系统".to_string()))
        }
    }

    /// 写入文本到剪贴板
    pub fn write_text(&self, text: &str) -> Result<(), ClipboardError> {
        #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
        {
            use arboard::Clipboard;

            let mut clipboard =
                Clipboard::new().map_err(|e| ClipboardError::AccessError(e.to_string()))?;

            clipboard
                .set_text(text)
                .map_err(|e| ClipboardError::AccessError(e.to_string()))
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Err(ClipboardError::SystemError("不支持的操作系统".to_string()))
        }
    }

    /// 检查剪贴板是否包含文本内容
    pub fn has_text(&self) -> bool {
        self.read_text().is_ok()
    }

    /// 清空剪贴板
    pub fn clear(&self) -> Result<(), ClipboardError> {
        self.write_text("")
    }
}

impl Default for ClipboardManager {
    fn default() -> Self {
        Self::new()
    }
}

// ============================================================================
// Tauri Commands for Clipboard operations
// ============================================================================

/// 读取剪贴板文本内容
#[command]
pub async fn read_clipboard() -> Result<String, String> {
    let clipboard = ClipboardManager::new();
    clipboard.read_text().map_err(|e| e.to_string())
}

/// 写入文本到剪贴板
#[command]
pub async fn write_clipboard(text: String) -> Result<(), String> {
    let clipboard = ClipboardManager::new();
    clipboard.write_text(&text).map_err(|e| e.to_string())
}

/// 检查剪贴板是否有文本内容
#[command]
pub async fn has_clipboard_text() -> Result<bool, String> {
    let clipboard = ClipboardManager::new();
    Ok(clipboard.has_text())
}

/// 清空剪贴板
#[command]
pub async fn clear_clipboard() -> Result<(), String> {
    let clipboard = ClipboardManager::new();
    clipboard.clear().map_err(|e| e.to_string())
}
