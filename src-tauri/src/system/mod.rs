// System module - Configuration, security, and storage management
// This module handles system-level functionality

pub mod clipboard;
pub mod config;
pub mod security;
pub mod shortcuts;
pub mod storage;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Re-export public types
pub use clipboard::*;
pub use config::*;
pub use security::*;
pub use shortcuts::*;
pub use storage::*;

/// System configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfig {
    pub app_name: String,
    pub version: String,
    pub data_directory: String,
    pub log_level: String,
    pub max_log_files: usize,
}

impl Default for SystemConfig {
    fn default() -> Self {
        Self {
            app_name: "TAgent".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            data_directory: Self::get_default_data_dir(),
            log_level: "info".to_string(),
            max_log_files: 10,
        }
    }
}

impl SystemConfig {
    fn get_default_data_dir() -> String {
        if let Some(data_dir) = dirs::data_dir() {
            data_dir.join("TAgent").to_string_lossy().to_string()
        } else {
            ".tagent".to_string()
        }
    }
}

/// System manager for coordinating system-level operations
#[derive(Debug)]
pub struct SystemManager {
    config: SystemConfig,
    security_manager: SecurityManager,
    storage_manager: StorageManager,
}

impl SystemManager {
    pub fn new() -> Result<Self, SystemError> {
        let config = SystemConfig::default();
        let security_manager = SecurityManager::new();
        let storage_manager = StorageManager::new(&config.data_directory)?;

        Ok(Self {
            config,
            security_manager,
            storage_manager,
        })
    }

    pub fn get_config(&self) -> &SystemConfig {
        &self.config
    }

    pub fn get_security_manager(&self) -> &SecurityManager {
        &self.security_manager
    }

    pub fn get_storage_manager(&self) -> &StorageManager {
        &self.storage_manager
    }
}

impl Default for SystemManager {
    fn default() -> Self {
        Self::new().expect("Failed to create system manager")
    }
}

/// System-related errors
#[derive(Debug, thiserror::Error)]
pub enum SystemError {
    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Storage error: {0}")]
    StorageError(String),

    #[error("Security error: {0}")]
    SecurityError(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}

/// System information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub platform: String,
    pub arch: String,
    pub version: String,
}

/// Application settings structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppSettings {
    pub terminal: TerminalSettings,
    pub ai: AISettings,
    pub appearance: AppearanceSettings,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalSettings {
    pub default_shell: String,
    pub font_size: u32,
    pub font_family: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AISettings {
    pub default_provider: String,
    pub auto_suggest: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppearanceSettings {
    pub theme: String,
    pub opacity: f32,
}

// ============================================================================
// Tauri Commands for System functionality
// ============================================================================

use tauri::command;

#[command]
pub async fn get_app_version() -> Result<String, String> {
    Ok(env!("CARGO_PKG_VERSION").to_string())
}

#[command]
pub async fn get_system_info() -> Result<SystemInfo, String> {
    Ok(SystemInfo {
        platform: std::env::consts::OS.to_string(),
        arch: std::env::consts::ARCH.to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
    })
}

#[command]
pub async fn get_settings() -> Result<AppSettings, String> {
    Ok(AppSettings {
        terminal: TerminalSettings {
            default_shell: "zsh".to_string(),
            font_size: 14,
            font_family: "Monaco".to_string(),
        },
        ai: AISettings {
            default_provider: "local".to_string(),
            auto_suggest: false,
        },
        appearance: AppearanceSettings {
            theme: "dark".to_string(),
            opacity: 0.95,
        },
    })
}

#[command]
pub async fn update_settings(_settings: AppSettings) -> Result<(), String> {
    // TODO: Implement actual settings persistence
    log::info!("Settings updated (placeholder implementation)");
    Ok(())
}
