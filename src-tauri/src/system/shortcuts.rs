use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager};

// Tauri 2.0 使用插件系统
use tauri_plugin_global_shortcut::{GlobalShortcutExt, Shortcut};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GlobalShortcutConfig {
    pub keys: String,
    pub action: String,
    pub enabled: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ShortcutConfig {
    pub shortcuts: HashMap<String, GlobalShortcutConfig>,
}

pub struct GlobalShortcutManagerState {
    shortcuts: HashMap<String, GlobalShortcutConfig>,
    app_handle: AppHandle,
}

impl GlobalShortcutManagerState {
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            shortcuts: HashMap::new(),
            app_handle,
        }
    }

    /// 注册全局快捷键
    pub fn register_shortcut(&mut self, keys: &str, action: String) -> Result<(), String> {
        let shortcut = keys
            .parse::<Shortcut>()
            .map_err(|e| format!("Invalid shortcut format '{}': {}", keys, e))?;

        // 使用 Tauri 2.0 插件 API 注册全局快捷键
        let app_handle = self.app_handle.clone();
        let action_clone = action.clone();
        let keys_owned = keys.to_string(); // 转换为owned String

        self.app_handle
            .global_shortcut()
            .on_shortcut(shortcut, move |_app, _shortcut, event| {
                match event.state() {
                    tauri_plugin_global_shortcut::ShortcutState::Pressed => {
                        // 这里可以发送事件到前端或执行其他操作
                        if let Some(window) = app_handle.get_webview_window("main") {
                            let _ = window.emit("shortcut-triggered", &action_clone);
                        }
                    }
                    tauri_plugin_global_shortcut::ShortcutState::Released => {
                        // 可以处理按键释放事件
                    }
                }
            })
            .map_err(|e| format!("Failed to register shortcut '{}': {}", keys, e))?;

        // 保存配置
        self.shortcuts.insert(
            keys.to_string(),
            GlobalShortcutConfig {
                keys: keys.to_string(),
                action,
                enabled: true,
            },
        );
        Ok(())
    }

    /// 注销全局快捷键
    pub fn unregister_shortcut(&mut self, keys: &str) -> Result<(), String> {
        let shortcut = keys
            .parse::<Shortcut>()
            .map_err(|e| format!("Invalid shortcut format '{}': {}", keys, e))?;

        // 使用 Tauri 2.0 插件 API 注销全局快捷键
        self.app_handle
            .global_shortcut()
            .unregister(shortcut)
            .map_err(|e| format!("Failed to unregister shortcut '{}': {}", keys, e))?;

        self.shortcuts.remove(keys);
        Ok(())
    }

    /// 更新快捷键
    pub fn update_shortcut(
        &mut self,
        old_keys: &str,
        new_keys: &str,
        action: String,
    ) -> Result<(), String> {
        // 先注销旧的快捷键
        if let Err(e) = self.unregister_shortcut(old_keys) {
            eprintln!("Failed to unregister old shortcut: {}", e);
        }

        // 注册新的快捷键
        self.register_shortcut(new_keys, action)
    }

    /// 获取所有已注册的快捷键
    pub fn get_shortcuts(&self) -> &HashMap<String, GlobalShortcutConfig> {
        &self.shortcuts
    }

    /// 启用/禁用快捷键
    pub fn set_shortcut_enabled(&mut self, keys: &str, enabled: bool) -> Result<(), String> {
        // 首先检查快捷键是否存在，并获取必要的信息
        let (current_enabled, action) = if let Some(shortcut_config) = self.shortcuts.get(keys) {
            (shortcut_config.enabled, shortcut_config.action.clone())
        } else {
            return Err(format!("Shortcut {} not found", keys));
        };

        // 根据状态变化执行操作
        if enabled && !current_enabled {
            // 重新注册快捷键
            self.register_shortcut(keys, action)?;
        } else if !enabled && current_enabled {
            // 注销快捷键
            self.unregister_shortcut(keys)?;
        }

        // 更新状态
        if let Some(shortcut_config) = self.shortcuts.get_mut(keys) {
            shortcut_config.enabled = enabled;
        }

        Ok(())
    }

    /// 清除所有快捷键
    pub fn clear_all_shortcuts(&mut self) -> Result<(), String> {
        // 使用 Tauri 2.0 插件 API 清除所有快捷键
        self.app_handle
            .global_shortcut()
            .unregister_all()
            .map_err(|e| format!("Failed to clear all shortcuts: {}", e))?;

        self.shortcuts.clear();
        Ok(())
    }

    /// 检查快捷键是否已注册
    pub fn is_registered(&self, keys: &str) -> Result<bool, String> {
        let shortcut = keys
            .parse::<Shortcut>()
            .map_err(|e| format!("Invalid shortcut format '{}': {}", keys, e))?;

        // 使用 Tauri 2.0 插件 API 检查快捷键是否已注册
        // is_registered 直接返回 bool，不是 Result
        let is_registered = self.app_handle.global_shortcut().is_registered(shortcut);
        Ok(is_registered)
    }
}

/// 初始化全局快捷键管理器
pub fn init_global_shortcuts(app_handle: AppHandle) -> GlobalShortcutManagerState {
    let manager = GlobalShortcutManagerState::new(app_handle);

    // 这里可以注册一些默认的全局快捷键
    // 例如：manager.register_shortcut("CmdOrCtrl+`", "toggle_app".to_string());

    manager
}

// ============================================================================
// Tauri Commands for Global Shortcuts
// ============================================================================

use std::sync::Mutex;
use tauri::{command, State};

pub type GlobalShortcutManagerStateType = Mutex<GlobalShortcutManagerState>;

#[command]
pub async fn register_global_shortcut(
    keys: String,
    action: String,
    state: State<'_, GlobalShortcutManagerStateType>,
) -> Result<(), String> {
    let mut manager = state
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    manager.register_shortcut(&keys, action)
}

#[command]
pub async fn unregister_global_shortcut(
    keys: String,
    state: State<'_, GlobalShortcutManagerStateType>,
) -> Result<(), String> {
    let mut manager = state
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    manager.unregister_shortcut(&keys)
}

#[command]
pub async fn get_global_shortcuts(
    state: State<'_, GlobalShortcutManagerStateType>,
) -> Result<HashMap<String, GlobalShortcutConfig>, String> {
    let manager = state
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    Ok(manager.get_shortcuts().clone())
}

#[command]
pub async fn update_global_shortcut(
    old_keys: String,
    new_keys: String,
    action: String,
    state: State<'_, GlobalShortcutManagerStateType>,
) -> Result<(), String> {
    let mut manager = state
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    manager.update_shortcut(&old_keys, &new_keys, action)
}

#[command]
pub async fn set_global_shortcut_enabled(
    keys: String,
    enabled: bool,
    state: State<'_, GlobalShortcutManagerStateType>,
) -> Result<(), String> {
    let mut manager = state
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    manager.set_shortcut_enabled(&keys, enabled)
}

#[command]
pub async fn clear_all_global_shortcuts(
    state: State<'_, GlobalShortcutManagerStateType>,
) -> Result<(), String> {
    let mut manager = state
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    manager.clear_all_shortcuts()
}

#[command]
pub async fn is_global_shortcut_registered(
    keys: String,
    state: State<'_, GlobalShortcutManagerStateType>,
) -> Result<bool, String> {
    let manager = state
        .lock()
        .map_err(|e| format!("Failed to lock state: {}", e))?;
    manager.is_registered(&keys)
}
