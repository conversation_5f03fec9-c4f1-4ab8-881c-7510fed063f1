// Configuration management
// This module handles application configuration

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// Configuration manager
#[derive(Debug, <PERSON>lone)]
pub struct ConfigManager {
    config_path: PathBuf,
    configs: HashMap<String, serde_json::Value>,
}

impl ConfigManager {
    pub fn new(config_dir: &str) -> Self {
        let config_path = PathBuf::from(config_dir).join("config.json");

        Self {
            config_path,
            configs: HashMap::new(),
        }
    }

    pub fn load(&mut self) -> Result<(), super::SystemError> {
        if self.config_path.exists() {
            let content = std::fs::read_to_string(&self.config_path)?;
            self.configs = serde_json::from_str(&content)
                .map_err(|e| super::SystemError::ConfigError(e.to_string()))?;
        }
        Ok(())
    }

    pub fn save(&self) -> Result<(), super::SystemError> {
        if let Some(parent) = self.config_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let content = serde_json::to_string_pretty(&self.configs)
            .map_err(|e| super::SystemError::ConfigError(e.to_string()))?;

        std::fs::write(&self.config_path, content)?;
        Ok(())
    }

    pub fn get<T: for<'de> Deserialize<'de>>(&self, key: &str) -> Option<T> {
        self.configs
            .get(key)
            .and_then(|v| serde_json::from_value(v.clone()).ok())
    }

    pub fn set<T: Serialize>(&mut self, key: &str, value: T) -> Result<(), super::SystemError> {
        let json_value = serde_json::to_value(value)
            .map_err(|e| super::SystemError::ConfigError(e.to_string()))?;

        self.configs.insert(key.to_string(), json_value);
        Ok(())
    }
}
