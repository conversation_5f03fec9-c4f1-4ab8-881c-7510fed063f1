// Security management
// This module handles security checks and command validation

use serde::{Deserialize, Serialize};
use std::collections::HashSet;

/// Security manager for command validation and safety checks
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct SecurityManager {
    dangerous_commands: HashSet<String>,
    safe_mode: bool,
}

impl SecurityManager {
    pub fn new() -> Self {
        Self {
            dangerous_commands: Self::load_dangerous_commands(),
            safe_mode: true,
        }
    }

    /// Check if a command is safe to execute
    pub fn is_command_safe(&self, command: &str) -> bool {
        if !self.safe_mode {
            return true;
        }

        let command_lower = command.to_lowercase();

        for dangerous in &self.dangerous_commands {
            if command_lower.contains(dangerous) {
                return false;
            }
        }

        true
    }

    /// Get security assessment for a command
    pub fn assess_command(&self, command: &str) -> SecurityAssessment {
        let is_safe = self.is_command_safe(command);
        let risk_level = if is_safe {
            RiskLevel::Low
        } else {
            RiskLevel::High
        };

        SecurityAssessment {
            command: command.to_string(),
            is_safe,
            risk_level,
            warnings: self.get_warnings(command),
            suggestions: self.get_suggestions(command),
        }
    }

    fn load_dangerous_commands() -> HashSet<String> {
        vec![
            "rm -rf".to_string(),
            "sudo rm".to_string(),
            "mkfs".to_string(),
            "dd if=".to_string(),
            "chmod 777".to_string(),
            "chown -R".to_string(),
            "format".to_string(),
            "fdisk".to_string(),
        ]
        .into_iter()
        .collect()
    }

    fn get_warnings(&self, command: &str) -> Vec<String> {
        let mut warnings = Vec::new();
        let command_lower = command.to_lowercase();

        if command_lower.contains("rm -rf") {
            warnings.push("This command will recursively delete files/directories".to_string());
        }

        if command_lower.contains("sudo") {
            warnings.push("This command requires administrator privileges".to_string());
        }

        warnings
    }

    fn get_suggestions(&self, command: &str) -> Vec<String> {
        let mut suggestions = Vec::new();
        let command_lower = command.to_lowercase();

        if command_lower.contains("rm -rf") {
            suggestions.push("Consider using 'rm -i' for interactive deletion".to_string());
        }

        suggestions
    }
}

impl Default for SecurityManager {
    fn default() -> Self {
        Self::new()
    }
}

/// Security assessment result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAssessment {
    pub command: String,
    pub is_safe: bool,
    pub risk_level: RiskLevel,
    pub warnings: Vec<String>,
    pub suggestions: Vec<String>,
}

/// Risk level enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}
