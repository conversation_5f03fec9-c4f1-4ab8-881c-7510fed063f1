// TAgent - AI Enhanced Terminal Agent
// Main library module with Tauri integration

pub mod ai;
pub mod system;
pub mod terminal;
pub mod utils;

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;

// Import types and managers
use ai::{AIServiceManager, ChatManagerState, LegacyAIManager};
use terminal::commands::TerminalState;
use terminal::execution_commands::CommandExecutionState;

// Global state for the application
pub struct AppState {
    pub terminal_state: TerminalState,
    pub command_execution_state: CommandExecutionState,
    // Use the new AI service manager
    pub ai_service_manager: Arc<RwLock<AIServiceManager>>,
    // Keep the legacy AI manager for backward compatibility
    pub ai_manager: Arc<LegacyAIManager>,
    // Chat manager state
    pub chat_manager_state: ChatManagerState,
}

impl AppState {
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        // Initialize AI services
        let ai_service_manager = ai::initialize_ai_services().await?;
        let ai_manager = Arc::new(LegacyAIManager::new().await?);
        let chat_manager_state = Arc::new(RwLock::new(None));

        Ok(Self {
            terminal_state: TerminalState::new(),
            command_execution_state: CommandExecutionState::new(),
            ai_service_manager,
            ai_manager,
            chat_manager_state,
        })
    }

    // Legacy constructor for compatibility
    pub fn new_sync() -> Self {
        Self {
            terminal_state: TerminalState::new(),
            command_execution_state: CommandExecutionState::new(),
            ai_service_manager: Arc::new(RwLock::new(AIServiceManager::new(
                ai::AIConfig::default(),
            ))),
            ai_manager: Arc::new(
                // This will be initialized later
                tokio::runtime::Runtime::new()
                    .unwrap()
                    .block_on(LegacyAIManager::new())
                    .unwrap(),
            ),
            chat_manager_state: Arc::new(RwLock::new(None)),
        }
    }
}

// ============================================================================
// AI Commands - Re-export from ai::commands module
// ============================================================================

// Re-export new AI commands
pub use ai::{
    ai_chat, ai_chat_simple, ai_explain_command, ai_get_available_models, ai_health_check,
    ai_process_natural_language, ai_switch_model, ai_update_config, get_current_directory,
    get_environment_variables, get_shell_type,
};

// Re-export legacy AI commands for backward compatibility
pub use ai::{explain_command, list_ai_services, process_natural_language};

// Re-export chat commands
pub use ai::chat_commands::{
    ai_chat_with_context, clear_chat_history, end_chat_session, get_chat_history,
    send_chat_message, start_chat_session,
};

// ============================================================================
// Terminal Commands (re-export from terminal module)
// ============================================================================

// Re-export terminal commands
pub use terminal::commands::{
    create_terminal, is_terminal_alive, kill_terminal, list_terminals, resize_terminal,
    write_to_terminal,
};

// Re-export shell management commands
pub use terminal::shell_commands::{
    create_shell_instance, detect_default_shell, get_available_shells, get_shell_status,
    get_shells_health, init_shell_manager, is_shell_available, list_shell_instances,
    send_shell_input, setup_shell_events, terminate_shell_instance, ShellManagerState,
};

// Re-export advanced command execution commands
pub use terminal::execution_commands::{
    cleanup_terminal_executor, confirm_dangerous_command, execute_advanced_command,
    get_advanced_command_history, get_execution_context, get_pending_confirmations,
    search_advanced_command_history, update_working_directory,
};

// Re-export system commands
pub use system::{get_app_version, get_settings, get_system_info, update_settings};

// Re-export clipboard commands
pub use system::clipboard::{clear_clipboard, has_clipboard_text, read_clipboard, write_clipboard};

// Re-export shortcuts commands
pub use system::shortcuts::{
    clear_all_global_shortcuts, get_global_shortcuts, init_global_shortcuts,
    is_global_shortcut_registered, register_global_shortcut, set_global_shortcut_enabled,
    unregister_global_shortcut, update_global_shortcut, GlobalShortcutManagerStateType,
};

// ============================================================================
// Settings Data Types
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppSettings {
    pub terminal: TerminalSettings,
    pub ai: AISettings,
    pub appearance: AppearanceSettings,
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            terminal: TerminalSettings::default(),
            ai: AISettings::default(),
            appearance: AppearanceSettings::default(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalSettings {
    pub default_shell: String,
    pub font_size: u32,
    pub font_family: String,
}

impl Default for TerminalSettings {
    fn default() -> Self {
        Self {
            default_shell: if cfg!(windows) {
                "powershell".to_string()
            } else {
                "bash".to_string()
            },
            font_size: 14,
            font_family: "JetBrains Mono".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AISettings {
    pub default_provider: String,
    pub auto_suggest: bool,
    pub cache_enabled: bool,
    pub confirmation_required: bool,
}

impl Default for AISettings {
    fn default() -> Self {
        Self {
            default_provider: "mock".to_string(),
            auto_suggest: true,
            cache_enabled: true,
            confirmation_required: true,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppearanceSettings {
    pub theme: String,
    pub opacity: f32,
}

impl Default for AppearanceSettings {
    fn default() -> Self {
        Self {
            theme: "dark".to_string(),
            opacity: 1.0,
        }
    }
}

// ============================================================================
// System Data Types
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub platform: String,
    pub arch: String,
    pub version: String,
}

// ============================================================================
// Application Setup
// ============================================================================

pub async fn create_app_state() -> Result<AppState, Box<dyn std::error::Error>> {
    AppState::new().await
}

pub fn create_app_state_sync() -> AppState {
    AppState::new_sync()
}

pub async fn initialize_services(state: &AppState) -> Result<(), Box<dyn std::error::Error>> {
    log::info!("Initializing application services");

    // Test AI service health
    {
        let ai_manager = state.ai_service_manager.read().await;
        if let Err(e) = ai_manager.health_check().await {
            log::warn!("AI service health check failed: {}", e);
        } else {
            log::info!("AI services are healthy");
        }
    }

    log::info!("All services initialized successfully");
    Ok(())
}
