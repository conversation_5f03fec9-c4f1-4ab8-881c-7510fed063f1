# TAgent - Terminal Agent

<div align="center">

![TAgent Logo](https://via.placeholder.com/200x80/2563EB/FFFFFF?text=TAgent)

**AI 增强型命令行终端应用**

基于 Tauri + Rust + React 构建的现代化终端，集成 AI 能力，让命令行操作更智能、更易用。

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Tauri](https://img.shields.io/badge/Tauri-2.0+-blue.svg)](https://tauri.app/)
[![Rust](https://img.shields.io/badge/Rust-1.70+-orange.svg)](https://www.rust-lang.org/)
[![React](https://img.shields.io/badge/React-18+-61DAFB.svg)](https://reactjs.org/)

[功能特性](#功能特性) • [快速开始](#快速开始) • [文档](#项目文档) • [开发指南](#开发指南) • [贡献](#贡献)

</div>

## ✨ 功能特性

### 🖥️ 传统终端功能
- **多 Shell 支持**: 兼容 bash、zsh、fish、PowerShell 等主流 Shell
- **多 Tab 界面**: 支持同时管理多个终端会话
- **跨平台支持**: macOS、Windows、Linux 全平台兼容
- **现代化界面**: 简洁美观的用户界面，支持暗色/亮色主题

### 🤖 AI 增强功能
- **自然语言转命令**: 通过 `@command` 将自然语言描述转换为可执行命令
- **智能对话**: 通过 `@model` 与 AI 进行技术问题讨论
- **命令解释**: 通过 `@explain` 获取复杂命令的详细解释
- **安全确认**: 危险命令执行前的智能识别和用户确认

### 🎯 用户体验
- **快速启动**: 应用启动时间 < 2 秒
- **响应迅速**: 界面操作响应时间 < 100ms
- **快捷键支持**: 丰富的键盘快捷键操作
- **拖拽排序**: 支持 Tab 拖拽重新排序

## 🚀 快速开始

### 系统要求

- **Node.js**: 18.0 或更高版本
- **Rust**: 1.70 或更高版本
- **操作系统**: macOS 10.15+ / Windows 10+ / Linux (Ubuntu 20.04+)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/yourusername/TAgent.git
   cd TAgent
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发环境**
   ```bash
   npm run tauri dev
   ```

4. **构建应用**
   ```bash
   npm run tauri build
   ```

### 使用示例

#### 基础终端操作
```bash
# 普通命令执行
ls -la
cd ~/projects
git status
```

#### AI 增强功能
```bash
# 自然语言转命令
@command 查看当前目录下所有隐藏文件的大小
# → ls -lah

# AI 对话
@model 如何优化 Git 工作流程？
# → 返回详细的最佳实践建议

# 命令解释
@explain ps aux | grep python | awk '{print $2}' | xargs kill -9
# → 详细解释每个命令部分的作用
```

## 📚 项目文档

### 核心文档
- 📋 [产品需求文档 (PRD)](doc/prd.md) - 完整的功能规格和业务需求
- 📝 [项目任务计划](doc/tasks.md) - 详细的开发任务分解和进度跟踪
- 📁 [任务详情](doc/tasks/) - 每个开发任务的具体实现指南

### 技术文档
- 🏗️ [架构设计](doc/tasks/task_03_architecture_design.md) - 系统架构和技术选型
- 🖥️ [PTY 实现](doc/tasks/task_04_pty_implementation.md) - 终端核心功能实现
- 🔧 [开发环境](doc/tasks/task_02_dev_environment.md) - 开发工具和环境配置

## 🏗️ 项目架构

```
TAgent/
├── src-tauri/           # Rust 后端
│   ├── src/
│   │   ├── terminal/    # 终端核心模块
│   │   ├── ai/          # AI 功能模块
│   │   ├── system/      # 系统集成模块
│   │   └── utils/       # 工具模块
│   └── Cargo.toml
├── src/                 # React 前端
│   ├── components/      # UI 组件
│   ├── stores/          # 状态管理
│   ├── services/        # 业务服务
│   └── types/           # 类型定义
├── doc/                 # 项目文档
└── tests/               # 测试文件
```

## 🛠️ 开发指南

### 开发环境配置

1. **安装 Rust 工具链**
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   source ~/.cargo/env
   ```

2. **安装 Tauri CLI**
   ```bash
   cargo install tauri-cli
   ```

3. **配置开发工具**
   - 推荐使用 VS Code 并安装推荐扩展
   - 配置 ESLint 和 Prettier
   - 设置 Rust Analyzer

### 开发工作流

1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **开发和测试**
   ```bash
   npm run lint          # 代码检查
   npm run type-check    # 类型检查
   cargo test            # 运行 Rust 测试
   npm test              # 运行前端测试
   ```

3. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   git push origin feature/your-feature-name
   ```

### 代码规范

- **提交信息**: 遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范
- **代码格式**: 使用 Prettier (JavaScript) 和 rustfmt (Rust)
- **代码检查**: 通过 ESLint 和 Clippy 检查
- **测试覆盖**: 核心功能测试覆盖率 > 80%

## 🗺️ 开发路线图

### 🏁 MVP 版本 (v0.1.0) - 预计 Q1 2024
- [x] 项目基础设施搭建
- [ ] 基础终端功能实现
- [ ] 简单 AI 增强功能
- [ ] 暗色主题 UI
- [ ] macOS 平台支持

### 🚀 Beta 版本 (v0.5.0) - 预计 Q2 2024
- [ ] 多 Tab 终端界面
- [ ] 完整 AI 功能集成
- [ ] 跨平台支持 (Windows/Linux)
- [ ] 主题系统
- [ ] 插件框架基础

### 🎯 正式版本 (v1.0.0) - 预计 Q3 2024
- [ ] 性能优化和稳定性提升
- [ ] 完整插件生态
- [ ] 企业级功能
- [ ] 完善文档和社区支持

## 🤝 贡献

我们欢迎各种形式的贡献！无论是报告 bug、提出功能建议，还是提交代码，都是对项目的宝贵帮助。

### 贡献方式

1. **报告问题**: 在 [Issues](https://github.com/yourusername/TAgent/issues) 中报告 bug 或提出建议
2. **功能请求**: 提交新功能的想法和建议
3. **代码贡献**: 提交 Pull Request 来改进代码
4. **文档改进**: 帮助完善项目文档
5. **社区支持**: 在讨论区帮助其他用户

### 贡献流程

1. Fork 项目到您的 GitHub 账户
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 贡献者

感谢所有为 TAgent 项目做出贡献的开发者！

<a href="https://github.com/yourusername/TAgent/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=yourusername/TAgent" />
</a>

## 📈 项目状态

- **开发状态**: 🚧 积极开发中
- **版本**: v0.1.0-alpha
- **最后更新**: 2024-01-XX
- **测试覆盖率**: 目标 > 80%
- **文档完整度**: 75%

## 📄 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [Tauri](https://tauri.app/) - 提供优秀的桌面应用框架
- [React](https://reactjs.org/) - 强大的前端框架  
- [Rust](https://www.rust-lang.org/) - 高性能系统编程语言
- [portable-pty](https://github.com/wez/wezterm/tree/main/pty) - PTY 实现库
- 所有贡献者和社区成员的支持

## 📞 联系我们

- **项目主页**: [https://github.com/yourusername/TAgent](https://github.com/yourusername/TAgent)
- **问题反馈**: [GitHub Issues](https://github.com/yourusername/TAgent/issues)
- **讨论交流**: [GitHub Discussions](https://github.com/yourusername/TAgent/discussions)
- **邮箱**: <EMAIL>

---

<div align="center">

**如果 TAgent 对您有帮助，请给我们一个 ⭐ Star！**

Made with ❤️ by the TAgent Team

</div>
