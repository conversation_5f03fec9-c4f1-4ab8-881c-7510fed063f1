<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751426162222" clover="3.2.0">
  <project timestamp="1751426162222" name="All files">
    <metrics statements="3257" coveredstatements="2617" conditionals="254" coveredconditionals="185" methods="90" coveredmethods="49" elements="3601" coveredelements="2851" complexity="0" loc="3257" ncloc="3257" packages="9" files="20" classes="20"/>
    <package name="components.tabs">
      <metrics statements="342" coveredstatements="300" conditionals="74" coveredconditionals="44" methods="18" coveredmethods="11"/>
      <file name="NewTabButton.tsx" path="/Users/<USER>/coding/TAgent/src/components/tabs/NewTabButton.tsx">
        <metrics statements="29" coveredstatements="28" conditionals="2" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="10" count="8" type="stmt"/>
        <line num="11" count="8" type="stmt"/>
        <line num="12" count="8" type="stmt"/>
        <line num="13" count="8" type="stmt"/>
        <line num="14" count="8" type="stmt"/>
        <line num="15" count="8" type="stmt"/>
        <line num="16" count="8" type="stmt"/>
        <line num="17" count="8" type="stmt"/>
        <line num="18" count="8" type="stmt"/>
        <line num="19" count="8" type="cond" truecount="0" falsecount="1"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="8" type="stmt"/>
        <line num="22" count="8" type="stmt"/>
        <line num="23" count="8" type="stmt"/>
        <line num="24" count="8" type="stmt"/>
        <line num="25" count="8" type="stmt"/>
        <line num="26" count="8" type="stmt"/>
        <line num="27" count="8" type="stmt"/>
        <line num="28" count="8" type="stmt"/>
        <line num="29" count="8" type="stmt"/>
      </file>
      <file name="TabBar.tsx" path="/Users/<USER>/coding/TAgent/src/components/tabs/TabBar.tsx">
        <metrics statements="46" coveredstatements="45" conditionals="5" coveredconditionals="4" methods="3" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="9" count="8" type="stmt"/>
        <line num="10" count="8" type="stmt"/>
        <line num="11" count="8" type="stmt"/>
        <line num="12" count="8" type="stmt"/>
        <line num="13" count="8" type="stmt"/>
        <line num="14" count="8" type="stmt"/>
        <line num="15" count="8" type="stmt"/>
        <line num="16" count="8" type="stmt"/>
        <line num="17" count="8" type="stmt"/>
        <line num="18" count="8" type="stmt"/>
        <line num="19" count="8" type="stmt"/>
        <line num="20" count="8" type="stmt"/>
        <line num="21" count="8" type="stmt"/>
        <line num="22" count="8" type="cond" truecount="0" falsecount="1"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="8" type="stmt"/>
        <line num="25" count="8" type="stmt"/>
        <line num="26" count="8" type="stmt"/>
        <line num="27" count="8" type="stmt"/>
        <line num="28" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="29" count="9" type="stmt"/>
        <line num="30" count="9" type="stmt"/>
        <line num="31" count="9" type="stmt"/>
        <line num="32" count="9" type="stmt"/>
        <line num="33" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="34" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="35" count="9" type="stmt"/>
        <line num="36" count="9" type="stmt"/>
        <line num="37" count="8" type="stmt"/>
        <line num="38" count="8" type="stmt"/>
        <line num="39" count="8" type="stmt"/>
        <line num="40" count="8" type="stmt"/>
        <line num="41" count="8" type="stmt"/>
        <line num="42" count="8" type="stmt"/>
        <line num="43" count="8" type="stmt"/>
        <line num="44" count="8" type="stmt"/>
        <line num="45" count="8" type="stmt"/>
        <line num="46" count="8" type="stmt"/>
      </file>
      <file name="TabContent.tsx" path="/Users/<USER>/coding/TAgent/src/components/tabs/TabContent.tsx">
        <metrics statements="81" coveredstatements="74" conditionals="47" coveredconditionals="24" methods="3" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="12" count="7" type="stmt"/>
        <line num="13" count="7" type="stmt"/>
        <line num="14" count="7" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="7" type="stmt"/>
        <line num="19" count="7" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="7" type="stmt"/>
        <line num="24" count="7" type="stmt"/>
        <line num="25" count="7" type="stmt"/>
        <line num="26" count="7" type="stmt"/>
        <line num="27" count="7" type="stmt"/>
        <line num="28" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="29" count="7" type="stmt"/>
        <line num="30" count="7" type="stmt"/>
        <line num="31" count="7" type="stmt"/>
        <line num="32" count="7" type="stmt"/>
        <line num="33" count="7" type="stmt"/>
        <line num="34" count="7" type="stmt"/>
        <line num="35" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="36" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="7" type="stmt"/>
        <line num="39" count="7" type="stmt"/>
        <line num="40" count="7" type="stmt"/>
        <line num="41" count="7" type="stmt"/>
        <line num="42" count="7" type="stmt"/>
        <line num="43" count="7" type="stmt"/>
        <line num="44" count="7" type="stmt"/>
        <line num="45" count="7" type="stmt"/>
        <line num="46" count="7" type="stmt"/>
        <line num="47" count="7" type="stmt"/>
        <line num="48" count="7" type="stmt"/>
        <line num="49" count="7" type="stmt"/>
        <line num="50" count="7" type="stmt"/>
        <line num="51" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="52" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="53" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="54" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="55" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="56" count="7" type="stmt"/>
        <line num="57" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="58" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="59" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="60" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="61" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="62" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="63" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="64" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="65" count="7" type="stmt"/>
        <line num="66" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="67" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="68" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="69" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="70" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="71" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="72" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="73" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="74" count="7" type="stmt"/>
        <line num="75" count="7" type="stmt"/>
        <line num="76" count="7" type="stmt"/>
        <line num="77" count="7" type="stmt"/>
        <line num="78" count="7" type="stmt"/>
        <line num="79" count="7" type="stmt"/>
        <line num="80" count="7" type="stmt"/>
        <line num="81" count="7" type="stmt"/>
      </file>
      <file name="TabItem.tsx" path="/Users/<USER>/coding/TAgent/src/components/tabs/TabItem.tsx">
        <metrics statements="126" coveredstatements="93" conditionals="12" coveredconditionals="7" methods="7" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="15" count="9" type="stmt"/>
        <line num="16" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="18" count="9" type="stmt"/>
        <line num="19" count="9" type="stmt"/>
        <line num="20" count="9" type="stmt"/>
        <line num="21" count="9" type="stmt"/>
        <line num="22" count="9" type="stmt"/>
        <line num="23" count="9" type="stmt"/>
        <line num="24" count="9" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="26" count="9" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="9" type="stmt"/>
        <line num="31" count="9" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="9" type="stmt"/>
        <line num="38" count="9" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="9" type="stmt"/>
        <line num="47" count="9" type="stmt"/>
        <line num="48" count="9" type="stmt"/>
        <line num="49" count="9" type="stmt"/>
        <line num="50" count="9" type="stmt"/>
        <line num="51" count="9" type="stmt"/>
        <line num="52" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="53" count="6" type="stmt"/>
        <line num="54" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="55" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="56" count="3" type="stmt"/>
        <line num="57" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="9" type="stmt"/>
        <line num="60" count="9" type="stmt"/>
        <line num="61" count="9" type="stmt"/>
        <line num="62" count="9" type="stmt"/>
        <line num="63" count="9" type="stmt"/>
        <line num="64" count="9" type="stmt"/>
        <line num="65" count="9" type="stmt"/>
        <line num="66" count="9" type="stmt"/>
        <line num="67" count="9" type="stmt"/>
        <line num="68" count="9" type="stmt"/>
        <line num="69" count="9" type="stmt"/>
        <line num="70" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="71" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="72" count="3" type="stmt"/>
        <line num="73" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="9" type="stmt"/>
        <line num="76" count="9" type="stmt"/>
        <line num="77" count="9" type="stmt"/>
        <line num="78" count="9" type="stmt"/>
        <line num="79" count="9" type="stmt"/>
        <line num="80" count="9" type="cond" truecount="0" falsecount="1"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="9" type="stmt"/>
        <line num="96" count="9" type="stmt"/>
        <line num="97" count="9" type="stmt"/>
        <line num="98" count="9" type="stmt"/>
        <line num="99" count="9" type="stmt"/>
        <line num="100" count="9" type="stmt"/>
        <line num="101" count="9" type="stmt"/>
        <line num="102" count="9" type="stmt"/>
        <line num="103" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="104" count="6" type="stmt"/>
        <line num="105" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="6" type="stmt"/>
        <line num="110" count="6" type="stmt"/>
        <line num="111" count="6" type="stmt"/>
        <line num="112" count="6" type="stmt"/>
        <line num="113" count="6" type="stmt"/>
        <line num="114" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="6" type="stmt"/>
        <line num="117" count="6" type="stmt"/>
        <line num="118" count="6" type="stmt"/>
        <line num="119" count="6" type="stmt"/>
        <line num="120" count="6" type="stmt"/>
        <line num="121" count="6" type="stmt"/>
        <line num="122" count="6" type="stmt"/>
        <line num="123" count="9" type="stmt"/>
        <line num="124" count="9" type="stmt"/>
        <line num="125" count="9" type="stmt"/>
        <line num="126" count="9" type="stmt"/>
      </file>
      <file name="TabManager.tsx" path="/Users/<USER>/coding/TAgent/src/components/tabs/TabManager.tsx">
        <metrics statements="60" coveredstatements="60" conditionals="8" coveredconditionals="8" methods="4" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="9" type="stmt"/>
        <line num="14" count="9" type="stmt"/>
        <line num="15" count="9" type="stmt"/>
        <line num="16" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="18" count="9" type="stmt"/>
        <line num="19" count="9" type="stmt"/>
        <line num="20" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="21" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="22" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="24" count="9" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="26" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="9" type="stmt"/>
        <line num="30" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="9" type="stmt"/>
        <line num="36" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="9" type="stmt"/>
        <line num="41" count="9" type="stmt"/>
        <line num="42" count="9" type="stmt"/>
        <line num="43" count="9" type="stmt"/>
        <line num="44" count="9" type="stmt"/>
        <line num="45" count="9" type="stmt"/>
        <line num="46" count="9" type="stmt"/>
        <line num="47" count="9" type="stmt"/>
        <line num="48" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="49" count="9" type="stmt"/>
        <line num="50" count="9" type="stmt"/>
        <line num="51" count="9" type="stmt"/>
        <line num="52" count="9" type="stmt"/>
        <line num="53" count="9" type="stmt"/>
        <line num="54" count="9" type="stmt"/>
        <line num="55" count="9" type="stmt"/>
        <line num="56" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="57" count="9" type="stmt"/>
        <line num="58" count="9" type="stmt"/>
        <line num="59" count="9" type="stmt"/>
        <line num="60" count="9" type="stmt"/>
      </file>
    </package>
    <package name="components.terminal">
      <metrics statements="389" coveredstatements="272" conditionals="30" coveredconditionals="20" methods="4" coveredmethods="1"/>
      <file name="TerminalDisplay.tsx" path="/Users/<USER>/coding/TAgent/src/components/terminal/TerminalDisplay.tsx">
        <metrics statements="389" coveredstatements="272" conditionals="30" coveredconditionals="20" methods="4" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="15" count="18" type="stmt"/>
        <line num="16" count="18" type="stmt"/>
        <line num="17" count="18" type="stmt"/>
        <line num="18" count="18" type="stmt"/>
        <line num="19" count="18" type="stmt"/>
        <line num="20" count="18" type="stmt"/>
        <line num="21" count="18" type="stmt"/>
        <line num="22" count="18" type="stmt"/>
        <line num="23" count="18" type="stmt"/>
        <line num="24" count="18" type="stmt"/>
        <line num="25" count="18" type="stmt"/>
        <line num="26" count="18" type="stmt"/>
        <line num="27" count="18" type="stmt"/>
        <line num="28" count="18" type="stmt"/>
        <line num="29" count="18" type="stmt"/>
        <line num="30" count="18" type="stmt"/>
        <line num="31" count="18" type="stmt"/>
        <line num="32" count="18" type="stmt"/>
        <line num="33" count="18" type="stmt"/>
        <line num="34" count="18" type="stmt"/>
        <line num="35" count="18" type="stmt"/>
        <line num="36" count="18" type="stmt"/>
        <line num="37" count="18" type="stmt"/>
        <line num="38" count="18" type="stmt"/>
        <line num="39" count="18" type="stmt"/>
        <line num="40" count="18" type="stmt"/>
        <line num="41" count="18" type="stmt"/>
        <line num="42" count="18" type="stmt"/>
        <line num="43" count="18" type="stmt"/>
        <line num="44" count="18" type="stmt"/>
        <line num="45" count="18" type="stmt"/>
        <line num="46" count="18" type="stmt"/>
        <line num="47" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="48" count="17" type="stmt"/>
        <line num="49" count="17" type="stmt"/>
        <line num="50" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="51" count="11" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="11" type="stmt"/>
        <line num="56" count="11" type="stmt"/>
        <line num="57" count="17" type="stmt"/>
        <line num="58" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="59" count="17" type="stmt"/>
        <line num="60" count="17" type="stmt"/>
        <line num="61" count="18" type="stmt"/>
        <line num="62" count="18" type="stmt"/>
        <line num="63" count="18" type="stmt"/>
        <line num="64" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="65" count="6" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="67" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="68" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="6" type="stmt"/>
        <line num="74" count="18" type="stmt"/>
        <line num="75" count="18" type="stmt"/>
        <line num="76" count="18" type="stmt"/>
        <line num="77" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="78" count="6" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="18" type="stmt"/>
        <line num="82" count="18" type="stmt"/>
        <line num="83" count="18" type="stmt"/>
        <line num="84" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="85" count="11" type="stmt"/>
        <line num="86" count="11" type="stmt"/>
        <line num="87" count="11" type="stmt"/>
        <line num="88" count="18" type="stmt"/>
        <line num="89" count="18" type="stmt"/>
        <line num="90" count="18" type="stmt"/>
        <line num="91" count="18" type="stmt"/>
        <line num="92" count="18" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="18" type="stmt"/>
        <line num="108" count="18" type="stmt"/>
        <line num="109" count="18" type="stmt"/>
        <line num="110" count="18" type="stmt"/>
        <line num="111" count="18" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="18" type="stmt"/>
        <line num="116" count="18" type="stmt"/>
        <line num="117" count="18" type="stmt"/>
        <line num="118" count="18" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="18" type="stmt"/>
        <line num="137" count="18" type="stmt"/>
        <line num="138" count="18" type="stmt"/>
        <line num="139" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="140" count="6" type="stmt"/>
        <line num="141" count="6" type="stmt"/>
        <line num="142" count="6" type="stmt"/>
        <line num="143" count="6" type="stmt"/>
        <line num="144" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="145" count="6" type="stmt"/>
        <line num="146" count="6" type="stmt"/>
        <line num="147" count="18" type="stmt"/>
        <line num="148" count="18" type="stmt"/>
        <line num="149" count="18" type="stmt"/>
        <line num="150" count="18" type="stmt"/>
        <line num="151" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="152" count="5" type="stmt"/>
        <line num="153" count="5" type="stmt"/>
        <line num="154" count="5" type="stmt"/>
        <line num="155" count="5" type="stmt"/>
        <line num="156" count="5" type="stmt"/>
        <line num="157" count="5" type="stmt"/>
        <line num="158" count="5" type="stmt"/>
        <line num="159" count="5" type="stmt"/>
        <line num="160" count="5" type="stmt"/>
        <line num="161" count="5" type="stmt"/>
        <line num="162" count="5" type="stmt"/>
        <line num="163" count="5" type="stmt"/>
        <line num="164" count="5" type="stmt"/>
        <line num="165" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="5" type="stmt"/>
        <line num="169" count="5" type="stmt"/>
        <line num="170" count="5" type="stmt"/>
        <line num="171" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="5" type="stmt"/>
        <line num="177" count="5" type="stmt"/>
        <line num="178" count="5" type="stmt"/>
        <line num="179" count="5" type="stmt"/>
        <line num="180" count="5" type="stmt"/>
        <line num="181" count="5" type="stmt"/>
        <line num="182" count="5" type="stmt"/>
        <line num="183" count="5" type="stmt"/>
        <line num="184" count="18" type="stmt"/>
        <line num="185" count="18" type="stmt"/>
        <line num="186" count="18" type="stmt"/>
        <line num="187" count="18" type="stmt"/>
        <line num="188" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="189" count="17" type="cond" truecount="0" falsecount="1"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="17" type="stmt"/>
        <line num="234" count="18" type="stmt"/>
        <line num="235" count="18" type="stmt"/>
        <line num="236" count="18" type="stmt"/>
        <line num="237" count="18" type="stmt"/>
        <line num="238" count="18" type="stmt"/>
        <line num="239" count="18" type="stmt"/>
        <line num="240" count="18" type="stmt"/>
        <line num="241" count="18" type="stmt"/>
        <line num="242" count="18" type="stmt"/>
        <line num="243" count="18" type="stmt"/>
        <line num="244" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="245" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="246" count="6" type="stmt"/>
        <line num="247" count="6" type="stmt"/>
        <line num="248" count="6" type="stmt"/>
        <line num="249" count="6" type="stmt"/>
        <line num="250" count="6" type="stmt"/>
        <line num="251" count="6" type="stmt"/>
        <line num="252" count="6" type="stmt"/>
        <line num="253" count="6" type="stmt"/>
        <line num="254" count="6" type="stmt"/>
        <line num="255" count="6" type="stmt"/>
        <line num="256" count="6" type="stmt"/>
        <line num="257" count="6" type="stmt"/>
        <line num="258" count="6" type="stmt"/>
        <line num="259" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="260" count="11" type="stmt"/>
        <line num="261" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="262" count="5" type="stmt"/>
        <line num="263" count="5" type="stmt"/>
        <line num="264" count="5" type="stmt"/>
        <line num="265" count="5" type="stmt"/>
        <line num="266" count="5" type="stmt"/>
        <line num="267" count="5" type="stmt"/>
        <line num="268" count="5" type="stmt"/>
        <line num="269" count="5" type="stmt"/>
        <line num="270" count="5" type="stmt"/>
        <line num="271" count="5" type="stmt"/>
        <line num="272" count="5" type="stmt"/>
        <line num="273" count="5" type="stmt"/>
        <line num="274" count="5" type="stmt"/>
        <line num="275" count="5" type="stmt"/>
        <line num="276" count="5" type="stmt"/>
        <line num="277" count="5" type="stmt"/>
        <line num="278" count="5" type="stmt"/>
        <line num="279" count="5" type="stmt"/>
        <line num="280" count="5" type="stmt"/>
        <line num="281" count="5" type="stmt"/>
        <line num="282" count="5" type="stmt"/>
        <line num="283" count="5" type="stmt"/>
        <line num="284" count="5" type="stmt"/>
        <line num="285" count="5" type="stmt"/>
        <line num="286" count="5" type="stmt"/>
        <line num="287" count="5" type="stmt"/>
        <line num="288" count="5" type="stmt"/>
        <line num="289" count="5" type="stmt"/>
        <line num="290" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="291" count="6" type="stmt"/>
        <line num="292" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="293" count="6" type="stmt"/>
        <line num="294" count="6" type="stmt"/>
        <line num="295" count="6" type="stmt"/>
        <line num="296" count="6" type="stmt"/>
        <line num="297" count="6" type="stmt"/>
        <line num="298" count="6" type="stmt"/>
        <line num="299" count="6" type="stmt"/>
        <line num="300" count="6" type="stmt"/>
        <line num="301" count="6" type="stmt"/>
        <line num="302" count="6" type="stmt"/>
        <line num="303" count="6" type="stmt"/>
        <line num="304" count="6" type="stmt"/>
        <line num="305" count="6" type="stmt"/>
        <line num="306" count="6" type="stmt"/>
        <line num="307" count="6" type="stmt"/>
        <line num="308" count="6" type="stmt"/>
        <line num="309" count="6" type="stmt"/>
        <line num="310" count="6" type="stmt"/>
        <line num="311" count="6" type="stmt"/>
        <line num="312" count="6" type="stmt"/>
        <line num="313" count="6" type="stmt"/>
        <line num="314" count="6" type="stmt"/>
        <line num="315" count="6" type="stmt"/>
        <line num="316" count="6" type="stmt"/>
        <line num="317" count="6" type="stmt"/>
        <line num="318" count="6" type="stmt"/>
        <line num="319" count="6" type="stmt"/>
        <line num="320" count="6" type="stmt"/>
        <line num="321" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="18" type="stmt"/>
        <line num="325" count="18" type="stmt"/>
        <line num="326" count="18" type="stmt"/>
        <line num="327" count="18" type="stmt"/>
        <line num="328" count="18" type="stmt"/>
        <line num="329" count="18" type="stmt"/>
        <line num="330" count="18" type="stmt"/>
        <line num="331" count="18" type="stmt"/>
        <line num="332" count="18" type="stmt"/>
        <line num="333" count="18" type="stmt"/>
        <line num="334" count="18" type="stmt"/>
        <line num="335" count="18" type="stmt"/>
        <line num="336" count="18" type="stmt"/>
        <line num="337" count="18" type="stmt"/>
        <line num="338" count="18" type="stmt"/>
        <line num="339" count="18" type="stmt"/>
        <line num="340" count="18" type="stmt"/>
        <line num="341" count="18" type="stmt"/>
        <line num="342" count="18" type="stmt"/>
        <line num="343" count="18" type="stmt"/>
        <line num="344" count="18" type="stmt"/>
        <line num="345" count="18" type="stmt"/>
        <line num="346" count="18" type="stmt"/>
        <line num="347" count="18" type="stmt"/>
        <line num="348" count="18" type="stmt"/>
        <line num="349" count="18" type="stmt"/>
        <line num="350" count="18" type="stmt"/>
        <line num="351" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="352" count="18" type="stmt"/>
        <line num="353" count="18" type="stmt"/>
        <line num="354" count="18" type="stmt"/>
        <line num="355" count="18" type="stmt"/>
        <line num="356" count="18" type="stmt"/>
        <line num="357" count="18" type="cond" truecount="0" falsecount="1"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="18" type="stmt"/>
        <line num="371" count="18" type="stmt"/>
        <line num="372" count="18" type="stmt"/>
        <line num="373" count="18" type="cond" truecount="0" falsecount="1"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="18" type="stmt"/>
        <line num="387" count="18" type="stmt"/>
        <line num="388" count="18" type="stmt"/>
        <line num="389" count="18" type="stmt"/>
      </file>
    </package>
    <package name="components.ui">
      <metrics statements="596" coveredstatements="469" conditionals="44" coveredconditionals="32" methods="25" coveredmethods="11"/>
      <file name="Button.tsx" path="/Users/<USER>/coding/TAgent/src/components/ui/Button.tsx">
        <metrics statements="90" coveredstatements="90" conditionals="5" coveredconditionals="5" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="18" count="179" type="stmt"/>
        <line num="19" count="179" type="stmt"/>
        <line num="20" count="179" type="stmt"/>
        <line num="21" count="179" type="stmt"/>
        <line num="22" count="179" type="stmt"/>
        <line num="23" count="179" type="stmt"/>
        <line num="24" count="179" type="stmt"/>
        <line num="25" count="179" type="stmt"/>
        <line num="26" count="179" type="cond" truecount="1" falsecount="0"/>
        <line num="27" count="179" type="stmt"/>
        <line num="28" count="179" type="stmt"/>
        <line num="29" count="179" type="stmt"/>
        <line num="30" count="179" type="stmt"/>
        <line num="31" count="179" type="stmt"/>
        <line num="32" count="179" type="stmt"/>
        <line num="33" count="179" type="stmt"/>
        <line num="34" count="179" type="stmt"/>
        <line num="35" count="179" type="stmt"/>
        <line num="36" count="179" type="stmt"/>
        <line num="37" count="179" type="stmt"/>
        <line num="38" count="179" type="stmt"/>
        <line num="39" count="179" type="stmt"/>
        <line num="40" count="179" type="stmt"/>
        <line num="41" count="179" type="stmt"/>
        <line num="42" count="179" type="stmt"/>
        <line num="43" count="179" type="stmt"/>
        <line num="44" count="179" type="stmt"/>
        <line num="45" count="179" type="stmt"/>
        <line num="46" count="179" type="stmt"/>
        <line num="47" count="179" type="stmt"/>
        <line num="48" count="179" type="stmt"/>
        <line num="49" count="179" type="stmt"/>
        <line num="50" count="179" type="stmt"/>
        <line num="51" count="179" type="stmt"/>
        <line num="52" count="179" type="stmt"/>
        <line num="53" count="179" type="stmt"/>
        <line num="54" count="179" type="stmt"/>
        <line num="55" count="179" type="stmt"/>
        <line num="56" count="179" type="stmt"/>
        <line num="57" count="179" type="stmt"/>
        <line num="58" count="179" type="stmt"/>
        <line num="59" count="179" type="stmt"/>
        <line num="60" count="179" type="cond" truecount="1" falsecount="0"/>
        <line num="61" count="6" type="stmt"/>
        <line num="62" count="6" type="stmt"/>
        <line num="63" count="6" type="stmt"/>
        <line num="64" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="65" count="6" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="67" count="6" type="stmt"/>
        <line num="68" count="6" type="stmt"/>
        <line num="69" count="6" type="stmt"/>
        <line num="70" count="6" type="stmt"/>
        <line num="71" count="6" type="stmt"/>
        <line num="72" count="6" type="stmt"/>
        <line num="73" count="6" type="stmt"/>
        <line num="74" count="6" type="stmt"/>
        <line num="75" count="6" type="stmt"/>
        <line num="76" count="6" type="stmt"/>
        <line num="77" count="6" type="stmt"/>
        <line num="78" count="6" type="stmt"/>
        <line num="79" count="6" type="stmt"/>
        <line num="80" count="6" type="stmt"/>
        <line num="81" count="6" type="stmt"/>
        <line num="82" count="6" type="stmt"/>
        <line num="83" count="6" type="stmt"/>
        <line num="84" count="179" type="stmt"/>
        <line num="85" count="179" type="stmt"/>
        <line num="86" count="179" type="stmt"/>
        <line num="87" count="179" type="stmt"/>
        <line num="88" count="179" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
      </file>
      <file name="ContextMenu.tsx" path="/Users/<USER>/coding/TAgent/src/components/ui/ContextMenu.tsx">
        <metrics statements="142" coveredstatements="134" conditionals="25" coveredconditionals="20" methods="5" coveredmethods="5"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="27" count="11" type="stmt"/>
        <line num="28" count="11" type="stmt"/>
        <line num="29" count="11" type="stmt"/>
        <line num="30" count="11" type="stmt"/>
        <line num="31" count="11" type="stmt"/>
        <line num="32" count="11" type="stmt"/>
        <line num="33" count="11" type="stmt"/>
        <line num="34" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="35" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="10" type="stmt"/>
        <line num="41" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="10" type="stmt"/>
        <line num="47" count="10" type="stmt"/>
        <line num="48" count="10" type="stmt"/>
        <line num="49" count="10" type="stmt"/>
        <line num="50" count="10" type="stmt"/>
        <line num="51" count="10" type="stmt"/>
        <line num="52" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="53" count="10" type="stmt"/>
        <line num="54" count="10" type="stmt"/>
        <line num="55" count="10" type="stmt"/>
        <line num="56" count="11" type="stmt"/>
        <line num="57" count="11" type="stmt"/>
        <line num="58" count="11" type="stmt"/>
        <line num="59" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="60" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="11" type="stmt"/>
        <line num="90" count="11" type="stmt"/>
        <line num="91" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="92" count="11" type="stmt"/>
        <line num="93" count="11" type="stmt"/>
        <line num="94" count="11" type="stmt"/>
        <line num="95" count="11" type="stmt"/>
        <line num="96" count="11" type="stmt"/>
        <line num="97" count="11" type="stmt"/>
        <line num="98" count="11" type="stmt"/>
        <line num="99" count="11" type="stmt"/>
        <line num="100" count="11" type="stmt"/>
        <line num="101" count="11" type="stmt"/>
        <line num="102" count="11" type="stmt"/>
        <line num="103" count="11" type="stmt"/>
        <line num="104" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="105" count="42" type="stmt"/>
        <line num="106" count="42" type="cond" truecount="1" falsecount="0"/>
        <line num="107" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="108" count="33" type="stmt"/>
        <line num="109" count="33" type="stmt"/>
        <line num="110" count="33" type="stmt"/>
        <line num="111" count="33" type="stmt"/>
        <line num="112" count="33" type="stmt"/>
        <line num="113" count="33" type="cond" truecount="2" falsecount="0"/>
        <line num="114" count="33" type="stmt"/>
        <line num="115" count="33" type="stmt"/>
        <line num="116" count="33" type="cond" truecount="1" falsecount="0"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="33" type="stmt"/>
        <line num="123" count="33" type="stmt"/>
        <line num="124" count="33" type="stmt"/>
        <line num="125" count="33" type="cond" truecount="1" falsecount="0"/>
        <line num="126" count="33" type="stmt"/>
        <line num="127" count="33" type="stmt"/>
        <line num="128" count="33" type="stmt"/>
        <line num="129" count="33" type="stmt"/>
        <line num="130" count="33" type="cond" truecount="1" falsecount="0"/>
        <line num="131" count="15" type="stmt"/>
        <line num="132" count="15" type="stmt"/>
        <line num="133" count="15" type="stmt"/>
        <line num="134" count="33" type="stmt"/>
        <line num="135" count="33" type="stmt"/>
        <line num="136" count="42" type="stmt"/>
        <line num="137" count="42" type="stmt"/>
        <line num="138" count="11" type="stmt"/>
        <line num="139" count="11" type="stmt"/>
        <line num="140" count="11" type="stmt"/>
        <line num="141" count="11" type="stmt"/>
        <line num="142" count="11" type="stmt"/>
      </file>
      <file name="ContextMenuManager.tsx" path="/Users/<USER>/coding/TAgent/src/components/ui/ContextMenuManager.tsx">
        <metrics statements="45" coveredstatements="20" conditionals="5" coveredconditionals="1" methods="2" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
      </file>
      <file name="TabContextMenu.tsx" path="/Users/<USER>/coding/TAgent/src/components/ui/TabContextMenu.tsx">
        <metrics statements="114" coveredstatements="91" conditionals="1" coveredconditionals="1" methods="7" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="16" count="3" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="18" count="3" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="3" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="23" count="3" type="stmt"/>
        <line num="24" count="3" type="stmt"/>
        <line num="25" count="3" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="3" type="stmt"/>
        <line num="36" count="3" type="stmt"/>
        <line num="37" count="3" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="3" type="stmt"/>
        <line num="44" count="3" type="stmt"/>
        <line num="45" count="3" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="3" type="stmt"/>
        <line num="51" count="3" type="stmt"/>
        <line num="52" count="3" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="3" type="stmt"/>
        <line num="57" count="3" type="stmt"/>
        <line num="58" count="3" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="3" type="stmt"/>
        <line num="63" count="3" type="stmt"/>
        <line num="64" count="3" type="stmt"/>
        <line num="65" count="3" type="stmt"/>
        <line num="66" count="3" type="stmt"/>
        <line num="67" count="3" type="stmt"/>
        <line num="68" count="3" type="stmt"/>
        <line num="69" count="3" type="stmt"/>
        <line num="70" count="3" type="stmt"/>
        <line num="71" count="3" type="stmt"/>
        <line num="72" count="3" type="stmt"/>
        <line num="73" count="3" type="stmt"/>
        <line num="74" count="3" type="stmt"/>
        <line num="75" count="3" type="stmt"/>
        <line num="76" count="3" type="stmt"/>
        <line num="77" count="3" type="stmt"/>
        <line num="78" count="3" type="stmt"/>
        <line num="79" count="3" type="stmt"/>
        <line num="80" count="3" type="stmt"/>
        <line num="81" count="3" type="stmt"/>
        <line num="82" count="3" type="stmt"/>
        <line num="83" count="3" type="stmt"/>
        <line num="84" count="3" type="stmt"/>
        <line num="85" count="3" type="stmt"/>
        <line num="86" count="3" type="stmt"/>
        <line num="87" count="3" type="stmt"/>
        <line num="88" count="3" type="stmt"/>
        <line num="89" count="3" type="stmt"/>
        <line num="90" count="3" type="stmt"/>
        <line num="91" count="3" type="stmt"/>
        <line num="92" count="3" type="stmt"/>
        <line num="93" count="3" type="stmt"/>
        <line num="94" count="3" type="stmt"/>
        <line num="95" count="3" type="stmt"/>
        <line num="96" count="3" type="stmt"/>
        <line num="97" count="3" type="stmt"/>
        <line num="98" count="3" type="stmt"/>
        <line num="99" count="3" type="stmt"/>
        <line num="100" count="3" type="stmt"/>
        <line num="101" count="3" type="stmt"/>
        <line num="102" count="3" type="stmt"/>
        <line num="103" count="3" type="stmt"/>
        <line num="104" count="3" type="stmt"/>
        <line num="105" count="3" type="stmt"/>
        <line num="106" count="3" type="stmt"/>
        <line num="107" count="3" type="stmt"/>
        <line num="108" count="3" type="stmt"/>
        <line num="109" count="3" type="stmt"/>
        <line num="110" count="3" type="stmt"/>
        <line num="111" count="3" type="stmt"/>
        <line num="112" count="3" type="stmt"/>
        <line num="113" count="3" type="stmt"/>
        <line num="114" count="3" type="stmt"/>
      </file>
      <file name="TerminalContextMenu.tsx" path="/Users/<USER>/coding/TAgent/src/components/ui/TerminalContextMenu.tsx">
        <metrics statements="140" coveredstatements="89" conditionals="1" coveredconditionals="1" methods="7" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="16" count="3" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="18" count="3" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="3" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="23" count="3" type="stmt"/>
        <line num="24" count="3" type="stmt"/>
        <line num="25" count="3" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="3" type="stmt"/>
        <line num="40" count="3" type="stmt"/>
        <line num="41" count="3" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="3" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
        <line num="56" count="3" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="3" type="stmt"/>
        <line num="72" count="3" type="stmt"/>
        <line num="73" count="3" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="3" type="stmt"/>
        <line num="87" count="3" type="stmt"/>
        <line num="88" count="3" type="stmt"/>
        <line num="89" count="3" type="stmt"/>
        <line num="90" count="3" type="stmt"/>
        <line num="91" count="3" type="stmt"/>
        <line num="92" count="3" type="stmt"/>
        <line num="93" count="3" type="stmt"/>
        <line num="94" count="3" type="stmt"/>
        <line num="95" count="3" type="stmt"/>
        <line num="96" count="3" type="stmt"/>
        <line num="97" count="3" type="stmt"/>
        <line num="98" count="3" type="stmt"/>
        <line num="99" count="3" type="stmt"/>
        <line num="100" count="3" type="stmt"/>
        <line num="101" count="3" type="stmt"/>
        <line num="102" count="3" type="stmt"/>
        <line num="103" count="3" type="stmt"/>
        <line num="104" count="3" type="stmt"/>
        <line num="105" count="3" type="stmt"/>
        <line num="106" count="3" type="stmt"/>
        <line num="107" count="3" type="stmt"/>
        <line num="108" count="3" type="stmt"/>
        <line num="109" count="3" type="stmt"/>
        <line num="110" count="3" type="stmt"/>
        <line num="111" count="3" type="stmt"/>
        <line num="112" count="3" type="stmt"/>
        <line num="113" count="3" type="stmt"/>
        <line num="114" count="3" type="stmt"/>
        <line num="115" count="3" type="stmt"/>
        <line num="116" count="3" type="stmt"/>
        <line num="117" count="3" type="stmt"/>
        <line num="118" count="3" type="stmt"/>
        <line num="119" count="3" type="stmt"/>
        <line num="120" count="3" type="stmt"/>
        <line num="121" count="3" type="stmt"/>
        <line num="122" count="3" type="stmt"/>
        <line num="123" count="3" type="stmt"/>
        <line num="124" count="3" type="stmt"/>
        <line num="125" count="3" type="stmt"/>
        <line num="126" count="3" type="stmt"/>
        <line num="127" count="3" type="stmt"/>
        <line num="128" count="3" type="stmt"/>
        <line num="129" count="3" type="stmt"/>
        <line num="130" count="3" type="stmt"/>
        <line num="131" count="3" type="stmt"/>
        <line num="132" count="3" type="stmt"/>
        <line num="133" count="3" type="stmt"/>
        <line num="134" count="3" type="stmt"/>
        <line num="135" count="3" type="stmt"/>
        <line num="136" count="3" type="stmt"/>
        <line num="137" count="3" type="stmt"/>
        <line num="138" count="3" type="stmt"/>
        <line num="139" count="3" type="stmt"/>
        <line num="140" count="3" type="stmt"/>
      </file>
      <file name="ThemeProvider.tsx" path="/Users/<USER>/coding/TAgent/src/components/ui/ThemeProvider.tsx">
        <metrics statements="65" coveredstatements="45" conditionals="7" coveredconditionals="4" methods="3" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="9" count="69" type="stmt"/>
        <line num="10" count="69" type="stmt"/>
        <line num="11" count="69" type="stmt"/>
        <line num="12" count="69" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="34" type="stmt"/>
        <line num="14" count="34" type="stmt"/>
        <line num="15" count="34" type="stmt"/>
        <line num="16" count="34" type="stmt"/>
        <line num="17" count="34" type="stmt"/>
        <line num="18" count="34" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="34" type="stmt"/>
        <line num="24" count="34" type="stmt"/>
        <line num="25" count="34" type="stmt"/>
        <line num="26" count="34" type="stmt"/>
        <line num="27" count="34" type="cond" truecount="1" falsecount="0"/>
        <line num="28" count="44" type="stmt"/>
        <line num="29" count="44" type="cond" truecount="0" falsecount="1"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="44" type="cond" truecount="0" falsecount="1"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="44" type="stmt"/>
        <line num="48" count="34" type="stmt"/>
        <line num="49" count="34" type="stmt"/>
        <line num="50" count="34" type="stmt"/>
        <line num="51" count="34" type="cond" truecount="1" falsecount="0"/>
        <line num="52" count="34" type="stmt"/>
        <line num="53" count="34" type="stmt"/>
        <line num="54" count="34" type="stmt"/>
        <line num="55" count="69" type="stmt"/>
        <line num="56" count="69" type="stmt"/>
        <line num="57" count="69" type="stmt"/>
        <line num="58" count="69" type="stmt"/>
        <line num="59" count="69" type="stmt"/>
        <line num="60" count="69" type="stmt"/>
        <line num="61" count="69" type="stmt"/>
        <line num="62" count="69" type="stmt"/>
        <line num="63" count="69" type="stmt"/>
        <line num="64" count="69" type="stmt"/>
        <line num="65" count="69" type="stmt"/>
      </file>
    </package>
    <package name="hooks">
      <metrics statements="338" coveredstatements="177" conditionals="12" coveredconditionals="6" methods="1" coveredmethods="1"/>
      <file name="useRealTerminal.ts" path="/Users/<USER>/coding/TAgent/src/hooks/useRealTerminal.ts">
        <metrics statements="338" coveredstatements="177" conditionals="12" coveredconditionals="6" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="47" count="17" type="stmt"/>
        <line num="48" count="17" type="stmt"/>
        <line num="49" count="17" type="stmt"/>
        <line num="50" count="17" type="stmt"/>
        <line num="51" count="17" type="stmt"/>
        <line num="52" count="17" type="stmt"/>
        <line num="53" count="17" type="stmt"/>
        <line num="54" count="17" type="stmt"/>
        <line num="55" count="17" type="stmt"/>
        <line num="56" count="17" type="stmt"/>
        <line num="57" count="17" type="stmt"/>
        <line num="58" count="17" type="stmt"/>
        <line num="59" count="17" type="stmt"/>
        <line num="60" count="17" type="stmt"/>
        <line num="61" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="62" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="63" count="5" type="stmt"/>
        <line num="64" count="5" type="stmt"/>
        <line num="65" count="5" type="stmt"/>
        <line num="66" count="5" type="stmt"/>
        <line num="67" count="5" type="stmt"/>
        <line num="68" count="5" type="stmt"/>
        <line num="69" count="11" type="stmt"/>
        <line num="70" count="11" type="stmt"/>
        <line num="71" count="17" type="stmt"/>
        <line num="72" count="17" type="stmt"/>
        <line num="73" count="17" type="stmt"/>
        <line num="74" count="17" type="stmt"/>
        <line num="75" count="17" type="stmt"/>
        <line num="76" count="17" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="17" type="stmt"/>
        <line num="87" count="17" type="stmt"/>
        <line num="88" count="17" type="stmt"/>
        <line num="89" count="17" type="stmt"/>
        <line num="90" count="17" type="stmt"/>
        <line num="91" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="92" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="93" count="11" type="stmt"/>
        <line num="94" count="11" type="stmt"/>
        <line num="95" count="11" type="stmt"/>
        <line num="96" count="11" type="stmt"/>
        <line num="97" count="11" type="stmt"/>
        <line num="98" count="11" type="stmt"/>
        <line num="99" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="100" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="101" count="11" type="stmt"/>
        <line num="102" count="11" type="stmt"/>
        <line num="103" count="11" type="stmt"/>
        <line num="104" count="11" type="stmt"/>
        <line num="105" count="11" type="stmt"/>
        <line num="106" count="11" type="stmt"/>
        <line num="107" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="11" type="stmt"/>
        <line num="125" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="126" count="11" type="stmt"/>
        <line num="127" count="11" type="stmt"/>
        <line num="128" count="11" type="stmt"/>
        <line num="129" count="11" type="stmt"/>
        <line num="130" count="11" type="stmt"/>
        <line num="131" count="11" type="stmt"/>
        <line num="132" count="11" type="stmt"/>
        <line num="133" count="17" type="stmt"/>
        <line num="134" count="17" type="stmt"/>
        <line num="135" count="17" type="stmt"/>
        <line num="136" count="17" type="stmt"/>
        <line num="137" count="17" type="stmt"/>
        <line num="138" count="17" type="stmt"/>
        <line num="139" count="17" type="stmt"/>
        <line num="140" count="17" type="stmt"/>
        <line num="141" count="17" type="stmt"/>
        <line num="142" count="17" type="stmt"/>
        <line num="143" count="17" type="stmt"/>
        <line num="144" count="17" type="stmt"/>
        <line num="145" count="17" type="stmt"/>
        <line num="146" count="17" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="17" type="stmt"/>
        <line num="168" count="17" type="stmt"/>
        <line num="169" count="17" type="stmt"/>
        <line num="170" count="17" type="stmt"/>
        <line num="171" count="17" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="17" type="stmt"/>
        <line num="188" count="17" type="stmt"/>
        <line num="189" count="17" type="stmt"/>
        <line num="190" count="17" type="stmt"/>
        <line num="191" count="17" type="stmt"/>
        <line num="192" count="17" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="17" type="stmt"/>
        <line num="233" count="17" type="stmt"/>
        <line num="234" count="17" type="stmt"/>
        <line num="235" count="17" type="stmt"/>
        <line num="236" count="17" type="stmt"/>
        <line num="237" count="17" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="17" type="stmt"/>
        <line num="248" count="17" type="stmt"/>
        <line num="249" count="17" type="stmt"/>
        <line num="250" count="17" type="stmt"/>
        <line num="251" count="17" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="17" type="stmt"/>
        <line num="254" count="17" type="stmt"/>
        <line num="255" count="17" type="stmt"/>
        <line num="256" count="17" type="stmt"/>
        <line num="257" count="17" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="17" type="stmt"/>
        <line num="309" count="17" type="stmt"/>
        <line num="310" count="17" type="stmt"/>
        <line num="311" count="17" type="stmt"/>
        <line num="312" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="313" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="314" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="6" type="stmt"/>
        <line num="318" count="17" type="stmt"/>
        <line num="319" count="17" type="stmt"/>
        <line num="320" count="17" type="stmt"/>
        <line num="321" count="17" type="stmt"/>
        <line num="322" count="17" type="stmt"/>
        <line num="323" count="17" type="stmt"/>
        <line num="324" count="17" type="stmt"/>
        <line num="325" count="17" type="stmt"/>
        <line num="326" count="17" type="stmt"/>
        <line num="327" count="17" type="stmt"/>
        <line num="328" count="17" type="stmt"/>
        <line num="329" count="17" type="stmt"/>
        <line num="330" count="17" type="stmt"/>
        <line num="331" count="17" type="stmt"/>
        <line num="332" count="17" type="stmt"/>
        <line num="333" count="17" type="stmt"/>
        <line num="334" count="17" type="stmt"/>
        <line num="335" count="17" type="stmt"/>
        <line num="336" count="17" type="stmt"/>
        <line num="337" count="17" type="stmt"/>
        <line num="338" count="17" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="226" coveredstatements="112" conditionals="6" coveredconditionals="3" methods="12" coveredmethods="3"/>
      <file name="terminalService.ts" path="/Users/<USER>/coding/TAgent/src/services/terminalService.ts">
        <metrics statements="226" coveredstatements="112" conditionals="6" coveredconditionals="3" methods="12" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="37" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="38" count="11" type="stmt"/>
        <line num="39" count="11" type="stmt"/>
        <line num="40" count="11" type="stmt"/>
        <line num="41" count="11" type="stmt"/>
        <line num="42" count="11" type="stmt"/>
        <line num="43" count="11" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="11" type="stmt"/>
        <line num="59" count="11" type="stmt"/>
        <line num="60" count="11" type="stmt"/>
        <line num="61" count="11" type="stmt"/>
        <line num="62" count="11" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="81" count="11" type="stmt"/>
        <line num="82" count="11" type="stmt"/>
        <line num="83" count="11" type="stmt"/>
        <line num="84" count="11" type="cond" truecount="0" falsecount="1"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="11" type="stmt"/>
        <line num="92" count="11" type="stmt"/>
        <line num="93" count="11" type="stmt"/>
        <line num="94" count="11" type="stmt"/>
        <line num="95" count="11" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
      </file>
    </package>
    <package name="stores">
      <metrics statements="190" coveredstatements="135" conditionals="24" coveredconditionals="21" methods="12" coveredmethods="8"/>
      <file name="themeStore.ts" path="/Users/<USER>/coding/TAgent/src/stores/themeStore.ts">
        <metrics statements="190" coveredstatements="135" conditionals="24" coveredconditionals="21" methods="12" coveredmethods="8"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="16" count="3" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="18" count="3" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="3" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="23" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="24" count="23" type="stmt"/>
        <line num="25" count="23" type="cond" truecount="1" falsecount="0"/>
        <line num="26" count="23" type="stmt"/>
        <line num="27" count="23" type="cond" truecount="1" falsecount="0"/>
        <line num="28" count="21" type="stmt"/>
        <line num="29" count="21" type="stmt"/>
        <line num="30" count="21" type="stmt"/>
        <line num="31" count="21" type="stmt"/>
        <line num="32" count="21" type="stmt"/>
        <line num="33" count="21" type="stmt"/>
        <line num="34" count="21" type="stmt"/>
        <line num="35" count="21" type="stmt"/>
        <line num="36" count="21" type="stmt"/>
        <line num="37" count="21" type="stmt"/>
        <line num="38" count="23" type="stmt"/>
        <line num="39" count="3" type="stmt"/>
        <line num="40" count="3" type="stmt"/>
        <line num="41" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="42" count="7" type="stmt"/>
        <line num="43" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="44" count="7" type="stmt"/>
        <line num="45" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="46" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="47" count="6" type="stmt"/>
        <line num="48" count="6" type="stmt"/>
        <line num="49" count="6" type="stmt"/>
        <line num="50" count="7" type="stmt"/>
        <line num="51" count="3" type="stmt"/>
        <line num="52" count="3" type="stmt"/>
        <line num="53" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="54" count="4" type="stmt"/>
        <line num="55" count="4" type="stmt"/>
        <line num="56" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="57" count="4" type="stmt"/>
        <line num="58" count="4" type="stmt"/>
        <line num="59" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="60" count="4" type="stmt"/>
        <line num="61" count="4" type="stmt"/>
        <line num="62" count="4" type="stmt"/>
        <line num="63" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="64" count="2" type="stmt"/>
        <line num="65" count="2" type="stmt"/>
        <line num="66" count="4" type="stmt"/>
        <line num="67" count="4" type="stmt"/>
        <line num="68" count="3" type="stmt"/>
        <line num="69" count="3" type="stmt"/>
        <line num="70" count="3" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="3" type="stmt"/>
        <line num="89" count="3" type="stmt"/>
        <line num="90" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="91" count="40" type="stmt"/>
        <line num="92" count="40" type="stmt"/>
        <line num="93" count="40" type="stmt"/>
        <line num="94" count="40" type="stmt"/>
        <line num="95" count="40" type="stmt"/>
        <line num="96" count="40" type="cond" truecount="1" falsecount="0"/>
        <line num="97" count="8" type="stmt"/>
        <line num="98" count="8" type="cond" truecount="0" falsecount="1"/>
        <line num="99" count="8" type="stmt"/>
        <line num="100" count="8" type="stmt"/>
        <line num="101" count="40" type="stmt"/>
        <line num="102" count="3" type="stmt"/>
        <line num="103" count="3" type="stmt"/>
        <line num="104" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="105" count="7" type="stmt"/>
        <line num="106" count="7" type="stmt"/>
        <line num="107" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="108" count="5" type="stmt"/>
        <line num="109" count="5" type="stmt"/>
        <line num="110" count="5" type="stmt"/>
        <line num="111" count="7" type="stmt"/>
        <line num="112" count="3" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="117" count="102" type="stmt"/>
        <line num="118" count="102" type="stmt"/>
        <line num="119" count="102" type="cond" truecount="1" falsecount="0"/>
        <line num="120" count="282" type="stmt"/>
        <line num="121" count="282" type="cond" truecount="1" falsecount="0"/>
        <line num="122" count="102" type="stmt"/>
        <line num="123" count="102" type="stmt"/>
        <line num="124" count="102" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="128" count="3" type="stmt"/>
        <line num="129" count="3" type="stmt"/>
        <line num="130" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="131" count="3" type="stmt"/>
        <line num="132" count="3" type="stmt"/>
        <line num="133" count="3" type="stmt"/>
        <line num="134" count="3" type="stmt"/>
        <line num="135" count="3" type="stmt"/>
        <line num="136" count="3" type="stmt"/>
        <line num="137" count="3" type="stmt"/>
        <line num="138" count="3" type="stmt"/>
        <line num="139" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="3" type="stmt"/>
        <line num="143" count="3" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
      </file>
    </package>
    <package name="themes">
      <metrics statements="320" coveredstatements="308" conditionals="11" coveredconditionals="9" methods="4" coveredmethods="3"/>
      <file name="index.ts" path="/Users/<USER>/coding/TAgent/src/themes/index.ts">
        <metrics statements="107" coveredstatements="95" conditionals="11" coveredconditionals="9" methods="4" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="23" count="19" type="stmt"/>
        <line num="24" count="19" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="32" count="16" type="cond" truecount="1" falsecount="0"/>
        <line num="33" count="15" type="stmt"/>
        <line num="34" count="15" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="39" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="42" count="16" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="45" count="4" type="stmt"/>
        <line num="46" count="4" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="89" count="10" type="stmt"/>
        <line num="90" count="10" type="cond" truecount="0" falsecount="1"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="10" type="stmt"/>
        <line num="93" count="10" type="cond" truecount="0" falsecount="1"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
      </file>
      <file name="presets.ts" path="/Users/<USER>/coding/TAgent/src/themes/presets.ts">
        <metrics statements="213" coveredstatements="213" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
      </file>
    </package>
    <package name="types">
      <metrics statements="607" coveredstatements="607" conditionals="2" coveredconditionals="2" methods="0" coveredmethods="0"/>
      <file name="history.ts" path="/Users/<USER>/coding/TAgent/src/types/history.ts">
        <metrics statements="73" coveredstatements="73" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/coding/TAgent/src/types/index.ts">
        <metrics statements="534" coveredstatements="534" conditionals="2" coveredconditionals="2" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="234" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="253" count="1" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="257" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="263" count="1" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="267" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="269" count="1" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="stmt"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="282" count="1" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="289" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="300" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="303" count="1" type="stmt"/>
        <line num="304" count="1" type="stmt"/>
        <line num="305" count="1" type="stmt"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="309" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="325" count="1" type="stmt"/>
        <line num="326" count="1" type="stmt"/>
        <line num="327" count="1" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="330" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="332" count="1" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="334" count="1" type="stmt"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="349" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="352" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="354" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="356" count="1" type="stmt"/>
        <line num="357" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="360" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="368" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="370" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="377" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="393" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="395" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="405" count="1" type="stmt"/>
        <line num="406" count="1" type="stmt"/>
        <line num="407" count="1" type="stmt"/>
        <line num="408" count="1" type="stmt"/>
        <line num="409" count="1" type="stmt"/>
        <line num="410" count="1" type="stmt"/>
        <line num="411" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="415" count="1" type="stmt"/>
        <line num="416" count="1" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="1" type="stmt"/>
        <line num="419" count="1" type="stmt"/>
        <line num="420" count="1" type="stmt"/>
        <line num="421" count="1" type="stmt"/>
        <line num="422" count="1" type="stmt"/>
        <line num="423" count="1" type="stmt"/>
        <line num="424" count="1" type="stmt"/>
        <line num="425" count="1" type="stmt"/>
        <line num="426" count="1" type="stmt"/>
        <line num="427" count="1" type="stmt"/>
        <line num="428" count="1" type="stmt"/>
        <line num="429" count="1" type="stmt"/>
        <line num="430" count="1" type="stmt"/>
        <line num="431" count="1" type="stmt"/>
        <line num="432" count="1" type="stmt"/>
        <line num="433" count="1" type="stmt"/>
        <line num="434" count="1" type="stmt"/>
        <line num="435" count="1" type="stmt"/>
        <line num="436" count="1" type="stmt"/>
        <line num="437" count="1" type="stmt"/>
        <line num="438" count="1" type="stmt"/>
        <line num="439" count="1" type="stmt"/>
        <line num="440" count="1" type="stmt"/>
        <line num="441" count="1" type="stmt"/>
        <line num="442" count="1" type="stmt"/>
        <line num="443" count="1" type="stmt"/>
        <line num="444" count="1" type="stmt"/>
        <line num="445" count="1" type="stmt"/>
        <line num="446" count="1" type="stmt"/>
        <line num="447" count="1" type="stmt"/>
        <line num="448" count="1" type="stmt"/>
        <line num="449" count="1" type="stmt"/>
        <line num="450" count="1" type="stmt"/>
        <line num="451" count="1" type="stmt"/>
        <line num="452" count="1" type="stmt"/>
        <line num="453" count="1" type="stmt"/>
        <line num="454" count="1" type="stmt"/>
        <line num="455" count="1" type="stmt"/>
        <line num="456" count="1" type="stmt"/>
        <line num="457" count="1" type="stmt"/>
        <line num="458" count="1" type="stmt"/>
        <line num="459" count="1" type="stmt"/>
        <line num="460" count="1" type="stmt"/>
        <line num="461" count="1" type="stmt"/>
        <line num="462" count="1" type="stmt"/>
        <line num="463" count="1" type="stmt"/>
        <line num="464" count="1" type="stmt"/>
        <line num="465" count="1" type="stmt"/>
        <line num="466" count="1" type="stmt"/>
        <line num="467" count="1" type="stmt"/>
        <line num="468" count="1" type="stmt"/>
        <line num="469" count="1" type="stmt"/>
        <line num="470" count="1" type="stmt"/>
        <line num="471" count="1" type="stmt"/>
        <line num="472" count="1" type="stmt"/>
        <line num="473" count="1" type="stmt"/>
        <line num="474" count="1" type="stmt"/>
        <line num="475" count="1" type="stmt"/>
        <line num="476" count="1" type="stmt"/>
        <line num="477" count="1" type="stmt"/>
        <line num="478" count="1" type="stmt"/>
        <line num="479" count="1" type="stmt"/>
        <line num="480" count="1" type="stmt"/>
        <line num="481" count="1" type="stmt"/>
        <line num="482" count="1" type="stmt"/>
        <line num="483" count="1" type="stmt"/>
        <line num="484" count="1" type="stmt"/>
        <line num="485" count="1" type="stmt"/>
        <line num="486" count="1" type="stmt"/>
        <line num="487" count="1" type="stmt"/>
        <line num="488" count="1" type="stmt"/>
        <line num="489" count="1" type="stmt"/>
        <line num="490" count="1" type="stmt"/>
        <line num="491" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="492" count="1" type="stmt"/>
        <line num="493" count="1" type="stmt"/>
        <line num="494" count="1" type="stmt"/>
        <line num="495" count="1" type="stmt"/>
        <line num="496" count="1" type="stmt"/>
        <line num="497" count="1" type="stmt"/>
        <line num="498" count="1" type="stmt"/>
        <line num="499" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="500" count="1" type="stmt"/>
        <line num="501" count="1" type="stmt"/>
        <line num="502" count="1" type="stmt"/>
        <line num="503" count="1" type="stmt"/>
        <line num="504" count="1" type="stmt"/>
        <line num="505" count="1" type="stmt"/>
        <line num="506" count="1" type="stmt"/>
        <line num="507" count="1" type="stmt"/>
        <line num="508" count="1" type="stmt"/>
        <line num="509" count="1" type="stmt"/>
        <line num="510" count="1" type="stmt"/>
        <line num="511" count="1" type="stmt"/>
        <line num="512" count="1" type="stmt"/>
        <line num="513" count="1" type="stmt"/>
        <line num="514" count="1" type="stmt"/>
        <line num="515" count="1" type="stmt"/>
        <line num="516" count="1" type="stmt"/>
        <line num="517" count="1" type="stmt"/>
        <line num="518" count="1" type="stmt"/>
        <line num="519" count="1" type="stmt"/>
        <line num="520" count="1" type="stmt"/>
        <line num="521" count="1" type="stmt"/>
        <line num="522" count="1" type="stmt"/>
        <line num="523" count="1" type="stmt"/>
        <line num="524" count="1" type="stmt"/>
        <line num="525" count="1" type="stmt"/>
        <line num="526" count="1" type="stmt"/>
        <line num="527" count="1" type="stmt"/>
        <line num="528" count="1" type="stmt"/>
        <line num="529" count="1" type="stmt"/>
        <line num="530" count="1" type="stmt"/>
        <line num="531" count="1" type="stmt"/>
        <line num="532" count="1" type="stmt"/>
        <line num="533" count="1" type="stmt"/>
        <line num="534" count="1" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="249" coveredstatements="237" conditionals="51" coveredconditionals="48" methods="14" coveredmethods="11"/>
      <file name="helpers.ts" path="/Users/<USER>/coding/TAgent/src/utils/helpers.ts">
        <metrics statements="249" coveredstatements="237" conditionals="51" coveredconditionals="48" methods="14" coveredmethods="11"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="8" count="249" type="stmt"/>
        <line num="9" count="249" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="18" count="3" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="3" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="22" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="23" count="5" type="stmt"/>
        <line num="24" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="25" count="5" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="35" count="2" type="stmt"/>
        <line num="36" count="2" type="stmt"/>
        <line num="37" count="2" type="stmt"/>
        <line num="38" count="2" type="stmt"/>
        <line num="39" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="41" count="3" type="stmt"/>
        <line num="42" count="3" type="stmt"/>
        <line num="43" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="44" count="3" type="stmt"/>
        <line num="45" count="5" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="55" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="6" type="stmt"/>
        <line num="57" count="6" type="stmt"/>
        <line num="58" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="59" count="7" type="stmt"/>
        <line num="60" count="7" type="stmt"/>
        <line num="61" count="7" type="stmt"/>
        <line num="62" count="7" type="stmt"/>
        <line num="63" count="7" type="stmt"/>
        <line num="64" count="7" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="72" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="73" count="6" type="stmt"/>
        <line num="74" count="6" type="stmt"/>
        <line num="75" count="6" type="stmt"/>
        <line num="76" count="6" type="stmt"/>
        <line num="77" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="80" count="5" type="stmt"/>
        <line num="81" count="5" type="stmt"/>
        <line num="82" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="83" count="2" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="85" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="86" count="3" type="stmt"/>
        <line num="87" count="3" type="stmt"/>
        <line num="88" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="94" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="115" count="3" type="stmt"/>
        <line num="116" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="117" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="120" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="3" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="146" count="6" type="stmt"/>
        <line num="147" count="6" type="stmt"/>
        <line num="148" count="6" type="stmt"/>
        <line num="149" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="150" count="148" type="stmt"/>
        <line num="151" count="148" type="stmt"/>
        <line num="152" count="6" type="stmt"/>
        <line num="153" count="6" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="161" count="7" type="stmt"/>
        <line num="162" count="7" type="stmt"/>
        <line num="163" count="7" type="stmt"/>
        <line num="164" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="165" count="3" type="stmt"/>
        <line num="166" count="3" type="stmt"/>
        <line num="167" count="7" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="176" count="11" type="stmt"/>
        <line num="177" count="11" type="stmt"/>
        <line num="178" count="11" type="stmt"/>
        <line num="179" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="180" count="6" type="stmt"/>
        <line num="181" count="6" type="stmt"/>
        <line num="182" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="183" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="184" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="185" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="188" count="7" type="stmt"/>
        <line num="189" count="7" type="stmt"/>
        <line num="190" count="8" type="stmt"/>
        <line num="191" count="6" type="stmt"/>
        <line num="192" count="6" type="stmt"/>
        <line num="193" count="6" type="stmt"/>
        <line num="194" count="6" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="20" type="cond" truecount="1" falsecount="0"/>
        <line num="202" count="20" type="cond" truecount="1" falsecount="0"/>
        <line num="203" count="20" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="240" count="1" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="244" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="245" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="246" count="13" type="cond" truecount="3" falsecount="0"/>
        <line num="247" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="248" count="7" type="stmt"/>
        <line num="249" count="7" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
