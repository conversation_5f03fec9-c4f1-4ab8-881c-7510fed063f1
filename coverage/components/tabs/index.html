
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for components/tabs</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> components/tabs</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">87.71% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>300/342</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.45% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>44/74</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.11% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>11/18</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">87.71% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>300/342</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="NewTabButton.tsx"><a href="NewTabButton.tsx.html">NewTabButton.tsx</a></td>
	<td data-value="96.55" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.55" class="pct high">96.55%</td>
	<td data-value="29" class="abs high">28/29</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="96.55" class="pct high">96.55%</td>
	<td data-value="29" class="abs high">28/29</td>
	</tr>

<tr>
	<td class="file high" data-value="TabBar.tsx"><a href="TabBar.tsx.html">TabBar.tsx</a></td>
	<td data-value="97.82" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.82" class="pct high">97.82%</td>
	<td data-value="46" class="abs high">45/46</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="5" class="abs high">4/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="97.82" class="pct high">97.82%</td>
	<td data-value="46" class="abs high">45/46</td>
	</tr>

<tr>
	<td class="file high" data-value="TabContent.tsx"><a href="TabContent.tsx.html">TabContent.tsx</a></td>
	<td data-value="91.35" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 91%"></div><div class="cover-empty" style="width: 9%"></div></div>
	</td>
	<td data-value="91.35" class="pct high">91.35%</td>
	<td data-value="81" class="abs high">74/81</td>
	<td data-value="51.06" class="pct medium">51.06%</td>
	<td data-value="47" class="abs medium">24/47</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	<td data-value="91.35" class="pct high">91.35%</td>
	<td data-value="81" class="abs high">74/81</td>
	</tr>

<tr>
	<td class="file medium" data-value="TabItem.tsx"><a href="TabItem.tsx.html">TabItem.tsx</a></td>
	<td data-value="73.8" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 73%"></div><div class="cover-empty" style="width: 27%"></div></div>
	</td>
	<td data-value="73.8" class="pct medium">73.8%</td>
	<td data-value="126" class="abs medium">93/126</td>
	<td data-value="58.33" class="pct medium">58.33%</td>
	<td data-value="12" class="abs medium">7/12</td>
	<td data-value="28.57" class="pct low">28.57%</td>
	<td data-value="7" class="abs low">2/7</td>
	<td data-value="73.8" class="pct medium">73.8%</td>
	<td data-value="126" class="abs medium">93/126</td>
	</tr>

<tr>
	<td class="file high" data-value="TabManager.tsx"><a href="TabManager.tsx.html">TabManager.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="60" class="abs high">60/60</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="60" class="abs high">60/60</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-02T03:16:02.204Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    