---
description:
globs:
alwaysApply: true
---
# 项目开发指导规则

## 概述
在执行任何开发任务时，请始终参考项目的核心文档以确保开发工作符合项目要求和规范。本规则旨在为AI提供充分的上下文信息，实现高效、一致的代码开发。

## 核心参考文档

### 项目概述文档
- @README.md - **项目全景指南**，包含：
  - 项目基本信息（名称、描述、版本）
  - 安装和使用指南
  - **项目架构和目录结构** - 理解代码组织方式
  - **核心文件和模块功能说明** - 掌握关键组件职责
  - **技术栈和依赖要求** - 了解技术约束和环境要求
  - **代码规范和最佳实践** - 保持代码风格一致性
  - **API文档和接口规范** - 了解对外接口定义

### 产品需求文档
- [prd.md](mdc:doc/prd.md) - **业务需求蓝图**，包含：
  - 详细的功能规格和业务逻辑
  - 用户故事和使用场景
  - 非功能性需求（性能、安全、可用性）
  - 业务流程和数据流设计
  - UI/UX设计要求

### 任务管理文档
- [tasks.md](mdc:doc/tasks.md) - **开发执行指南**，包含：
  - 子任务列表和优先级排序
  - **任务依赖关系** - 理解开发顺序和阻塞关系
  - **当前开发状态** - 掌握项目进度和完成情况
- @task_*.md - /doc/tasks/ 目录下每个子任务的详细描述

## 开发工作流程

1. **文档理解**: 按顺序查阅核心文档
   - [tasks.md](mdc:doc/tasks.md) - 了解当前任务需求和依赖关系，判断下一个需要执行的子任务 @task_*.md
   - [prd.md](mdc:doc/prd.md) - 确认业务需求和功能规格
   - [README.md](mdc:README.md) - 掌握项目架构和代码规范

2. **方案设计**: 基于文档约束设计技术实现方案

3. **代码实现**: 遵循项目规范进行开发

4. **状态更新**: 及时更新任务完成状态

## 重要注意事项

- **架构一致性**: 新功能必须符合 [README.md](mdc:README.md) 中定义的架构原则
- **代码规范**: 严格遵循项目既定的编码标准和最佳实践
- **功能完整性**: 确保实现完全满足 [prd.md](mdc:doc/prd.md) 中的功能规格
- **功能测试**: 确保实现的功能经过测试，包括单元测试，以及应用测试
- **依赖管理**: 严格按照 [tasks.md](mdc:doc/tasks.md) 中的依赖关系执行开发
- **文档优先**: 当代码与文档不一致时，优先以最新文档为准
- **SDK 文档**: 请使用 context7 (mcp 服务) 获取三方 SDK 文档
- **清理环境**: 除了正常单元、集成测试所需内容或者文件外，其他临时生成的内容和文件在结束前去除。
