{"name": "tagent", "version": "0.1.0", "private": true, "type": "module", "description": "TAgent - AI Enhanced Terminal Agent", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "prepare": "husky install"}, "dependencies": {"@headlessui/react": "^1.7.0", "@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-fs": "^2.4.0", "@tauri-apps/plugin-global-shortcut": "^2.3.0", "@tauri-apps/plugin-os": "^2.3.0", "@tauri-apps/plugin-shell": "^2.3.0", "clsx": "^2.0.0", "lucide-react": "^0.263.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "zustand": "^4.5.7"}, "devDependencies": {"@commitlint/cli": "^17.7.0", "@commitlint/config-conventional": "^17.7.0", "@tauri-apps/cli": "^2.6.2", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.0.0", "@vitest/coverage-v8": "^0.34.0", "@vitest/ui": "^0.34.0", "autoprefixer": "^10.4.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^8.0.0", "jsdom": "^22.1.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.0"}, "author": "TAgent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/TAgent.git"}, "keywords": ["terminal", "ai", "tauri", "rust", "react", "command-line"]}