# 终端界面大小调整时换行符问题修复

## 问题描述

在调整终端界面大小时，终端命令行中会莫名出现多个换行符，影响用户体验。

## 问题原因分析

1. **ResizeObserver频繁触发**: 没有防抖处理，导致终端大小调整函数被频繁调用
2. **重复的大小调整**: 即使尺寸没有实际改变，也会触发resize操作
3. **终端输出处理不完善**: 对于终端大小调整时产生的控制序列和多余换行符处理不够完善

## 修复方案

### 1. 智能检测终端大小调整输出

**文件**: `src/hooks/useRealTerminal.ts`

**新增功能**:
- 添加 `isResizeOutput()` 函数，智能检测终端大小调整产生的输出
- 识别常见的终端大小调整输出模式：
  - 只包含换行符和空白字符的输出
  - 包含光标定位序列的输出
  - 包含清屏/清行序列的输出
  - 包含光标移动序列的输出
- 对检测到的终端大小调整输出直接跳过处理

### 2. 添加ResizeObserver防抖处理

**文件**: `src/components/Terminal/TerminalDisplay.tsx` 和 `src/components/terminal/TerminalDisplay.tsx`

**修改内容**:
- 添加150ms的防抖延迟
- 只有当尺寸真正改变时才触发resize
- 正确清理timeout以避免内存泄漏

```typescript
// 防抖处理，避免频繁调整
if (resizeTimeout) {
  clearTimeout(resizeTimeout);
}

resizeTimeout = setTimeout(() => {
  // 只有当大小真正改变时才调整
  if (cols > 0 && rows > 0 && (cols !== lastSize.cols || rows !== lastSize.rows)) {
    lastSize = { cols, rows };
    resize(rows, cols);
    onResize?.(cols, rows);
    console.log('[TerminalDisplay] 终端大小已调整:', { rows, cols });
  }
}, 150); // 150ms防抖延迟
```

### 3. 优化终端输出处理

**文件**: `src/hooks/useRealTerminal.ts`

**修改内容**:
- 改进回车符和换行符的处理逻辑
- 添加多余换行符的过滤（更严格，只保留1个连续换行符）
- 优化光标控制序列的处理
- 添加智能空行检测，跳过终端大小调整产生的空行

```typescript
// 智能检测终端大小调整输出
const isResizeOutput = (input: string): boolean => {
  const ESC = '\u001b';
  const patterns = [
    /^\s*\n+\s*$/,                                                    // 只有换行符和空白
    new RegExp(`^${ESC}\\[[0-9;]*[Hf]\\s*\\n*\\s*$`),              // 光标定位序列
    new RegExp(`^${ESC}\\[[0-9]*[JK]\\s*\\n*\\s*$`),               // 清屏/清行序列
    new RegExp(`^${ESC}\\[[0-9]*[ABCD]\\s*\\n*\\s*$`),             // 光标移动序列
  ];

  return patterns.some(pattern => pattern.test(input));
};

// 处理回车符和换行符的组合
processedData = processedData.replace(/\r\n/g, '\n');
processedData = processedData.replace(/\r(?!\n)/g, '\n'); // 只替换不跟随\n的\r

// 处理终端大小调整时可能产生的多余换行符
// 移除连续的多个换行符，保留最多1个连续换行符
processedData = processedData.replace(/\n{2,}/g, '\n');

// 特别处理：如果数据只包含换行符和空白字符，可能是终端大小调整产生的
if (/^\s*\n*\s*$/.test(processedData)) {
  return; // 直接跳过处理
}
```

### 4. 修复useCallback依赖和正则表达式警告

**文件**: `src/hooks/useRealTerminal.ts`

**修改内容**:
- 移除handleKeyInput中不必要的clear依赖
- 修复所有正则表达式中的控制字符警告，使用字符串构造方式
- 修复过时的substr方法，改用substring

## 测试验证

创建了完整的测试套件 `src/test/terminal-resize-fix.test.ts` 来验证修复效果：

1. **防抖测试**: 验证resize调用的防抖处理
2. **换行符过滤测试**: 验证多余换行符的正确过滤
3. **回车符处理测试**: 验证\r\n和\r的正确处理
4. **尺寸变化检测测试**: 验证只有真正的尺寸变化才触发resize
5. **控制序列过滤测试**: 验证光标控制序列的正确移除
6. **清屏序列过滤测试**: 验证清屏和清行序列的正确移除
7. **终端大小调整输出检测测试**: 验证智能检测终端大小调整输出的功能

所有测试均通过，确保修复的有效性。

## 修复效果

- ✅ 终端大小调整时不再产生多余的换行符
- ✅ 提高了终端界面的响应性能（通过防抖处理）
- ✅ 改善了终端输出的显示质量
- ✅ 减少了不必要的后端调用

## 相关文件

- `src/components/Terminal/TerminalDisplay.tsx`
- `src/components/terminal/TerminalDisplay.tsx`
- `src/hooks/useRealTerminal.ts`
- `src/test/terminal-resize-fix.test.ts`

## 注意事项

1. 防抖延迟设置为150ms，在响应性和性能之间取得平衡
2. 换行符过滤保留最多2个连续换行符，避免过度过滤影响正常输出
3. 控制序列过滤针对常见的光标移动和清屏序列，不影响颜色等其他ANSI序列
