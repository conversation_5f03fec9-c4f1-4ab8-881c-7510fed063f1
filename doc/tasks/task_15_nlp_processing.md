# Task 4.4: 自然语言处理

## 任务概述
- **任务ID**: Task 4.4
- **任务名称**: 自然语言处理
- **优先级**: 高
- **预计时间**: 3 天
- **负责人**: 待分配
- **状态**: ⏳ 待开始

## 任务描述
实现 TAgent 的核心 AI 功能 - 自然语言转命令处理系统。该系统允许用户通过 `@command` 前缀使用自然语言描述需求，自动转换为可执行的命令。同时实现命令解释、智能建议和错误分析等增强功能。

## 依赖关系
- **前置任务**: Task 4.1 (AI 服务架构), Task 4.3 (云端模型接口)
- **后续任务**: Task 4.5 (AI 对话功能), Task 6.1 (危险命令检测)

## 具体任务

### 4.4.1 自然语言处理核心引擎 (8 小时)
- 实现自然语言解析器
- 设计命令生成算法
- 实现上下文理解机制

#### NLP 处理流程图

```
┌─────────────────────────────────────────────────────────┐
│                  自然语言处理流程                          │
├─────────────────────────────────────────────────────────┤
│  输入预处理                                              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐│
│  │ Text Clean  │ Intent Det  │ Entity Ext  │ Context Ana ││
│  │ - 标准化     │ - 意图识别   │ - 实体提取   │ - 上下文分析 ││
│  │ - 去噪音     │ - 分类标签   │ - 参数识别   │ - 历史记录  ││
│  │ - 分词       │ - 置信度     │ - 路径解析   │ - 环境变量  ││
│  └─────────────┴─────────────┴─────────────┴─────────────┘│
├─────────────────────────────────────────────────────────┤
│  命令生成                                                │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐│
│  │ Template    │ AI Model    │ Rule Engine │ Validation  ││
│  │ - 模板匹配   │ - 模型推理   │ - 规则引擎   │ - 语法检查  ││
│  │ - 参数填充   │ - 创新生成   │ - 模式识别   │ - 安全检查  ││
│  │ - 格式化     │ - 上下文感知 │ - 智能建议   │ - 执行预览  ││
│  └─────────────┴─────────────┴─────────────┴─────────────┘│
├─────────────────────────────────────────────────────────┤
│  输出后处理                                              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐│
│  │ Command Fmt │ Explanation │ Suggestions │ Risk Check  ││
│  │ - 格式美化   │ - 命令解释   │ - 相关建议   │ - 风险评估  ││
│  │ - 语法高亮   │ - 参数说明   │ - 优化建议   │ - 确认提示  ││
│  │ - 结构化     │ - 示例展示   │ - 学习资源   │ - 回滚方案  ││
│  └─────────────┴─────────────┴─────────────┴─────────────┘│
└─────────────────────────────────────────────────────────┘
```

#### 核心 NLP 处理器实现

```rust
// NLP 处理器
pub struct NLPProcessor {
    ai_service: Arc<AIServiceManager>,
    template_engine: TemplateEngine,
    rule_engine: RuleEngine,
    context_analyzer: ContextAnalyzer,
    command_validator: CommandValidator,
    cache: Arc<RwLock<NLPCache>>,
}

impl NLPProcessor {
    pub async fn process_natural_language(
        &self,
        input: &str,
        context: &TerminalContext,
    ) -> Result<NLPResponse, NLPError> {
        // 1. 输入预处理
        let cleaned_input = self.preprocess_input(input);
        let intent = self.detect_intent(&cleaned_input).await?;
        let entities = self.extract_entities(&cleaned_input, &intent).await?;
        let enriched_context = self.analyze_context(context, &entities).await?;

        // 2. 命令生成策略
        let generation_result = match intent.confidence {
            conf if conf > 0.8 => {
                // 高置信度：使用模板匹配 + AI 增强
                self.template_based_generation(&intent, &entities, &enriched_context).await?
            }
            conf if conf > 0.5 => {
                // 中等置信度：AI 模型主导 + 规则验证
                self.ai_model_generation(&cleaned_input, &enriched_context).await?
            }
            _ => {
                // 低置信度：多策略融合
                self.hybrid_generation(&cleaned_input, &intent, &entities, &enriched_context).await?
            }
        };

        // 3. 后处理和验证
        let validated_command = self.validate_command(&generation_result).await?;
        let explanation = self.generate_explanation(&validated_command).await?;
        let suggestions = self.generate_suggestions(&validated_command, &enriched_context).await?;
        let risk_assessment = self.assess_risk(&validated_command).await?;

        Ok(NLPResponse {
            generated_command: validated_command,
            explanation,
            suggestions,
            risk_assessment,
            confidence: generation_result.confidence,
            metadata: ResponseMetadata {
                processing_time: generation_result.processing_time,
                strategy_used: generation_result.strategy,
                model_version: self.ai_service.get_current_model_info().version,
            },
        })
    }

    async fn preprocess_input(&self, input: &str) -> CleanedInput {
        CleanedInput {
            original: input.to_string(),
            normalized: self.normalize_text(input),
            tokens: self.tokenize(input),
            sanitized: self.sanitize_input(input),
        }
    }

    async fn detect_intent(&self, input: &CleanedInput) -> Result<Intent, NLPError> {
        // 意图识别逻辑
        let patterns = self.load_intent_patterns();
        let ai_prediction = self.ai_service.predict_intent(&input.normalized).await?;
        let rule_match = self.rule_engine.match_intent(&input.tokens);

        Intent::merge(ai_prediction, rule_match)
    }
}

// 数据结构定义
#[derive(Debug, Clone)]
pub struct NLPResponse {
    pub generated_command: GeneratedCommand,
    pub explanation: CommandExplanation,
    pub suggestions: Vec<CommandSuggestion>,
    pub risk_assessment: RiskAssessment,
    pub confidence: f32,
    pub metadata: ResponseMetadata,
}

#[derive(Debug, Clone)]
pub struct GeneratedCommand {
    pub command: String,
    pub parsed_parts: Vec<CommandPart>,
    pub execution_context: ExecutionContext,
    pub requires_confirmation: bool,
}

#[derive(Debug, Clone)]
pub struct CommandExplanation {
    pub summary: String,
    pub detailed_breakdown: Vec<ParameterExplanation>,
    pub expected_output: String,
    pub potential_side_effects: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct CommandSuggestion {
    pub suggestion_type: SuggestionType,
    pub content: String,
    pub priority: u8,
    pub category: SuggestionCategory,
}

#[derive(Debug, Clone)]
pub enum SuggestionType {
    Alternative,    // 替代命令
    Optimization,   // 优化建议
    SafetyTip,     // 安全提示
    LearningTip,   // 学习建议
    RelatedCommand, // 相关命令
}
```

### 4.4.2 命令模板系统 (6 小时)
- 构建常用命令模板库
- 实现模板匹配算法
- 支持动态参数填充

#### 命令模板引擎

```rust
pub struct TemplateEngine {
    templates: HashMap<String, CommandTemplate>,
    pattern_matcher: PatternMatcher,
    parameter_resolver: ParameterResolver,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub patterns: Vec<String>,
    pub command_template: String,
    pub parameters: Vec<TemplateParameter>,
    pub examples: Vec<TemplateExample>,
    pub category: CommandCategory,
    pub risk_level: RiskLevel,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateParameter {
    pub name: String,
    pub param_type: ParameterType,
    pub required: bool,
    pub default_value: Option<String>,
    pub validation_rules: Vec<ValidationRule>,
    pub extraction_patterns: Vec<String>,
}

impl TemplateEngine {
    pub fn match_templates(&self, input: &str, entities: &[Entity]) -> Vec<TemplateMatch> {
        let mut matches = Vec::new();

        for template in self.templates.values() {
            if let Some(score) = self.calculate_match_score(input, template, entities) {
                matches.push(TemplateMatch {
                    template: template.clone(),
                    score,
                    parameter_mappings: self.map_parameters(template, entities),
                });
            }
        }

        matches.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        matches
    }

    fn load_default_templates() -> HashMap<String, CommandTemplate> {
        // 文件操作模板
        let file_ops = vec![
            CommandTemplate {
                id: "list_files".to_string(),
                name: "列出文件".to_string(),
                description: "列出目录中的文件和文件夹".to_string(),
                patterns: vec![
                    "列出.*文件".to_string(),
                    "显示.*目录.*内容".to_string(),
                    "查看.*文件夹".to_string(),
                    "list.*files".to_string(),
                ],
                command_template: "ls {flags} {path}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "flags".to_string(),
                        param_type: ParameterType::Flags,
                        required: false,
                        default_value: Some("-la".to_string()),
                        validation_rules: vec![],
                        extraction_patterns: vec!["详细", "隐藏", "大小"],
                    },
                    TemplateParameter {
                        name: "path".to_string(),
                        param_type: ParameterType::Path,
                        required: false,
                        default_value: Some(".".to_string()),
                        validation_rules: vec![ValidationRule::PathExists],
                        extraction_patterns: vec![r"\S+/\S*", r"\.\w+"],
                    },
                ],
                examples: vec![
                    TemplateExample {
                        input: "列出当前目录下的所有文件".to_string(),
                        output: "ls -la".to_string(),
                        description: "显示详细信息和隐藏文件".to_string(),
                    },
                ],
                category: CommandCategory::FileSystem,
                risk_level: RiskLevel::Low,
            },
            // 更多模板...
        ];

        // 进程管理模板
        let process_ops = vec![
            CommandTemplate {
                id: "kill_process".to_string(),
                name: "终止进程".to_string(),
                description: "终止指定的进程".to_string(),
                patterns: vec![
                    "杀死.*进程".to_string(),
                    "终止.*程序".to_string(),
                    "停止.*服务".to_string(),
                    "kill.*process".to_string(),
                ],
                command_template: "kill {signal} {pid_or_name}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "signal".to_string(),
                        param_type: ParameterType::Signal,
                        required: false,
                        default_value: Some("-TERM".to_string()),
                        validation_rules: vec![],
                        extraction_patterns: vec!["强制", "立即", "gracefully"],
                    },
                ],
                category: CommandCategory::Process,
                risk_level: RiskLevel::Medium,
            },
        ];

        // 网络操作模板
        let network_ops = vec![
            CommandTemplate {
                id: "ping_host".to_string(),
                name: "网络ping测试".to_string(),
                description: "测试网络连接".to_string(),
                patterns: vec![
                    "ping.*".to_string(),
                    "测试.*连接".to_string(),
                    "检查.*网络".to_string(),
                ],
                command_template: "ping {flags} {host}".to_string(),
                parameters: vec![
                    TemplateParameter {
                        name: "host".to_string(),
                        param_type: ParameterType::Host,
                        required: true,
                        default_value: None,
                        validation_rules: vec![ValidationRule::ValidHost],
                        extraction_patterns: vec![
                            r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b",
                            r"\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b",
                        ],
                    },
                ],
                category: CommandCategory::Network,
                risk_level: RiskLevel::Low,
            },
        ];

        let mut templates = HashMap::new();
        for template in file_ops.into_iter().chain(process_ops).chain(network_ops) {
            templates.insert(template.id.clone(), template);
        }
        templates
    }
}
```

### 4.4.3 上下文感知系统 (6 小时)
- 实现终端状态感知
- 支持命令历史分析
- 实现环境变量理解

#### 上下文分析器

```rust
pub struct ContextAnalyzer {
    terminal_state: Arc<RwLock<TerminalState>>,
    history_analyzer: HistoryAnalyzer,
    environment_parser: EnvironmentParser,
    project_detector: ProjectDetector,
}

impl ContextAnalyzer {
    pub async fn analyze_context(
        &self,
        base_context: &TerminalContext,
        entities: &[Entity],
    ) -> Result<EnrichedContext, ContextError> {
        let terminal_state = self.terminal_state.read().await;

        // 分析当前目录上下文
        let directory_context = self.analyze_directory_context(&terminal_state.current_directory).await?;

        // 分析命令历史模式
        let history_patterns = self.history_analyzer.analyze_patterns(&terminal_state.command_history)?;

        // 分析环境变量
        let environment_context = self.environment_parser.parse(&terminal_state.environment)?;

        // 检测项目类型
        let project_context = self.project_detector.detect_project_type(&terminal_state.current_directory).await?;

        // 分析用户意图的上下文依赖
        let intent_context = self.analyze_intent_context(entities, &directory_context).await?;

        Ok(EnrichedContext {
            base: base_context.clone(),
            directory: directory_context,
            history: history_patterns,
            environment: environment_context,
            project: project_context,
            intent: intent_context,
            timestamp: chrono::Utc::now(),
        })
    }

    async fn analyze_directory_context(&self, path: &str) -> Result<DirectoryContext, ContextError> {
        let path_obj = Path::new(path);

        // 分析目录结构
        let files = fs::read_dir(path_obj)
            .map_err(|e| ContextError::IoError(e))?
            .filter_map(|entry| entry.ok())
            .map(|entry| FileInfo {
                name: entry.file_name().to_string_lossy().to_string(),
                is_dir: entry.path().is_dir(),
                size: entry.metadata().map(|m| m.len()).unwrap_or(0),
                permissions: self.analyze_permissions(&entry.path()),
            })
            .collect();

        // 检测特殊文件
        let special_files = self.detect_special_files(&files);

        // 分析路径特征
        let path_features = self.analyze_path_features(path_obj);

        Ok(DirectoryContext {
            current_path: path.to_string(),
            files,
            special_files,
            path_features,
            is_git_repo: path_obj.join(".git").exists(),
            is_project_root: self.is_project_root(path_obj),
            disk_usage: self.calculate_disk_usage(path_obj).await?,
        })
    }
}

#[derive(Debug, Clone)]
pub struct EnrichedContext {
    pub base: TerminalContext,
    pub directory: DirectoryContext,
    pub history: HistoryPatterns,
    pub environment: EnvironmentContext,
    pub project: ProjectContext,
    pub intent: IntentContext,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone)]
pub struct DirectoryContext {
    pub current_path: String,
    pub files: Vec<FileInfo>,
    pub special_files: Vec<SpecialFile>,
    pub path_features: PathFeatures,
    pub is_git_repo: bool,
    pub is_project_root: bool,
    pub disk_usage: DiskUsage,
}

#[derive(Debug, Clone)]
pub struct HistoryPatterns {
    pub frequent_commands: Vec<CommandFrequency>,
    pub recent_patterns: Vec<CommandPattern>,
    pub user_preferences: UserCommandPreferences,
    pub error_patterns: Vec<ErrorPattern>,
}
```

### 4.4.4 智能建议系统 (4 小时)
- 实现命令优化建议
- 支持相关命令推荐
- 实现学习资源推荐

#### 智能建议引擎

```rust
pub struct SuggestionEngine {
    command_database: CommandDatabase,
    optimization_rules: OptimizationRules,
    learning_resources: LearningResourceDatabase,
    user_profiler: UserProfiler,
}

impl SuggestionEngine {
    pub async fn generate_suggestions(
        &self,
        command: &GeneratedCommand,
        context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 1. 优化建议
        suggestions.extend(self.generate_optimization_suggestions(command, context).await?);

        // 2. 安全建议
        suggestions.extend(self.generate_safety_suggestions(command).await?);

        // 3. 相关命令建议
        suggestions.extend(self.generate_related_commands(command, context).await?);

        // 4. 学习建议
        suggestions.extend(self.generate_learning_suggestions(command, context).await?);

        // 5. 个性化建议
        suggestions.extend(self.generate_personalized_suggestions(command, context).await?);

        // 排序和过滤
        self.rank_and_filter_suggestions(suggestions, context).await
    }

    async fn generate_optimization_suggestions(
        &self,
        command: &GeneratedCommand,
        context: &EnrichedContext,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 检查是否有更高效的替代方案
        if let Some(alternatives) = self.optimization_rules.find_alternatives(&command.command) {
            for alt in alternatives {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::Optimization,
                    content: format!("考虑使用 '{}' 代替，因为{}", alt.command, alt.reason),
                    priority: alt.priority,
                    category: SuggestionCategory::Performance,
                });
            }
        }

        // 检查参数优化
        if let Some(param_opts) = self.analyze_parameter_optimizations(command, context).await? {
            suggestions.extend(param_opts);
        }

        // 检查组合命令优化
        if let Some(pipeline_opts) = self.suggest_pipeline_optimizations(command, context).await? {
            suggestions.extend(pipeline_opts);
        }

        Ok(suggestions)
    }

    async fn generate_safety_suggestions(
        &self,
        command: &GeneratedCommand,
    ) -> Result<Vec<CommandSuggestion>, SuggestionError> {
        let mut suggestions = Vec::new();

        // 检查危险操作
        if self.is_potentially_dangerous(&command.command) {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::SafetyTip,
                content: "此命令可能有风险，建议先在测试环境中验证".to_string(),
                priority: 9,
                category: SuggestionCategory::Safety,
            });

            // 提供更安全的替代方案
            if let Some(safer_alt) = self.find_safer_alternative(&command.command) {
                suggestions.push(CommandSuggestion {
                    suggestion_type: SuggestionType::Alternative,
                    content: format!("更安全的做法：{}", safer_alt),
                    priority: 8,
                    category: SuggestionCategory::Safety,
                });
            }
        }

        // 提供备份建议
        if self.should_suggest_backup(&command.command) {
            suggestions.push(CommandSuggestion {
                suggestion_type: SuggestionType::SafetyTip,
                content: "建议先备份相关文件：cp file file.bak".to_string(),
                priority: 7,
                category: SuggestionCategory::Safety,
            });
        }

        Ok(suggestions)
    }
}
```

## 验收标准

### 功能性测试
1. **@command 基础功能**
   - [ ] 能够正确解析简单的自然语言命令（准确率 > 85%）
   - [ ] 支持中英文混合输入
   - [ ] 能够处理模糊或不完整的描述

2. **上下文感知**
   - [ ] 能够根据当前目录调整命令建议
   - [ ] 能够利用命令历史优化建议
   - [ ] 能够理解项目类型和环境

3. **智能建议**
   - [ ] 提供相关的替代命令
   - [ ] 给出安全性建议
   - [ ] 提供学习资源推荐

### 性能测试
1. **响应时间**
   - [ ] 本地模型响应时间 < 2 秒
   - [ ] 云端模型响应时间 < 5 秒
   - [ ] 缓存命中时响应时间 < 200ms

2. **准确性**
   - [ ] 命令生成准确率 > 85%
   - [ ] 意图识别准确率 > 90%
   - [ ] 危险命令识别率 > 95%

### 用户体验测试
1. **易用性**
   - [ ] 新用户能在 2 分钟内理解 @command 用法
   - [ ] 错误提示清晰易懂
   - [ ] 建议内容有实用价值

2. **可靠性**
   - [ ] 系统在网络异常时能正常工作（本地模式）
   - [ ] 能够处理各种边界情况
   - [ ] 不会生成明显错误的命令

## 风险评估

### 技术风险
- **AI 模型准确性**: 可能出现生成错误命令的情况
  - 缓解措施：多层验证机制 + 用户确认
- **性能问题**: 复杂 NLP 处理可能影响响应速度
  - 缓解措施：智能缓存 + 异步处理
- **上下文理解限制**: 复杂场景下上下文分析可能不准确
  - 缓解措施：逐步改进 + 用户反馈学习

### 业务风险
- **用户接受度**: 用户可能不信任 AI 生成的命令
  - 缓解措施：透明的解释 + 安全确认机制
- **学习成本**: 新功能可能增加学习成本
  - 缓解措施：良好的文档 + 交互式教程

## 后续优化

### 短期优化 (1-2 周)
- 增加更多命令模板
- 优化意图识别算法
- 改进错误处理机制

### 中期优化 (1-2 月)
- 实现用户行为学习
- 增加多语言支持
- 优化性能和缓存

### 长期优化 (3-6 月)
- 实现完全的上下文理解
- 支持复杂的组合命令生成
- 集成更多 AI 能力

## 相关文档

- [AI 服务架构文档](./task_12_ai_service.md)
- [云端模型接口文档](./task_14_cloud_model.md)
- [产品需求文档](../prd.md)
- [项目架构设计](./task_03_architecture_design.md)
