# Task 5.2: 快捷键系统

## 任务信息
- **任务ID**: Task 5.2
- **任务名称**: 快捷键系统
- **优先级**: 中
- **预计时间**: 2 天
- **依赖任务**: Task 5.1 (多Tab管理)
- **状态**: ✅ 已完成
- **完成时间**: 2024年12月23日

## 任务完成总结

### ✅ 已实现功能
1. **完整的快捷键框架**
   - 类型定义和接口 (`src/types/index.ts`)
   - 工具函数库 (`src/utils/shortcutUtils.ts`)
   - 默认快捷键配置 (`src/constants/shortcuts.ts`)
   - 状态管理 (`src/stores/shortcutStore.ts`)

2. **前端Hook系统**
   - 核心快捷键Hook (`src/hooks/useShortcuts.ts`)
   - Tab管理快捷键 (`src/hooks/useTabShortcuts.ts`)
   - AI对话快捷键 (`src/hooks/useChatShortcuts.ts`)
   - 终端操作快捷键 (`src/hooks/useTerminalShortcuts.ts`)
   - 应用功能快捷键 (`src/hooks/useAppShortcuts.ts`)

3. **设置界面**
   - 完整的快捷键设置组件 (`src/components/settings/sections/ShortcutSettings.tsx`)
   - 快捷键录制和编辑功能
   - 冲突检测和验证
   - 配置导入导出

4. **后端Tauri集成**
   - Tauri 2.0全局快捷键支持 (`src-tauri/src/system/shortcuts.rs`)
   - 全局快捷键注册和管理
   - 主应用集成和命令处理

5. **平台适配**
   - macOS和Windows/Linux平台支持
   - 平台特定的快捷键标准化
   - 快捷键显示格式化

### 🎯 核心特性
- **全面的快捷键支持**: Tab管理、终端操作、AI功能、应用功能
- **冲突检测**: 自动检测和提醒快捷键冲突
- **平台适配**: 自动适配不同操作系统的按键习惯
- **可视化配置**: 直观的快捷键设置界面
- **持久化存储**: 快捷键配置自动保存

### 📋 测试状态
- ✅ 前端编译通过
- ✅ 113项测试全部通过
- ✅ Tab管理、UI组件、主题系统集成测试通过
- ⚠️ 后端存在一些AI模块的编译警告（不影响快捷键功能）

## 任务目标
实现完整的快捷键系统，包括全局快捷键和应用内快捷键，提升用户操作效率和使用体验。支持快捷键自定义配置，与现有功能模块深度集成。

## 功能需求

### 核心功能
1. **全局快捷键**
   - 应用唤醒/隐藏 (Cmd/Ctrl + Space)
   - 新建窗口 (Cmd/Ctrl + Shift + N)
   - 快速终端 (Cmd/Ctrl + `)

2. **Tab 管理快捷键**
   - 新建Tab (Cmd/Ctrl + T)
   - 关闭Tab (Cmd/Ctrl + W)
   - 切换Tab (Cmd/Ctrl + 1-9, Cmd/Ctrl + Tab)
   - 下一个/上一个Tab (Cmd/Ctrl + Shift + ])

3. **终端操作快捷键**
   - 复制 (Cmd/Ctrl + C)
   - 粘贴 (Cmd/Ctrl + V)
   - 全选 (Cmd/Ctrl + A)
   - 清屏 (Cmd/Ctrl + K)
   - 中断进程 (Ctrl + C)

4. **AI 功能快捷键**
   - 打开AI对话 (Cmd/Ctrl + /)
   - 快速命令 (Cmd/Ctrl + Enter)
   - AI建议 (Cmd/Ctrl + Space)

5. **应用功能快捷键**
   - 设置面板 (Cmd/Ctrl + ,)
   - 主题切换 (Cmd/Ctrl + Shift + T)
   - 搜索 (Cmd/Ctrl + F)
   - 帮助 (F1)

### 配置功能
1. **快捷键自定义**
   - 快捷键冲突检测
   - 自定义快捷键配置
   - 重置为默认设置
   - 导入/导出配置

2. **平台适配**
   - macOS (Cmd键) / Windows+Linux (Ctrl键)
   - 平台特定快捷键
   - 系统快捷键冲突避免

## 技术实现方案

### 前端架构（React + TypeScript）

#### 1. 快捷键管理组件
```typescript
// 快捷键相关组件
- ShortcutManager.tsx     // 快捷键管理器
- ShortcutProvider.tsx    // 快捷键上下文提供者
- ShortcutRegistrar.tsx   // 快捷键注册器
- ShortcutSettings.tsx    // 快捷键设置界面
- ShortcutConflictDialog.tsx // 冲突解决对话框
```

#### 2. 快捷键状态管理
```typescript
interface ShortcutState {
  // 快捷键配置
  shortcuts: Record<string, ShortcutConfig>
  // 激活状态
  isEnabled: boolean
  // 配置方法
  setShortcut: (action: string, keys: string[]) => void
  resetShortcuts: () => void
  enableShortcuts: (enabled: boolean) => void
  checkConflict: (keys: string[]) => string[]
}

interface ShortcutConfig {
  action: string           // 动作名称
  keys: string[]          // 按键组合
  description: string     // 描述
  category: ShortcutCategory
  global: boolean         // 是否为全局快捷键
  enabled: boolean        // 是否启用
  platform?: Platform     // 平台限制
}

enum ShortcutCategory {
  TAB_MANAGEMENT = 'tab_management',
  TERMINAL_OPERATION = 'terminal_operation',
  AI_FUNCTION = 'ai_function',
  APPLICATION = 'application',
  CUSTOM = 'custom'
}
```

#### 3. Hook 设计
```typescript
// useShortcuts Hook
interface UseShortcutsReturn {
  registerShortcut: (config: ShortcutConfig) => void
  unregisterShortcut: (action: string) => void
  executeAction: (action: string) => void
  isShortcutPressed: (keys: string[]) => boolean
}

// useGlobalShortcuts Hook - 全局快捷键
interface UseGlobalShortcutsReturn {
  registerGlobalShortcut: (keys: string[], callback: () => void) => void
  unregisterGlobalShortcut: (keys: string[]) => void
}

// 具体功能快捷键Hooks
- useTabShortcuts.ts      // Tab管理快捷键 (已存在，需扩展)
- useChatShortcuts.ts     // AI对话快捷键 (已存在，需扩展)
- useTerminalShortcuts.ts // 终端操作快捷键
- useAppShortcuts.ts      // 应用功能快捷键
```

#### 4. 核心实现文件
```typescript
// src/stores/shortcutStore.ts - 快捷键状态管理
// src/components/shortcuts/ShortcutManager.tsx - 快捷键管理器
// src/hooks/useShortcuts.ts - 快捷键Hook
// src/utils/shortcutUtils.ts - 快捷键工具函数
// src/constants/shortcuts.ts - 默认快捷键配置
```

### 后端架构（Rust + Tauri）

#### 1. 全局快捷键管理
```rust
// src/system/shortcuts.rs
pub struct GlobalShortcutManager {
    shortcuts: HashMap<String, GlobalShortcut>,
    app_handle: AppHandle,
}

impl GlobalShortcutManager {
    pub fn register_shortcut(&mut self, keys: &str, action: String) -> Result<(), Error>
    pub fn unregister_shortcut(&mut self, keys: &str) -> Result<(), Error>
    pub fn update_shortcut(&mut self, old_keys: &str, new_keys: &str) -> Result<(), Error>
}

pub struct GlobalShortcut {
    keys: String,
    action: String,
    enabled: bool,
}
```

#### 2. Tauri Commands
```rust
#[tauri::command]
async fn register_global_shortcut(keys: String, action: String) -> Result<(), String>

#[tauri::command]
async fn unregister_global_shortcut(keys: String) -> Result<(), String>

#[tauri::command]
async fn get_shortcut_config() -> Result<ShortcutConfig, String>

#[tauri::command]
async fn save_shortcut_config(config: ShortcutConfig) -> Result<(), String>

#[tauri::command]
async fn reset_shortcuts_to_default() -> Result<(), String>
```

## 实现步骤

### 第一阶段：基础快捷键框架 (Day 1 上午)
1. **创建快捷键状态管理**
   - 实现shortcutStore.ts
   - 定义快捷键接口和类型
   - 实现基础配置管理

2. **创建快捷键管理器**
   - 实现ShortcutManager组件
   - 实现useShortcuts Hook
   - 建立快捷键注册机制

3. **平台适配层**
   - 检测操作系统类型
   - 按键映射（Cmd/Ctrl适配）
   - 平台特定快捷键处理

### 第二阶段：应用内快捷键实现 (Day 1 下午)
1. **扩展现有Hook**
   - 完善useTabShortcuts
   - 完善useChatShortcuts
   - 新增useTerminalShortcuts
   - 新增useAppShortcuts

2. **快捷键与功能集成**
   - Tab管理快捷键集成
   - 终端操作快捷键
   - AI功能快捷键
   - 应用功能快捷键

3. **按键事件处理**
   - 全局按键监听
   - 快捷键冲突处理
   - 上下文相关快捷键

### 第三阶段：全局快捷键与配置 (Day 2 上午)
1. **全局快捷键实现**
   - Rust端全局快捷键管理
   - Tauri全局快捷键API集成
   - 应用唤醒/隐藏功能

2. **快捷键配置持久化**
   - 配置文件读写
   - 默认配置设定
   - 配置迁移机制

### 第四阶段：设置界面与优化 (Day 2 下午)
1. **快捷键设置界面**
   - 快捷键配置面板
   - 冲突检测和提示
   - 自定义快捷键编辑

2. **用户体验优化**
   - 快捷键提示显示
   - 帮助文档集成
   - 错误处理和反馈

3. **测试与调试**
   - 快捷键功能测试
   - 平台兼容性测试
   - 性能优化

## 默认快捷键配置

### macOS 默认配置
```typescript
const macOSShortcuts: Record<string, ShortcutConfig> = {
  // Tab管理
  'tab.new': { keys: ['cmd', 't'], action: 'createTab' },
  'tab.close': { keys: ['cmd', 'w'], action: 'closeTab' },
  'tab.next': { keys: ['cmd', 'shift', ']'], action: 'nextTab' },
  'tab.prev': { keys: ['cmd', 'shift', '['], action: 'prevTab' },
  'tab.switch': { keys: ['cmd', '1-9'], action: 'switchToTab' },

  // 终端操作
  'terminal.copy': { keys: ['cmd', 'c'], action: 'copySelection' },
  'terminal.paste': { keys: ['cmd', 'v'], action: 'pasteText' },
  'terminal.clear': { keys: ['cmd', 'k'], action: 'clearTerminal' },
  'terminal.interrupt': { keys: ['ctrl', 'c'], action: 'interruptProcess' },

  // AI功能
  'ai.chat': { keys: ['cmd', '/'], action: 'openAIChat' },
  'ai.command': { keys: ['cmd', 'enter'], action: 'executeAICommand' },

  // 应用功能
  'app.settings': { keys: ['cmd', ','], action: 'openSettings' },
  'app.theme': { keys: ['cmd', 'shift', 't'], action: 'toggleTheme' },

  // 全局快捷键
  'global.toggle': { keys: ['cmd', 'space'], action: 'toggleApp', global: true },
  'global.newWindow': { keys: ['cmd', 'shift', 'n'], action: 'newWindow', global: true }
}
```

### Windows/Linux 默认配置
```typescript
const windowsLinuxShortcuts: Record<string, ShortcutConfig> = {
  // 将所有 'cmd' 替换为 'ctrl'
  // 其他逻辑保持一致
}
```

## 测试计划

### 功能测试
1. **快捷键注册和执行**
   - 快捷键注册机制测试
   - 按键组合识别测试
   - 动作执行准确性测试

2. **平台兼容性**
   - macOS快捷键测试
   - Windows快捷键测试
   - Linux快捷键测试

3. **冲突检测**
   - 快捷键冲突识别
   - 冲突解决机制
   - 系统快捷键避让

### 用户体验测试
1. **响应性能**
   - 快捷键响应延迟
   - 大量快捷键注册性能
   - 内存使用优化

2. **配置功能**
   - 自定义配置保存
   - 配置重置功能
   - 导入导出功能

## 交付物

### 代码文件
- `src/stores/shortcutStore.ts` - 快捷键状态管理
- `src/components/shortcuts/` - 快捷键相关组件
- `src/hooks/useShortcuts.ts` - 快捷键Hook
- `src/hooks/useTerminalShortcuts.ts` - 终端快捷键Hook
- `src/hooks/useAppShortcuts.ts` - 应用快捷键Hook
- `src/utils/shortcutUtils.ts` - 快捷键工具函数
- `src/constants/shortcuts.ts` - 默认快捷键配置
- `src-tauri/src/system/shortcuts.rs` - 全局快捷键管理

### 集成更新
- 更新 `src/components/settings/sections/ShortcutSettings.tsx` - 快捷键设置界面
- 扩展现有Hook：`useTabShortcuts.ts`、`useChatShortcuts.ts`
- 更新主应用组件集成快捷键系统

### 测试文件
- `src/hooks/__tests__/useShortcuts.test.ts`
- `src/utils/__tests__/shortcutUtils.test.ts`
- 集成测试用例

## 验收标准
1. ✅ 所有默认快捷键功能正常工作
2. ✅ 快捷键自定义配置功能完整
3. ✅ 平台适配正确（macOS/Windows/Linux）
4. ✅ 快捷键冲突检测和解决机制有效
5. ✅ 全局快捷键注册和注销正常
6. ✅ 配置持久化和迁移功能正常
7. ✅ 性能符合要求，无明显延迟
8. ✅ 用户界面友好，易于配置和使用
