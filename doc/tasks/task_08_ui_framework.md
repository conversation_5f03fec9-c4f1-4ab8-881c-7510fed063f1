# Task 3.1: 基础 UI 框架

## 📋 任务概述

**任务编号**: Task 3.1
**任务名称**: 基础 UI 框架
**任务状态**: ✅ 已完成
**优先级**: 高
**预计时间**: 2 天
**依赖任务**: Task 1.3 (项目架构设计)

## 🎯 任务目标

搭建完整的 React + TypeScript + Tailwind CSS 用户界面框架，建立组件系统、状态管理、路由结构和基础布局，为后续的终端显示组件和其他 UI 功能提供坚实的基础。

## 📝 详细需求

### 3.1.1 React 应用架构
- **组件层次结构**: 建立清晰的组件层次和文件组织
- **TypeScript 集成**: 完善的类型定义和类型安全
- **状态管理**: 使用 Zustand 进行全局状态管理
- **路由系统**: 使用 React Router 实现页面导航
- **错误边界**: 添加错误边界处理组件崩溃

### 3.1.2 Tailwind CSS 样式系统
- **主题配置**: 配置自定义颜色、字体、间距等主题变量
- **组件样式**: 建立一致的组件样式规范
- **响应式设计**: 支持不同屏幕尺寸的响应式布局
- **暗色主题**: 预设暗色和亮色主题切换基础
- **CSS 优化**: 配置 PurgeCSS 和样式压缩

### 3.1.3 基础组件库
- **布局组件**: Header、Sidebar、Main、Footer 等布局组件
- **通用组件**: Button、Input、Modal、Toast 等基础 UI 组件
- **图标系统**: 集成 Lucide React 图标库
- **加载状态**: Loading、Skeleton、Spinner 等加载组件
- **表单组件**: Form、Field、Validation 等表单处理组件

### 3.1.4 开发工具配置
- **热重载**: 配置 Vite 热重载和快速刷新
- **代码分割**: 实现路由级别的代码分割
- **开发服务器**: 配置开发代理和 API Mock
- **构建优化**: 优化生产构建性能和体积
- **PWA 支持**: 基础的 PWA 配置（可选）

## 🛠️ 技术实现

### 3.1.1 项目结构设计

```
src/
├── components/          # 组件目录
│   ├── ui/             # 基础 UI 组件
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Modal.tsx
│   │   └── index.ts
│   ├── layout/         # 布局组件
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   ├── MainLayout.tsx
│   │   └── index.ts
│   └── features/       # 功能组件
│       └── terminal/   # 终端相关组件
├── hooks/              # 自定义 Hooks
│   ├── useTheme.ts
│   ├── useKeyboard.ts
│   └── useTerminal.ts
├── stores/             # 状态管理
│   ├── appStore.ts
│   ├── terminalStore.ts
│   ├── settingsStore.ts
│   └── index.ts
├── types/              # 类型定义
│   ├── ui.ts
│   ├── terminal.ts
│   └── index.ts
├── utils/              # 工具函数
│   ├── theme.ts
│   ├── constants.ts
│   └── helpers.ts
└── styles/             # 样式文件
    ├── globals.css
    ├── components.css
    └── themes.css
```

### 3.1.2 核心组件实现

#### 主布局组件
```tsx
// src/components/layout/MainLayout.tsx
import React from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { useAppStore } from '@/stores';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { sidebarVisible, theme } = useAppStore();

  return (
    <div className={`app-layout ${theme} flex h-screen bg-background text-foreground`}>
      <Header />
      <div className="flex flex-1 overflow-hidden">
        {sidebarVisible && (
          <Sidebar className="w-64 border-r border-border" />
        )}
        <main className="flex-1 overflow-hidden">
          {children}
        </main>
      </div>
    </div>
  );
};
```

#### 基础 UI 组件
```tsx
// src/components/ui/Button.tsx
import React from 'react';
import { cn } from '@/utils/helpers';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  className,
  variant = 'default',
  size = 'default',
  children,
  ...props
}) => {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
        'disabled:opacity-50 disabled:pointer-events-none',
        {
          'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',
          'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',
          'border border-input hover:bg-accent hover:text-accent-foreground': variant === 'outline',
          'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
          'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',
        },
        {
          'h-10 py-2 px-4': size === 'default',
          'h-9 px-3 rounded-md': size === 'sm',
          'h-11 px-8 rounded-md': size === 'lg',
          'h-10 w-10': size === 'icon',
        },
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};
```

### 3.1.3 状态管理配置

#### 应用状态 Store
```tsx
// src/stores/appStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface AppState {
  // UI 状态
  theme: 'light' | 'dark';
  sidebarVisible: boolean;

  // 应用状态
  isLoading: boolean;
  currentView: 'terminal' | 'settings' | 'ai-chat';

  // 窗口状态
  windowSize: {
    width: number;
    height: number;
  };

  // 操作方法
  setTheme: (theme: 'light' | 'dark') => void;
  toggleSidebar: () => void;
  setCurrentView: (view: AppState['currentView']) => void;
  setLoading: (loading: boolean) => void;
  updateWindowSize: (size: { width: number; height: number }) => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      // 初始状态
      theme: 'dark',
      sidebarVisible: true,
      isLoading: false,
      currentView: 'terminal',
      windowSize: { width: 1200, height: 800 },

      // 操作方法
      setTheme: (theme) => set({ theme }),
      toggleSidebar: () => set((state) => ({ sidebarVisible: !state.sidebarVisible })),
      setCurrentView: (currentView) => set({ currentView }),
      setLoading: (isLoading) => set({ isLoading }),
      updateWindowSize: (windowSize) => set({ windowSize }),
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({
        theme: state.theme,
        sidebarVisible: state.sidebarVisible,
        currentView: state.currentView,
      }),
    }
  )
);
```

### 3.1.4 Tailwind 主题配置

#### tailwind.config.js 扩展
```javascript
// tailwind.config.js
module.exports = {
  darkMode: ['class'],
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        // 终端相关颜色
        terminal: {
          black: '#000000',
          red: '#ff5555',
          green: '#50fa7b',
          yellow: '#f1fa8c',
          blue: '#bd93f9',
          magenta: '#ff79c6',
          cyan: '#8be9fd',
          white: '#f8f8f2',
        },
      },
      fontFamily: {
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.15s ease-in-out',
        'slide-down': 'slideDown 0.15s ease-out',
        'slide-up': 'slideUp 0.15s ease-out',
      },
      keyframes: {
        fadeIn: {
          from: { opacity: '0' },
          to: { opacity: '1' },
        },
        slideDown: {
          from: { transform: 'translateY(-10px)', opacity: '0' },
          to: { transform: 'translateY(0)', opacity: '1' },
        },
        slideUp: {
          from: { transform: 'translateY(10px)', opacity: '0' },
          to: { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};
```

#### CSS 变量定义
```css
/* src/styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}
```

## ✅ 验收标准

### 3.1.1 功能验收
- [ ] React 应用正常启动和运行
- [ ] 主题切换功能正常工作
- [ ] 响应式布局在不同屏幕尺寸下正常显示
- [ ] 所有基础 UI 组件渲染正确
- [ ] 状态管理正常工作，数据持久化成功
- [ ] 路由导航功能正常
- [ ] 错误边界能正确处理组件崩溃

### 3.1.2 性能验收
- [ ] 首次加载时间 < 2 秒
- [ ] 主题切换响应时间 < 100ms
- [ ] 组件重新渲染次数合理
- [ ] 生产构建体积优化合理
- [ ] 热重载功能正常工作

### 3.1.3 代码质量验收
- [ ] TypeScript 类型检查通过
- [ ] ESLint 检查无错误
- [ ] Prettier 格式化一致
- [ ] 组件结构清晰，职责单一
- [ ] 样式规范一致

### 3.1.4 可维护性验收
- [ ] 组件文档完整
- [ ] 目录结构清晰
- [ ] 代码注释充足
- [ ] 组件可复用性好
- [ ] 易于扩展新功能

## 🧪 测试计划

### 3.1.1 单元测试
```typescript
// src/components/ui/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../Button';

describe('Button Component', () => {
  test('renders button with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('applies correct variant classes', () => {
    render(<Button variant="destructive">Delete</Button>);
    const button = screen.getByText('Delete');
    expect(button).toHaveClass('bg-destructive');
  });

  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### 3.1.2 集成测试
```typescript
// src/components/layout/__tests__/MainLayout.test.tsx
import { render, screen } from '@testing-library/react';
import { MainLayout } from '../MainLayout';
import { useAppStore } from '@/stores';

jest.mock('@/stores');

describe('MainLayout Component', () => {
  test('renders layout with sidebar when visible', () => {
    (useAppStore as jest.Mock).mockReturnValue({
      sidebarVisible: true,
      theme: 'dark',
    });

    render(
      <MainLayout>
        <div>Main Content</div>
      </MainLayout>
    );

    expect(screen.getByText('Main Content')).toBeInTheDocument();
    // 验证侧边栏是否存在
  });
});
```

### 3.1.3 视觉回归测试
```typescript
// src/__tests__/visual.test.tsx
import { render } from '@testing-library/react';
import { Button } from '@/components/ui/Button';

describe('Visual Regression Tests', () => {
  test('Button variants render correctly', () => {
    const variants = ['default', 'destructive', 'outline', 'secondary', 'ghost'] as const;

    variants.forEach(variant => {
      const { container } = render(
        <Button variant={variant}>Test Button</Button>
      );
      expect(container.firstChild).toMatchSnapshot(`button-${variant}`);
    });
  });
});
```

## 📚 相关文档

- [React 18 文档](https://react.dev/)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [Zustand 状态管理](https://github.com/pmndrs/zustand)
- [Vite 构建工具](https://vitejs.dev/)
- [Tauri 前端集成](https://tauri.app/v2/guides/frontend/)

## 🔄 任务依赖

### 前置依赖
- ✅ Task 1.3: 项目架构设计 - 提供整体架构指导

### 后续任务
- ⏳ Task 3.2: 终端显示组件 - 基于此框架开发终端组件
- ⏳ Task 3.3: 主题系统 - 扩展主题配置和切换逻辑
- ⏳ Task 3.4: 设置界面 - 使用基础组件构建设置页面

## 📝 开发日志

### 2024-12-XX
- 创建任务文档
- 开始基础框架搭建

---

**最后更新**: 2024-12-XX
**负责人**: AI Assistant
**审核人**: 项目负责人
