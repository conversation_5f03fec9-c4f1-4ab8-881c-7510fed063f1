# Task 2.1: PTY 终端实现

## 任务概述
- **任务ID**: Task 2.1
- **任务名称**: PTY 终端实现
- **优先级**: 高
- **预计时间**: 3 天
- **负责人**: 待分配
- **状态**: 🚧 进行中

## 任务描述
实现基于 Rust 的伪终端 (PTY) 功能，这是 TAgent 的核心组件，负责创建、管理和与系统 Shell 进程通信。

## 具体任务

### 2.1.1 PTY 基础实现 (1 天)
- 集成 PTY 相关依赖
- 实现基础 PTY 创建和管理
- 跨平台兼容性处理

#### 依赖配置

**Cargo.toml 新增依赖**:
```toml
[dependencies]
# PTY 相关
portable-pty = "0.8"
async-trait = "0.1"
futures = "0.3"

# 系统相关
nix = { version = "0.27", features = ["process", "signal"] }
winapi = { version = "0.3", features = ["processthreadsapi", "handleapi"] }

# 异步运行时
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
uuid = { version = "1.0", features = ["v4"] }
```

#### PTY 管理器实现

**src-tauri/src/terminal/pty.rs**:
```rust
use portable_pty::{CommandBuilder, PtySize, PtySystem, Child, MasterPty};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::sync::mpsc;
use uuid::Uuid;
use anyhow::Result;

#[derive(Debug, Clone)]
pub struct PtyConfig {
    pub shell: String,
    pub args: Vec<String>,
    pub cwd: Option<String>,
    pub env: HashMap<String, String>,
    pub size: PtySize,
}

impl Default for PtyConfig {
    fn default() -> Self {
        Self {
            shell: get_default_shell(),
            args: vec![],
            cwd: None,
            env: HashMap::new(),
            size: PtySize {
                rows: 24,
                cols: 80,
                pixel_width: 0,
                pixel_height: 0,
            },
        }
    }
}

pub struct PtyInstance {
    pub id: String,
    pub master: Box<dyn MasterPty + Send>,
    pub child: Box<dyn Child + Send + Sync>,
    pub config: PtyConfig,
}

pub struct PtyManager {
    instances: Arc<Mutex<HashMap<String, PtyInstance>>>,
    pty_system: PtySystem,
}

impl PtyManager {
    pub fn new() -> Self {
        Self {
            instances: Arc::new(Mutex::new(HashMap::new())),
            pty_system: PtySystem::default(),
        }
    }

    pub async fn create_pty(&self, config: PtyConfig) -> Result<String> {
        let id = Uuid::new_v4().to_string();

        // 创建 PTY 对
        let pty_pair = self.pty_system.openpty(config.size)?;

        // 构建命令
        let mut cmd = CommandBuilder::new(&config.shell);
        cmd.args(&config.args);

        if let Some(cwd) = &config.cwd {
            cmd.cwd(cwd);
        }

        for (key, value) in &config.env {
            cmd.env(key, value);
        }

        // 启动子进程
        let child = pty_pair.slave.spawn_command(cmd)?;

        let instance = PtyInstance {
            id: id.clone(),
            master: pty_pair.master,
            child,
            config,
        };

        self.instances.lock().unwrap().insert(id.clone(), instance);

        Ok(id)
    }

    pub async fn write_to_pty(&self, pty_id: &str, data: &[u8]) -> Result<()> {
        let instances = self.instances.lock().unwrap();
        if let Some(instance) = instances.get(pty_id) {
            instance.master.write_all(data)?;
        }
        Ok(())
    }

    pub async fn resize_pty(&self, pty_id: &str, size: PtySize) -> Result<()> {
        let instances = self.instances.lock().unwrap();
        if let Some(instance) = instances.get(pty_id) {
            instance.master.resize(size)?;
        }
        Ok(())
    }

    pub async fn kill_pty(&self, pty_id: &str) -> Result<()> {
        let mut instances = self.instances.lock().unwrap();
        if let Some(mut instance) = instances.remove(pty_id) {
            instance.child.kill()?;
            instance.child.wait()?;
        }
        Ok(())
    }
}

fn get_default_shell() -> String {
    #[cfg(target_os = "windows")]
    {
        std::env::var("COMSPEC").unwrap_or_else(|_| "cmd.exe".to_string())
    }

    #[cfg(not(target_os = "windows"))]
    {
        std::env::var("SHELL").unwrap_or_else(|_| "/bin/bash".to_string())
    }
}
```

### 2.1.2 异步 IO 处理 (1 天)
- 实现异步读写功能
- 处理终端输出流
- 实现数据缓冲和处理

#### 异步 IO 管理器

**src-tauri/src/terminal/io_handler.rs**:
```rust
use std::io::{Read, Write};
use tokio::sync::mpsc;
use tokio::time::{interval, Duration};
use anyhow::Result;

pub struct IoHandler {
    output_tx: mpsc::UnboundedSender<Vec<u8>>,
    input_rx: mpsc::UnboundedReceiver<Vec<u8>>,
}

impl IoHandler {
    pub fn new() -> (Self, mpsc::UnboundedReceiver<Vec<u8>>, mpsc::UnboundedSender<Vec<u8>>) {
        let (output_tx, output_rx) = mpsc::unbounded_channel();
        let (input_tx, input_rx) = mpsc::unbounded_channel();

        let handler = Self {
            output_tx,
            input_rx,
        };

        (handler, output_rx, input_tx)
    }

    pub async fn start_io_loop(&mut self, pty_manager: Arc<PtyManager>, pty_id: String) -> Result<()> {
        let mut read_buffer = vec![0u8; 4096];
        let mut interval = interval(Duration::from_millis(10));

        loop {
            tokio::select! {
                // 处理从 PTY 读取的数据
                _ = interval.tick() => {
                    if let Ok(bytes_read) = self.read_from_pty(&pty_manager, &pty_id, &mut read_buffer).await {
                        if bytes_read > 0 {
                            let data = read_buffer[..bytes_read].to_vec();
                            if self.output_tx.send(data).is_err() {
                                break;
                            }
                        }
                    }
                }

                // 处理输入数据
                Some(input_data) = self.input_rx.recv() => {
                    if let Err(_) = pty_manager.write_to_pty(&pty_id, &input_data).await {
                        break;
                    }
                }
            }
        }

        Ok(())
    }

    async fn read_from_pty(&self, _pty_manager: &PtyManager, _pty_id: &str, buffer: &mut [u8]) -> Result<usize> {
        // 实际实现中需要从 PTY master 读取数据
        // 这里是示例代码框架
        Ok(0)
    }
}
```

### 2.1.3 进程生命周期管理 (0.5 天)
- 实现进程状态监控
- 处理进程退出和清理
- 实现进程信号处理

#### 进程管理器

**src-tauri/src/terminal/process.rs**:
```rust
use std::process::{ExitStatus};
use tokio::sync::watch;
use anyhow::Result;

#[derive(Debug, Clone)]
pub enum ProcessState {
    Running,
    Exited(i32),
    Terminated(String),
    Error(String),
}

pub struct ProcessManager {
    state_tx: watch::Sender<ProcessState>,
    state_rx: watch::Receiver<ProcessState>,
}

impl ProcessManager {
    pub fn new() -> Self {
        let (state_tx, state_rx) = watch::channel(ProcessState::Running);

        Self {
            state_tx,
            state_rx,
        }
    }

    pub async fn monitor_process(&self, pty_id: String, pty_manager: Arc<PtyManager>) -> Result<()> {
        tokio::spawn(async move {
            // 监控进程状态的逻辑
            // 当进程退出时更新状态
        });

        Ok(())
    }

    pub fn get_state_receiver(&self) -> watch::Receiver<ProcessState> {
        self.state_rx.clone()
    }

    pub async fn terminate_process(&self, signal: ProcessSignal) -> Result<()> {
        match signal {
            ProcessSignal::Term => {
                // 发送 SIGTERM 信号
            }
            ProcessSignal::Kill => {
                // 发送 SIGKILL 信号
            }
            ProcessSignal::Int => {
                // 发送 SIGINT 信号 (Ctrl+C)
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub enum ProcessSignal {
    Term,
    Kill,
    Int,
}
```

### 2.1.4 Tauri 命令接口 (0.5 天)
- 实现前端调用接口
- 添加错误处理
- 集成到主应用

#### Tauri 命令实现

**src-tauri/src/terminal/commands.rs**:
```rust
use tauri::{State, Window};
use serde::{Deserialize, Serialize};
use anyhow::Result;
use super::{PtyManager, PtyConfig};

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTerminalRequest {
    pub shell: Option<String>,
    pub cwd: Option<String>,
    pub env: Option<std::collections::HashMap<String, String>>,
    pub rows: Option<u16>,
    pub cols: Option<u16>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TerminalResponse {
    pub id: String,
    pub success: bool,
    pub message: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResizeRequest {
    pub rows: u16,
    pub cols: u16,
}

#[tauri::command]
pub async fn create_terminal(
    request: CreateTerminalRequest,
    pty_manager: State<'_, Arc<PtyManager>>,
    window: Window,
) -> Result<TerminalResponse, String> {
    let config = PtyConfig {
        shell: request.shell.unwrap_or_else(|| get_default_shell()),
        cwd: request.cwd,
        env: request.env.unwrap_or_default(),
        size: portable_pty::PtySize {
            rows: request.rows.unwrap_or(24),
            cols: request.cols.unwrap_or(80),
            pixel_width: 0,
            pixel_height: 0,
        },
        ..Default::default()
    };

    match pty_manager.create_pty(config).await {
        Ok(id) => {
            // 启动 IO 处理循环
            start_terminal_io_handler(id.clone(), pty_manager.inner().clone(), window).await;

            Ok(TerminalResponse {
                id,
                success: true,
                message: None,
            })
        }
        Err(e) => Err(format!("创建终端失败: {}", e)),
    }
}

#[tauri::command]
pub async fn write_to_terminal(
    terminal_id: String,
    data: String,
    pty_manager: State<'_, Arc<PtyManager>>,
) -> Result<bool, String> {
    match pty_manager.write_to_pty(&terminal_id, data.as_bytes()).await {
        Ok(_) => Ok(true),
        Err(e) => Err(format!("写入终端失败: {}", e)),
    }
}

#[tauri::command]
pub async fn resize_terminal(
    terminal_id: String,
    request: ResizeRequest,
    pty_manager: State<'_, Arc<PtyManager>>,
) -> Result<bool, String> {
    let size = portable_pty::PtySize {
        rows: request.rows,
        cols: request.cols,
        pixel_width: 0,
        pixel_height: 0,
    };

    match pty_manager.resize_pty(&terminal_id, size).await {
        Ok(_) => Ok(true),
        Err(e) => Err(format!("调整终端大小失败: {}", e)),
    }
}

#[tauri::command]
pub async fn kill_terminal(
    terminal_id: String,
    pty_manager: State<'_, Arc<PtyManager>>,
) -> Result<bool, String> {
    match pty_manager.kill_pty(&terminal_id).await {
        Ok(_) => Ok(true),
        Err(e) => Err(format!("关闭终端失败: {}", e)),
    }
}

async fn start_terminal_io_handler(
    terminal_id: String,
    pty_manager: Arc<PtyManager>,
    window: Window,
) {
    tokio::spawn(async move {
        // 启动 IO 处理循环
        // 将终端输出通过事件发送到前端
        let (mut io_handler, mut output_rx, _input_tx) = IoHandler::new();

        // 启动 IO 循环
        tokio::spawn(async move {
            io_handler.start_io_loop(pty_manager, terminal_id.clone()).await;
        });

        // 处理输出数据并发送事件到前端
        while let Some(output) = output_rx.recv().await {
            let output_str = String::from_utf8_lossy(&output);
            let _ = window.emit("terminal-output", serde_json::json!({
                "terminal_id": terminal_id,
                "data": output_str
            }));
        }
    });
}
```

## 验收标准

### 功能验收
- [ ] 能够成功创建 PTY 进程
- [ ] 支持基础的 Shell 命令执行
- [ ] 正确处理终端输入输出
- [ ] 支持终端窗口大小调整
- [ ] 能够正常关闭和清理进程

### 兼容性验收
- [ ] macOS 系统兼容性
- [ ] Windows 系统兼容性
- [ ] Linux 系统兼容性
- [ ] 多种 Shell 支持 (bash, zsh, fish, cmd, powershell)

### 性能验收
- [ ] PTY 创建时间 < 500ms
- [ ] 输入响应时间 < 50ms
- [ ] 内存使用合理 (< 50MB per terminal)
- [ ] CPU 占用率 < 5% (空闲状态)

### 稳定性验收
- [ ] 长时间运行稳定
- [ ] 异常情况正确处理
- [ ] 资源正确释放
- [ ] 无内存泄漏

## 测试计划

### 2.1.5 单元测试 (1 小时)

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_create_pty() {
        let manager = PtyManager::new();
        let config = PtyConfig::default();

        let result = manager.create_pty(config).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_pty_write_read() {
        let manager = PtyManager::new();
        let config = PtyConfig::default();

        let pty_id = manager.create_pty(config).await.unwrap();

        // 测试写入命令
        let write_result = manager.write_to_pty(&pty_id, b"echo hello\n").await;
        assert!(write_result.is_ok());

        // 清理
        let _ = manager.kill_pty(&pty_id).await;
    }

    #[tokio::test]
    async fn test_pty_resize() {
        let manager = PtyManager::new();
        let config = PtyConfig::default();

        let pty_id = manager.create_pty(config).await.unwrap();

        let new_size = PtySize {
            rows: 30,
            cols: 100,
            pixel_width: 0,
            pixel_height: 0,
        };

        let resize_result = manager.resize_pty(&pty_id, new_size).await;
        assert!(resize_result.is_ok());

        // 清理
        let _ = manager.kill_pty(&pty_id).await;
    }
}
```

## 风险与依赖

### 技术风险
- **PTY 库兼容性**: portable-pty 在不同系统的表现差异
- **异步性能**: 大量并发终端的性能影响
- **内存管理**: 长时间运行的内存泄漏风险
- **进程管理**: 异常终止时的资源清理

### 依赖关系
- **前置任务**: Task 1.3 项目架构设计
- **并行任务**: 可与前端 UI 开发并行
- **后续任务**: Task 2.2 Shell 进程管理

### 外部依赖
- portable-pty crate 稳定性
- 系统 PTY 功能支持
- Rust 异步运行时性能

## 调试和故障排除

### 常见问题
1. **PTY 创建失败**: 检查系统权限和 Shell 路径
2. **输出乱码**: 确认字符编码设置
3. **进程僵死**: 检查信号处理和进程清理
4. **性能问题**: 优化 IO 缓冲区大小

### 调试工具
- Rust 调试器 (lldb/gdb)
- 系统进程监控工具
- 内存分析工具
- 性能分析工具

## 完成标志
- [ ] PTY 基础功能实现完成
- [ ] 异步 IO 处理稳定运行
- [ ] Tauri 命令接口测试通过
- [ ] 跨平台兼容性验证完成
- [ ] 单元测试覆盖率 > 80%
- [ ] 性能基准测试通过
- [ ] 代码审查完成

---

**创建时间**: 2024-01-XX
**最后更新**: 2024-01-XX
**前置任务**: Task 1.3 项目架构设计
**下一个任务**: Task 2.2 Shell 进程管理
