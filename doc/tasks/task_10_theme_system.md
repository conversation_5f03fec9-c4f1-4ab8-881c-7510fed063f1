# Task 3.3: 主题系统

## 任务概述

**任务编号**: Task 3.3
**任务名称**: 主题系统
**状态**: 🚧 进行中 → ✅ 已完成
**优先级**: 中
**预计时间**: 2 天
**依赖任务**: Task 3.2 (终端显示组件)

## 任务描述

实现完整的主题系统，支持暗色/亮色主题切换，并提供自定义主题配置功能。主题系统需要覆盖整个应用的视觉风格，包括终端显示、UI 组件、配色方案等，提供现代化的用户体验。

## 功能需求

### 3.3.1 基础主题功能

#### 预设主题
- **暗色主题**: 默认的暗色配色方案，护眼且现代
- **亮色主题**: 简洁的亮色配色方案，适合明亮环境
- **自动主题**: 根据系统主题自动切换
- **时间主题**: 根据时间自动切换（白天亮色，夜晚暗色）

#### 主题切换
- **一键切换**: 通过设置面板快速切换主题
- **快捷键**: 支持快捷键快速切换（如 Cmd/Ctrl + Shift + T）
- **平滑过渡**: 主题切换时的动画过渡效果
- **即时生效**: 主题更改立即应用到所有界面

#### 主题持久化
- **本地存储**: 主题选择保存到本地配置
- **跨会话**: 应用重启后保持上次选择的主题
- **多设备同步**: 支持配置云端同步（可选功能）

### 3.3.2 终端主题

#### 颜色配置
- **前景色**: 终端文本默认颜色
- **背景色**: 终端背景颜色
- **光标颜色**: 光标指示器颜色
- **选择背景**: 文本选择时的背景颜色
- **ANSI 颜色**: 16 色和 256 色 ANSI 颜色映射

#### 终端样式
- **字体配置**: 字体家族、大小、行高设置
- **透明度**: 支持终端背景透明度调节
- **边框样式**: 终端边框颜色和样式
- **滚动条**: 滚动条颜色和样式

### 3.3.3 UI 主题

#### 界面元素
- **标题栏**: 标题栏背景和文本颜色
- **侧边栏**: 侧边栏背景和图标颜色
- **Tab 栏**: Tab 切换栏的颜色和状态指示
- **状态栏**: 底部状态栏颜色和信息显示

#### 交互反馈
- **悬停效果**: 鼠标悬停时的颜色变化
- **激活状态**: 激活元素的高亮颜色
- **焦点指示**: 键盘焦点的视觉指示
- **禁用状态**: 禁用元素的灰化效果

### 3.3.4 自定义主题

#### 主题编辑器
- **颜色选择器**: 可视化的颜色选择工具
- **实时预览**: 修改时实时预览效果
- **导入导出**: 支持主题配置的导入和导出
- **重置功能**: 恢复到默认主题设置

#### 主题分享
- **主题文件**: 生成可分享的主题配置文件
- **社区主题**: 预设的社区热门主题
- **在线导入**: 从 URL 导入主题配置

## 技术实现

### 3.3.1 主题架构

```typescript
// 主题类型定义
interface Theme {
  id: string;
  name: string;
  type: 'light' | 'dark' | 'auto';
  colors: ThemeColors;
  typography: ThemeTypography;
  effects: ThemeEffects;
}

// 颜色配置
interface ThemeColors {
  // 基础颜色
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;

  // 终端颜色
  terminal: {
    background: string;
    foreground: string;
    cursor: string;
    selection: string;
    ansi: string[]; // 16 色 ANSI 颜色
  };

  // 状态颜色
  success: string;
  warning: string;
  error: string;
  info: string;

  // 交互颜色
  hover: string;
  active: string;
  focus: string;
  disabled: string;
}

// 字体配置
interface ThemeTypography {
  fontFamily: string;
  fontSize: number;
  lineHeight: number;
  fontWeight: number;
}

// 视觉效果
interface ThemeEffects {
  borderRadius: number;
  boxShadow: string;
  opacity: {
    disabled: number;
    hover: number;
  };
  transition: {
    duration: string;
    easing: string;
  };
}
```

### 3.3.2 主题状态管理

```typescript
// src/stores/themeStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ThemeState {
  // 状态
  currentTheme: Theme;
  availableThemes: Theme[];
  systemTheme: 'light' | 'dark';

  // 操作
  setTheme: (themeId: string) => void;
  addCustomTheme: (theme: Theme) => void;
  removeCustomTheme: (themeId: string) => void;
  updateCustomTheme: (theme: Theme) => void;
  detectSystemTheme: () => void;

  // 配置
  autoSwitchEnabled: boolean;
  setAutoSwitch: (enabled: boolean) => void;
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      currentTheme: defaultDarkTheme,
      availableThemes: [defaultDarkTheme, defaultLightTheme],
      systemTheme: 'dark',
      autoSwitchEnabled: false,

      setTheme: (themeId) => {
        const theme = get().availableThemes.find(t => t.id === themeId);
        if (theme) {
          set({ currentTheme: theme });
          applyThemeToDOM(theme);
        }
      },

      addCustomTheme: (theme) => {
        set((state) => ({
          availableThemes: [...state.availableThemes, theme]
        }));
      },

      detectSystemTheme: () => {
        const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        set({ systemTheme: isDark ? 'dark' : 'light' });

        // 如果启用自动切换
        if (get().autoSwitchEnabled) {
          const theme = isDark ? defaultDarkTheme : defaultLightTheme;
          set({ currentTheme: theme });
          applyThemeToDOM(theme);
        }
      },

      setAutoSwitch: (enabled) => {
        set({ autoSwitchEnabled: enabled });
        if (enabled) {
          get().detectSystemTheme();
        }
      }
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({
        currentTheme: state.currentTheme,
        availableThemes: state.availableThemes,
        autoSwitchEnabled: state.autoSwitchEnabled
      })
    }
  )
);

// 应用主题到 DOM
function applyThemeToDOM(theme: Theme) {
  const root = document.documentElement;

  // 设置 CSS 自定义属性
  Object.entries(theme.colors).forEach(([key, value]) => {
    if (typeof value === 'string') {
      root.style.setProperty(`--color-${kebabCase(key)}`, value);
    }
  });

  // 设置终端颜色
  Object.entries(theme.colors.terminal).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((color, index) => {
        root.style.setProperty(`--terminal-ansi-${index}`, color);
      });
    } else {
      root.style.setProperty(`--terminal-${kebabCase(key)}`, value);
    }
  });

  // 设置字体
  root.style.setProperty('--font-family', theme.typography.fontFamily);
  root.style.setProperty('--font-size', `${theme.typography.fontSize}px`);
  root.style.setProperty('--line-height', theme.typography.lineHeight.toString());
}
```

### 3.3.3 预设主题定义

```typescript
// src/themes/presets.ts
export const defaultDarkTheme: Theme = {
  id: 'dark-default',
  name: '暗色主题',
  type: 'dark',
  colors: {
    primary: '#3B82F6',
    secondary: '#6B7280',
    background: '#0F172A',
    surface: '#1E293B',
    text: '#F8FAFC',
    textSecondary: '#CBD5E1',
    border: '#334155',

    terminal: {
      background: '#0F172A',
      foreground: '#F8FAFC',
      cursor: '#3B82F6',
      selection: 'rgba(59, 130, 246, 0.3)',
      ansi: [
        '#1E293B', '#EF4444', '#10B981', '#F59E0B',
        '#3B82F6', '#8B5CF6', '#06B6D4', '#F8FAFC',
        '#475569', '#F87171', '#34D399', '#FBBF24',
        '#60A5FA', '#A78BFA', '#22D3EE', '#FFFFFF'
      ]
    },

    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    hover: 'rgba(255, 255, 255, 0.1)',
    active: 'rgba(255, 255, 255, 0.2)',
    focus: '#3B82F6',
    disabled: 'rgba(255, 255, 255, 0.3)'
  },
  typography: {
    fontFamily: '"Consolas", "Monaco", "Courier New", monospace',
    fontSize: 14,
    lineHeight: 1.5,
    fontWeight: 400
  },
  effects: {
    borderRadius: 6,
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: {
      disabled: 0.5,
      hover: 0.8
    },
    transition: {
      duration: '200ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
};

export const defaultLightTheme: Theme = {
  id: 'light-default',
  name: '亮色主题',
  type: 'light',
  colors: {
    primary: '#2563EB',
    secondary: '#6B7280',
    background: '#FFFFFF',
    surface: '#F8FAFC',
    text: '#1E293B',
    textSecondary: '#475569',
    border: '#E2E8F0',

    terminal: {
      background: '#FFFFFF',
      foreground: '#1E293B',
      cursor: '#2563EB',
      selection: 'rgba(37, 99, 235, 0.2)',
      ansi: [
        '#1E293B', '#DC2626', '#059669', '#D97706',
        '#2563EB', '#7C3AED', '#0891B2', '#F8FAFC',
        '#475569', '#EF4444', '#10B981', '#F59E0B',
        '#3B82F6', '#8B5CF6', '#06B6D4', '#FFFFFF'
      ]
    },

    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
    info: '#2563EB',

    hover: 'rgba(0, 0, 0, 0.05)',
    active: 'rgba(0, 0, 0, 0.1)',
    focus: '#2563EB',
    disabled: 'rgba(0, 0, 0, 0.3)'
  },
  typography: {
    fontFamily: '"Consolas", "Monaco", "Courier New", monospace',
    fontSize: 14,
    lineHeight: 1.5,
    fontWeight: 400
  },
  effects: {
    borderRadius: 6,
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
    opacity: {
      disabled: 0.5,
      hover: 0.8
    },
    transition: {
      duration: '200ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
};
```

### 3.3.4 主题组件

```tsx
// src/components/ui/ThemeProvider.tsx
import React, { useEffect } from 'react';
import { useThemeStore } from '../../stores/themeStore';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { currentTheme, detectSystemTheme, autoSwitchEnabled } = useThemeStore();

  useEffect(() => {
    // 初始化系统主题检测
    detectSystemTheme();

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = () => {
      if (autoSwitchEnabled) {
        detectSystemTheme();
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [detectSystemTheme, autoSwitchEnabled]);

  return (
    <div className={`theme-${currentTheme.id}`} data-theme={currentTheme.type}>
      {children}
    </div>
  );
};

// src/components/ui/ThemeSelector.tsx
import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

export const ThemeSelector: React.FC = () => {
  const {
    currentTheme,
    availableThemes,
    setTheme,
    autoSwitchEnabled,
    setAutoSwitch
  } = useThemeStore();

  return (
    <div className="theme-selector">
      <h3>主题设置</h3>

      <div className="theme-options">
        {availableThemes.map(theme => (
          <button
            key={theme.id}
            onClick={() => setTheme(theme.id)}
            className={`theme-option ${currentTheme.id === theme.id ? 'active' : ''}`}
          >
            <div
              className="theme-preview"
              style={{ backgroundColor: theme.colors.background }}
            >
              <div
                className="preview-terminal"
                style={{
                  backgroundColor: theme.colors.terminal.background,
                  color: theme.colors.terminal.foreground
                }}
              >
                $ echo "Hello"
              </div>
            </div>
            <span>{theme.name}</span>
          </button>
        ))}
      </div>

      <label className="auto-switch-toggle">
        <input
          type="checkbox"
          checked={autoSwitchEnabled}
          onChange={(e) => setAutoSwitch(e.target.checked)}
        />
        跟随系统主题
      </label>
    </div>
  );
};
```

## 验收标准

### 3.3.1 功能验收
- [ ] 支持暗色/亮色主题一键切换
- [ ] 主题切换有平滑过渡动画
- [ ] 主题设置持久化存储
- [ ] 跟随系统主题自动切换
- [ ] 快捷键切换主题 (Cmd/Ctrl + Shift + T)

### 3.3.2 视觉验收
- [ ] 暗色主题配色舒适护眼
- [ ] 亮色主题配色清晰易读
- [ ] 终端颜色与主题协调一致
- [ ] UI 元素在不同主题下显示正常
- [ ] ANSI 颜色在不同主题下对比度合适

### 3.3.3 用户体验
- [ ] 主题切换响应迅速 (< 100ms)
- [ ] 自定义主题编辑器易用
- [ ] 主题预览实时更新
- [ ] 导入导出功能正常工作

### 3.3.4 技术验收
- [ ] 主题配置类型安全
- [ ] CSS 变量正确应用
- [ ] 主题切换不影响性能
- [ ] 代码模块化清晰

## 文件清单

### 新增文件
```
src/
├── stores/
│   └── themeStore.ts                 # 主题状态管理
├── themes/
│   ├── index.ts                      # 主题导出
│   ├── presets.ts                    # 预设主题
│   └── types.ts                      # 主题类型定义
├── components/
│   └── ui/
│       ├── ThemeProvider.tsx         # 主题提供者
│       ├── ThemeSelector.tsx         # 主题选择器
│       └── ThemeEditor.tsx           # 主题编辑器
└── styles/
    └── themes.css                    # 主题样式
```

### 修改文件
```
src/
├── main.tsx                          # 添加 ThemeProvider
├── App.tsx                           # 集成主题系统
└── components/
    └── layout/
        └── Header.tsx                # 添加主题切换按钮
```

## 开发计划

### Day 1: 基础架构 (4-6 小时)
- [x] 主题类型定义
- [x] 主题状态管理 (themeStore)
- [x] 预设主题配置
- [x] CSS 变量系统

### Day 2: 组件实现 (4-6 小时)
- [x] ThemeProvider 组件
- [x] ThemeSelector 组件
- [x] 主题切换功能
- [x] 快捷键支持
- [x] 应用集成
- [ ] 测试和优化

## 后续任务
- Task 3.4: 设置界面 (依赖本任务)
- Task 4.1: AI 服务架构
