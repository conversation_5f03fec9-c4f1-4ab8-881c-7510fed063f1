# Task 2.2: Shell 进程管理

## 📋 任务概述

**任务编号**: Task 2.2
**任务名称**: Shell 进程管理
**任务状态**: ✅ 已完成
**优先级**: 高
**预计时间**: 2 天
**依赖任务**: Task 2.1 (PTY 终端实现)

## 🎯 任务目标

实现 Shell 进程的启动、管理和通信机制，为终端应用提供稳定的 Shell 环境。

## 📝 详细需求

### 2.2.1 Shell 进程启动
- 支持多种 Shell 类型（bash、zsh、fish、PowerShell）
- 自动检测系统默认 Shell
- 支持自定义 Shell 路径和参数
- 处理 Shell 启动失败的情况

### 2.2.2 进程生命周期管理
- 进程启动和关闭
- 进程状态监控
- 异常退出处理
- 资源清理机制

### 2.2.3 进程通信
- 标准输入输出重定向
- 环境变量传递
- 工作目录设置
- 信号处理（SIGINT、SIGTERM等）

### 2.2.4 多实例管理
- 支持多个 Shell 实例
- 实例间隔离
- 实例状态跟踪
- 内存和资源优化

## 🛠️ 技术实现

### 2.2.1 核心模块设计

#### Shell 配置结构
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellConfig {
    pub shell_type: ShellType,
    pub executable_path: String,
    pub args: Vec<String>,
    pub env_vars: HashMap<String, String>,
    pub working_dir: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ShellType {
    Bash,
    Zsh,
    Fish,
    PowerShell,
    Cmd,
    Custom(String),
}
```

#### Shell 管理器
```rust
pub struct ShellManager {
    processes: HashMap<String, ShellProcess>,
    default_config: ShellConfig,
}

impl ShellManager {
    pub fn new() -> Self;
    pub fn create_shell(&mut self, id: String, config: ShellConfig) -> Result<()>;
    pub fn terminate_shell(&mut self, id: &str) -> Result<()>;
    pub fn get_shell_status(&self, id: &str) -> Option<ShellStatus>;
    pub fn send_input(&mut self, id: &str, input: &str) -> Result<()>;
    pub fn read_output(&mut self, id: &str) -> Result<String>;
}
```

#### Shell 进程包装
```rust
pub struct ShellProcess {
    process: Child,
    config: ShellConfig,
    status: ShellStatus,
    input_writer: BufWriter<ChildStdin>,
    output_reader: BufReader<ChildStdout>,
    error_reader: BufReader<ChildStderr>,
}

#[derive(Debug, Clone)]
pub enum ShellStatus {
    Starting,
    Running,
    Terminated,
    Error(String),
}
```

### 2.2.2 Shell 检测和配置

#### 系统 Shell 检测
```rust
pub fn detect_system_shell() -> Result<ShellConfig> {
    // 1. 检查 SHELL 环境变量
    // 2. 查找常见 Shell 路径
    // 3. 验证 Shell 可执行性
    // 4. 生成默认配置
}

pub fn get_available_shells() -> Vec<ShellConfig> {
    // 扫描系统中可用的 Shell
}
```

#### 平台特定实现
```rust
#[cfg(unix)]
mod unix_shell {
    pub fn get_shell_paths() -> Vec<String>;
    pub fn setup_shell_env(config: &ShellConfig) -> HashMap<String, String>;
}

#[cfg(windows)]
mod windows_shell {
    pub fn get_shell_paths() -> Vec<String>;
    pub fn setup_shell_env(config: &ShellConfig) -> HashMap<String, String>;
}
```

### 2.2.3 进程通信机制

#### 异步输入输出处理
```rust
impl ShellProcess {
    pub async fn send_command(&mut self, command: &str) -> Result<()> {
        self.input_writer.write_all(command.as_bytes()).await?;
        self.input_writer.write_all(b"\n").await?;
        self.input_writer.flush().await?;
        Ok(())
    }

    pub async fn read_output_async(&mut self) -> Result<String> {
        let mut buffer = String::new();
        self.output_reader.read_line(&mut buffer).await?;
        Ok(buffer)
    }

    pub fn send_signal(&mut self, signal: Signal) -> Result<()> {
        // 发送系统信号到进程
    }
}
```

#### 环境变量管理
```rust
pub fn setup_shell_environment(config: &ShellConfig) -> HashMap<String, String> {
    let mut env = std::env::vars().collect::<HashMap<_, _>>();

    // 添加自定义环境变量
    env.extend(config.env_vars.clone());

    // 设置 Shell 特定环境变量
    match config.shell_type {
        ShellType::Bash => {
            env.insert("BASH_ENV".to_string(), "~/.bashrc".to_string());
        },
        ShellType::Zsh => {
            env.insert("ZDOTDIR".to_string(), "~/.config/zsh".to_string());
        },
        _ => {}
    }

    env
}
```

### 2.2.4 错误处理和恢复

#### 错误定义
```rust
#[derive(Debug, thiserror::Error)]
pub enum ShellError {
    #[error("Shell executable not found: {path}")]
    ExecutableNotFound { path: String },

    #[error("Failed to start shell process: {reason}")]
    StartupFailed { reason: String },

    #[error("Shell process terminated unexpectedly")]
    UnexpectedTermination,

    #[error("Communication error: {message}")]
    CommunicationError { message: String },

    #[error("Permission denied: {details}")]
    PermissionDenied { details: String },
}
```

#### 恢复机制
```rust
impl ShellManager {
    pub fn handle_shell_crash(&mut self, id: &str) -> Result<()> {
        // 1. 记录崩溃信息
        // 2. 清理资源
        // 3. 尝试重启 Shell
        // 4. 通知前端
    }

    pub fn health_check(&self) -> Vec<(String, ShellStatus)> {
        // 检查所有 Shell 实例的健康状态
    }
}
```

## 🔗 Tauri 接口设计

### 2.2.1 命令接口

```rust
#[tauri::command]
async fn create_shell_instance(
    app: tauri::AppHandle,
    id: String,
    config: Option<ShellConfig>,
) -> Result<ShellConfig, String> {
    // 创建新的 Shell 实例
}

#[tauri::command]
async fn terminate_shell_instance(
    app: tauri::AppHandle,
    id: String,
) -> Result<(), String> {
    // 终止指定的 Shell 实例
}

#[tauri::command]
async fn send_shell_input(
    app: tauri::AppHandle,
    id: String,
    input: String,
) -> Result<(), String> {
    // 向 Shell 发送输入
}

#[tauri::command]
async fn get_shell_status(
    app: tauri::AppHandle,
    id: String,
) -> Result<ShellStatus, String> {
    // 获取 Shell 状态
}

#[tauri::command]
async fn get_available_shells() -> Result<Vec<ShellConfig>, String> {
    // 获取系统可用的 Shell 列表
}
```

### 2.2.2 事件接口

```rust
// Shell 输出事件
#[derive(Clone, serde::Serialize)]
struct ShellOutputEvent {
    id: String,
    output: String,
    output_type: OutputType, // stdout, stderr
}

// Shell 状态变化事件
#[derive(Clone, serde::Serialize)]
struct ShellStatusEvent {
    id: String,
    status: ShellStatus,
    timestamp: u64,
}

// 实现事件发送
impl ShellManager {
    fn emit_output(&self, app: &tauri::AppHandle, id: &str, output: &str, output_type: OutputType) {
        let event = ShellOutputEvent {
            id: id.to_string(),
            output: output.to_string(),
            output_type,
        };
        app.emit_all("shell-output", event).unwrap();
    }

    fn emit_status_change(&self, app: &tauri::AppHandle, id: &str, status: ShellStatus) {
        let event = ShellStatusEvent {
            id: id.to_string(),
            status,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };
        app.emit_all("shell-status", event).unwrap();
    }
}
```

## 📁 文件结构

```
src-tauri/src/terminal/
├── shell.rs           # Shell 管理器主模块
├── shell_config.rs    # Shell 配置定义
├── shell_process.rs   # Shell 进程包装
├── shell_detector.rs  # Shell 检测工具
└── platform/
    ├── unix.rs        # Unix 平台特定实现
    ├── windows.rs     # Windows 平台特定实现
    └── mod.rs         # 平台模块导出
```

## ✅ 完成标准

### 2.2.1 功能完成标准
- [ ] 成功启动和管理 bash/zsh/fish Shell
- [ ] 正确处理 Shell 进程生命周期
- [ ] 实现稳定的进程通信机制
- [ ] 支持多个 Shell 实例并发运行
- [ ] 处理各种异常情况和错误恢复

### 2.2.2 质量标准
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过
- [ ] 性能测试：Shell 启动时间 < 500ms
- [ ] 内存泄漏检查通过
- [ ] 代码审查通过

### 2.2.3 兼容性标准
- [ ] macOS 支持 (bash, zsh, fish)
- [ ] Linux 支持 (bash, zsh, fish)
- [ ] Windows 支持 (PowerShell, cmd)
- [ ] 不同 Shell 版本兼容性测试

## 🧪 测试计划

### 2.2.1 单元测试
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_shell_creation() {
        // 测试 Shell 实例创建
    }

    #[tokio::test]
    async fn test_shell_communication() {
        // 测试 Shell 进程通信
    }

    #[tokio::test]
    async fn test_shell_termination() {
        // 测试 Shell 进程终止
    }

    #[tokio::test]
    async fn test_error_handling() {
        // 测试错误处理机制
    }
}
```

### 2.2.2 集成测试
- Shell 与 PTY 模块的集成测试
- 多实例并发测试
- 异常情况下的恢复测试
- 性能和稳定性测试

## 📚 参考文档

- [Rust std::process 文档](https://doc.rust-lang.org/std/process/)
- [Tokio 异步编程指南](https://tokio.rs/tokio/tutorial)
- [Shell 标准和规范](https://pubs.opengroup.org/onlinepubs/9699919799/utilities/sh.html)
- [Tauri 进程管理最佳实践](https://tauri.app/v1/guides/features/command)

## 🔄 任务依赖

**前置任务**:
- ✅ Task 2.1: PTY 终端实现

**后续任务**:
- Task 2.3: 命令执行引擎
- Task 2.4: 输入输出处理

## 📅 时间计划

- **Day 1**: Shell 检测和配置模块实现
- **Day 2**: Shell 进程管理和通信机制实现
- **Day 3**: 错误处理和多实例管理
- **Day 4**: 测试和优化

## 🐛 已知问题

- 需要解决当前编译错误问题
- Windows 平台的 Shell 兼容性需要特别关注
- 异步进程通信的性能优化

## 📝 更新日志

- **2024-01-XX**: 任务文档创建
- **2024-01-XX**: 开始实现 Shell 检测模块
