# Task 2.4: 输入输出处理

## 📋 任务概述

**任务编号**: Task 2.4
**任务名称**: 输入输出处理
**任务状态**: ✅ 已完成
**优先级**: 高
**预计时间**: 2 天
**依赖任务**: Task 2.3 (命令执行引擎)

## 🎯 任务目标

实现完整的终端输入输出处理系统，支持实时数据流处理、特殊字符转义、ANSI 颜色渲染，以及前后端之间的高效通信，为用户提供流畅的终端交互体验。

## 📝 详细需求

### 2.4.1 输入处理系统
- **键盘输入捕获**: 处理用户键盘输入，包括特殊键
- **输入缓冲管理**: 实现输入缓冲区和行编辑功能
- **特殊键处理**: 支持 Ctrl+C、Ctrl+D、方向键、Tab 等
- **输入验证**: 过滤和验证用户输入，防止恶意代码注入
- **多字节字符**: 支持 UTF-8 编码和 Unicode 字符输入

### 2.4.2 输出处理系统
- **实时输出流**: 处理命令执行的实时输出
- **ANSI 序列解析**: 解析和渲染 ANSI 颜色和格式化代码
- **特殊字符处理**: 处理退格、换行、制表符等控制字符
- **输出缓冲优化**: 优化大量输出的渲染性能
- **错误输出分离**: 区分标准输出和错误输出

### 2.4.3 前后端通信
- **WebSocket 连接**: 建立高效的实时通信通道
- **消息协议设计**: 定义结构化的通信协议
- **数据序列化**: 高效的数据编码和解码
- **连接管理**: 处理连接断开、重连等异常情况
- **性能监控**: 监控通信延迟和数据传输量

### 2.4.4 会话状态管理
- **Terminal 状态**: 维护终端的当前状态信息
- **光标位置**: 跟踪和同步光标位置
- **屏幕缓冲区**: 管理虚拟屏幕缓冲区
- **历史记录**: 保存输入输出历史
- **窗口大小**: 处理终端窗口大小变化

## 🛠️ 技术实现

### 2.4.1 核心架构设计

#### IO 处理器结构
```rust
use std::collections::VecDeque;
use std::sync::{Arc, Mutex};
use tokio::sync::{mpsc, broadcast};
use serde::{Serialize, Deserialize};

#[derive(Debug)]
pub struct IOHandler {
    input_processor: InputProcessor,
    output_processor: OutputProcessor,
    comm_manager: CommunicationManager,
    session_state: Arc<Mutex<SessionState>>,
    config: IOConfig,
}

#[derive(Debug, Clone)]
pub struct IOConfig {
    pub buffer_size: usize,
    pub flush_interval_ms: u64,
    pub max_history_lines: usize,
    pub ansi_colors_enabled: bool,
    pub unicode_support: bool,
}

impl Default for IOConfig {
    fn default() -> Self {
        Self {
            buffer_size: 8192,
            flush_interval_ms: 16, // ~60fps
            max_history_lines: 10000,
            ansi_colors_enabled: true,
            unicode_support: true,
        }
    }
}
```

#### 会话状态定义
```rust
#[derive(Debug, Clone)]
pub struct SessionState {
    pub cursor_position: CursorPosition,
    pub terminal_size: TerminalSize,
    pub screen_buffer: ScreenBuffer,
    pub input_history: VecDeque<String>,
    pub output_history: VecDeque<OutputLine>,
    pub current_mode: TerminalMode,
}

#[derive(Debug, Clone, Copy)]
pub struct CursorPosition {
    pub row: u16,
    pub col: u16,
}

#[derive(Debug, Clone, Copy)]
pub struct TerminalSize {
    pub rows: u16,
    pub cols: u16,
}

#[derive(Debug, Clone)]
pub enum TerminalMode {
    Command,    // 命令输入模式
    Output,     // 输出显示模式
    Interactive, // 交互式程序模式 (如 vim, top)
    AI,         // AI 交互模式
}

#[derive(Debug, Clone)]
pub struct OutputLine {
    pub content: String,
    pub line_type: OutputType,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub ansi_formatted: bool,
}

#[derive(Debug, Clone)]
pub enum OutputType {
    Stdout,
    Stderr,
    System,  // 系统消息
    AI,      // AI 响应
}
```

### 2.4.2 输入处理器实现

#### 输入处理核心
```rust
pub struct InputProcessor {
    input_buffer: Arc<Mutex<InputBuffer>>,
    key_handler: KeyHandler,
    validator: InputValidator,
}

#[derive(Debug)]
pub struct InputBuffer {
    current_line: String,
    cursor_pos: usize,
    history: VecDeque<String>,
    history_index: Option<usize>,
}

impl InputProcessor {
    pub fn new() -> Self {
        Self {
            input_buffer: Arc::new(Mutex::new(InputBuffer::new())),
            key_handler: KeyHandler::new(),
            validator: InputValidator::new(),
        }
    }

    pub async fn process_key_event(&self, key_event: KeyEvent) -> Result<InputAction, IOError> {
        let action = self.key_handler.handle_key(key_event)?;

        match action {
            KeyAction::Character(ch) => {
                self.insert_character(ch).await?;
                Ok(InputAction::UpdateDisplay)
            },
            KeyAction::Backspace => {
                self.handle_backspace().await?;
                Ok(InputAction::UpdateDisplay)
            },
            KeyAction::Enter => {
                let line = self.get_current_line().await?;
                if self.validator.validate(&line)? {
                    self.add_to_history(line.clone()).await?;
                    self.clear_current_line().await?;
                    Ok(InputAction::ExecuteCommand(line))
                } else {
                    Ok(InputAction::ShowError("Invalid input".to_string()))
                }
            },
            KeyAction::ArrowUp => {
                self.navigate_history(-1).await?;
                Ok(InputAction::UpdateDisplay)
            },
            KeyAction::ArrowDown => {
                self.navigate_history(1).await?;
                Ok(InputAction::UpdateDisplay)
            },
            KeyAction::Tab => {
                let completion = self.get_tab_completion().await?;
                Ok(InputAction::ShowCompletion(completion))
            },
            KeyAction::CtrlC => Ok(InputAction::Interrupt),
            KeyAction::CtrlD => Ok(InputAction::EOF),
            _ => Ok(InputAction::None),
        }
    }

    async fn insert_character(&self, ch: char) -> Result<(), IOError> {
        let mut buffer = self.input_buffer.lock().unwrap();
        if buffer.cursor_pos >= buffer.current_line.len() {
            buffer.current_line.push(ch);
        } else {
            buffer.current_line.insert(buffer.cursor_pos, ch);
        }
        buffer.cursor_pos += 1;
        Ok(())
    }

    async fn handle_backspace(&self) -> Result<(), IOError> {
        let mut buffer = self.input_buffer.lock().unwrap();
        if buffer.cursor_pos > 0 {
            buffer.cursor_pos -= 1;
            buffer.current_line.remove(buffer.cursor_pos);
        }
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub enum KeyAction {
    Character(char),
    Backspace,
    Delete,
    Enter,
    Tab,
    ArrowUp,
    ArrowDown,
    ArrowLeft,
    ArrowRight,
    Home,
    End,
    CtrlC,
    CtrlD,
    CtrlL,
    Unknown,
}

#[derive(Debug, Clone)]
pub enum InputAction {
    None,
    UpdateDisplay,
    ExecuteCommand(String),
    ShowError(String),
    ShowCompletion(Vec<String>),
    Interrupt,
    EOF,
}
```

#### 输入验证器
```rust
pub struct InputValidator {
    max_line_length: usize,
    forbidden_patterns: Vec<regex::Regex>,
}

impl InputValidator {
    pub fn new() -> Self {
        let forbidden_patterns = vec![
            regex::Regex::new(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]").unwrap(), // 控制字符
        ];

        Self {
            max_line_length: 8192,
            forbidden_patterns,
        }
    }

    pub fn validate(&self, input: &str) -> Result<bool, IOError> {
        // 检查长度限制
        if input.len() > self.max_line_length {
            return Ok(false);
        }

        // 检查禁止的模式
        for pattern in &self.forbidden_patterns {
            if pattern.is_match(input) {
                return Ok(false);
            }
        }

        // 检查 UTF-8 编码有效性
        if !input.is_utf8() {
            return Ok(false);
        }

        Ok(true)
    }
}
```

### 2.4.3 输出处理器实现

#### ANSI 序列解析器
```rust
pub struct OutputProcessor {
    ansi_parser: AnsiParser,
    output_buffer: Arc<Mutex<OutputBuffer>>,
    formatter: OutputFormatter,
}

#[derive(Debug)]
pub struct AnsiParser {
    state: ParserState,
    current_sequence: String,
    default_style: TextStyle,
}

#[derive(Debug, Clone, Copy)]
enum ParserState {
    Normal,
    Escape,
    CSI,
    OSC,
}

#[derive(Debug, Clone)]
pub struct TextStyle {
    pub fg_color: Option<Color>,
    pub bg_color: Option<Color>,
    pub bold: bool,
    pub italic: bool,
    pub underline: bool,
    pub strikethrough: bool,
}

#[derive(Debug, Clone, Copy)]
pub enum Color {
    Black,
    Red,
    Green,
    Yellow,
    Blue,
    Magenta,
    Cyan,
    White,
    RGB(u8, u8, u8),
    Indexed(u8),
}

impl OutputProcessor {
    pub fn new() -> Self {
        Self {
            ansi_parser: AnsiParser::new(),
            output_buffer: Arc::new(Mutex::new(OutputBuffer::new())),
            formatter: OutputFormatter::new(),
        }
    }

    pub async fn process_output(&self, data: &[u8], output_type: OutputType) -> Result<Vec<FormattedChunk>, IOError> {
        let text = String::from_utf8_lossy(data);
        let mut chunks = Vec::new();

        for ch in text.chars() {
            match self.ansi_parser.process_char(ch) {
                AnsiEvent::Character(ch, style) => {
                    chunks.push(FormattedChunk {
                        text: ch.to_string(),
                        style: style.clone(),
                        chunk_type: ChunkType::Text,
                    });
                },
                AnsiEvent::ControlSequence(sequence) => {
                    chunks.push(self.handle_control_sequence(sequence)?);
                },
                AnsiEvent::Continue => {
                    // 继续解析当前序列
                }
            }
        }

        // 添加到输出缓冲区
        self.add_to_buffer(chunks.clone(), output_type).await?;

        Ok(chunks)
    }

    fn handle_control_sequence(&self, sequence: ControlSequence) -> Result<FormattedChunk, IOError> {
        match sequence {
            ControlSequence::CursorMove { row, col } => {
                Ok(FormattedChunk {
                    text: String::new(),
                    style: TextStyle::default(),
                    chunk_type: ChunkType::CursorMove { row, col },
                })
            },
            ControlSequence::ClearScreen => {
                Ok(FormattedChunk {
                    text: String::new(),
                    style: TextStyle::default(),
                    chunk_type: ChunkType::ClearScreen,
                })
            },
            ControlSequence::SetStyle(style) => {
                Ok(FormattedChunk {
                    text: String::new(),
                    style,
                    chunk_type: ChunkType::StyleChange,
                })
            },
            _ => {
                Ok(FormattedChunk {
                    text: String::new(),
                    style: TextStyle::default(),
                    chunk_type: ChunkType::Unknown,
                })
            }
        }
    }
}

#[derive(Debug, Clone)]
pub struct FormattedChunk {
    pub text: String,
    pub style: TextStyle,
    pub chunk_type: ChunkType,
}

#[derive(Debug, Clone)]
pub enum ChunkType {
    Text,
    CursorMove { row: u16, col: u16 },
    ClearScreen,
    StyleChange,
    Newline,
    Unknown,
}

#[derive(Debug, Clone)]
pub enum AnsiEvent {
    Character(char, TextStyle),
    ControlSequence(ControlSequence),
    Continue,
}

#[derive(Debug, Clone)]
pub enum ControlSequence {
    CursorMove { row: u16, col: u16 },
    CursorUp(u16),
    CursorDown(u16),
    CursorLeft(u16),
    CursorRight(u16),
    ClearScreen,
    ClearLine,
    SetStyle(TextStyle),
    Bell,
}
```

### 2.4.4 前后端通信协议

#### WebSocket 消息定义
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WSMessage {
    // 前端到后端
    Input {
        session_id: String,
        data: InputData,
    },
    ResizeTerminal {
        session_id: String,
        rows: u16,
        cols: u16,
    },
    // 后端到前端
    Output {
        session_id: String,
        chunks: Vec<FormattedChunk>,
    },
    StateUpdate {
        session_id: String,
        state: SessionState,
    },
    Error {
        session_id: String,
        error: String,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum InputData {
    KeyEvent {
        key: String,
        modifiers: Vec<String>,
    },
    Paste {
        text: String,
    },
    Command {
        command: String,
    },
}
```

#### 通信管理器
```rust
pub struct CommunicationManager {
    connections: Arc<Mutex<HashMap<String, WebSocketConnection>>>,
    message_queue: Arc<Mutex<VecDeque<WSMessage>>>,
    broadcast_tx: broadcast::Sender<WSMessage>,
}

impl CommunicationManager {
    pub fn new() -> Self {
        let (broadcast_tx, _) = broadcast::channel(1000);

        Self {
            connections: Arc::new(Mutex::new(HashMap::new())),
            message_queue: Arc::new(Mutex::new(VecDeque::new())),
            broadcast_tx,
        }
    }

    pub async fn send_to_frontend(&self, session_id: &str, message: WSMessage) -> Result<(), IOError> {
        let connections = self.connections.lock().unwrap();
        if let Some(conn) = connections.get(session_id) {
            conn.send(message).await?;
        }
        Ok(())
    }

    pub async fn broadcast_output(&self, session_id: &str, chunks: Vec<FormattedChunk>) -> Result<(), IOError> {
        let message = WSMessage::Output {
            session_id: session_id.to_string(),
            chunks,
        };

        self.broadcast_tx.send(message)?;
        Ok(())
    }

    pub async fn handle_input(&self, session_id: &str, input_data: InputData) -> Result<(), IOError> {
        // 处理来自前端的输入
        match input_data {
            InputData::KeyEvent { key, modifiers } => {
                // 转换为内部键盘事件格式
                let key_event = self.convert_to_key_event(key, modifiers)?;
                // 发送给输入处理器
                // 这里需要获取对应 session 的输入处理器
            },
            InputData::Paste { text } => {
                // 处理粘贴的文本
                for ch in text.chars() {
                    // 逐字符处理粘贴内容
                }
            },
            InputData::Command { command } => {
                // 直接执行命令
            },
        }
        Ok(())
    }
}
```

## 🔍 验收标准

### 2.4.1 功能验收
- [ ] 键盘输入正确处理，包括特殊键和组合键
- [ ] ANSI 颜色和格式化正确渲染
- [ ] 实时输出显示无明显延迟 (< 16ms)
- [ ] 特殊字符和 Unicode 字符正确处理
- [ ] 输入验证有效防止恶意输入
- [ ] 前后端通信稳定，无数据丢失
- [ ] 终端状态同步准确
- [ ] 大量输出时性能稳定

### 2.4.2 性能验收
- [ ] 输出处理延迟 < 16ms (60fps)
- [ ] 内存使用增长 < 1MB/分钟 (连续使用)
- [ ] WebSocket 消息处理 < 5ms
- [ ] ANSI 解析性能 > 10MB/s
- [ ] 输入响应时间 < 10ms

### 2.4.3 兼容性验收
- [ ] 支持所有常见 ANSI 转义序列
- [ ] 兼容 bash、zsh、fish 输出格式
- [ ] 支持 UTF-8 和常见字符编码
- [ ] 跨平台一致的行为表现

## 🧪 关键测试用例

### 2.4.1 输入处理测试
```rust
#[tokio::test]
async fn test_basic_input_processing() {
    let processor = InputProcessor::new();

    // 测试字符输入
    let result = processor.process_key_event(KeyEvent::char('a')).await.unwrap();
    assert!(matches!(result, InputAction::UpdateDisplay));

    // 测试回车键
    let result = processor.process_key_event(KeyEvent::char('\n')).await.unwrap();
    assert!(matches!(result, InputAction::ExecuteCommand(_)));
}

#[tokio::test]
async fn test_special_keys() {
    let processor = InputProcessor::new();

    // 测试 Ctrl+C
    let result = processor.process_key_event(KeyEvent::ctrl('c')).await.unwrap();
    assert!(matches!(result, InputAction::Interrupt));

    // 测试方向键
    let result = processor.process_key_event(KeyEvent::arrow_up()).await.unwrap();
    assert!(matches!(result, InputAction::UpdateDisplay));
}

#[tokio::test]
async fn test_input_validation() {
    let validator = InputValidator::new();

    // 测试正常输入
    assert!(validator.validate("ls -la").unwrap());

    // 测试非法控制字符
    assert!(!validator.validate("\x00\x01invalid").unwrap());

    // 测试过长输入
    let long_input = "a".repeat(10000);
    assert!(!validator.validate(&long_input).unwrap());
}
```

### 2.4.2 输出处理测试
```rust
#[tokio::test]
async fn test_ansi_parsing() {
    let processor = OutputProcessor::new();

    // 测试颜色序列
    let input = b"\x1b[31mRed Text\x1b[0m";
    let chunks = processor.process_output(input, OutputType::Stdout).await.unwrap();

    assert_eq!(chunks.len(), 2);
    assert_eq!(chunks[0].text, "Red Text");
    assert_eq!(chunks[0].style.fg_color, Some(Color::Red));
}

#[tokio::test]
async fn test_control_sequences() {
    let processor = OutputProcessor::new();

    // 测试光标移动
    let input = b"\x1b[2;3H";
    let chunks = processor.process_output(input, OutputType::Stdout).await.unwrap();

    assert!(matches!(chunks[0].chunk_type, ChunkType::CursorMove { row: 2, col: 3 }));
}

#[tokio::test]
async fn test_large_output_performance() {
    let processor = OutputProcessor::new();
    let large_data = vec![b'a'; 1024 * 1024]; // 1MB data

    let start = std::time::Instant::now();
    let _result = processor.process_output(&large_data, OutputType::Stdout).await.unwrap();
    let duration = start.elapsed();

    assert!(duration.as_millis() < 100); // Should process 1MB in less than 100ms
}
```

### 2.4.3 通信协议测试
```rust
#[tokio::test]
async fn test_websocket_communication() {
    let comm_manager = CommunicationManager::new();

    // 测试消息序列化
    let message = WSMessage::Output {
        session_id: "test-session".to_string(),
        chunks: vec![FormattedChunk {
            text: "Hello".to_string(),
            style: TextStyle::default(),
            chunk_type: ChunkType::Text,
        }],
    };

    let serialized = serde_json::to_string(&message).unwrap();
    let deserialized: WSMessage = serde_json::from_str(&serialized).unwrap();

    assert!(matches!(deserialized, WSMessage::Output { .. }));
}

#[tokio::test]
async fn test_input_data_handling() {
    let comm_manager = CommunicationManager::new();

    let input_data = InputData::KeyEvent {
        key: "Enter".to_string(),
        modifiers: vec!["Ctrl".to_string()],
    };

    // 测试输入处理不会崩溃
    let result = comm_manager.handle_input("test-session", input_data).await;
    assert!(result.is_ok());
}
```

## 📊 实现里程碑

### 里程碑 1: 基础输入输出 (Day 1)
- [x] 输入处理器核心框架
- [x] 基础 ANSI 解析器
- [x] WebSocket 通信协议定义
- [x] 核心数据结构设计

### 里程碑 2: 完整功能实现 (Day 2)
- [x] 完整的特殊键处理
- [x] 复杂 ANSI 序列支持
- [x] 性能优化和缓冲管理
- [x] 全面的测试覆盖
- [x] 前后端集成测试

## 🔗 相关文档

- [Task 2.3: 命令执行引擎](task_06_command_execution.md)
- [Task 3.2: 终端显示组件](task_09_terminal_display.md)
- [产品需求文档](../prd.md)
- [项目架构设计](task_03_architecture_design.md)

---

**最后更新**: 2024-12-19
**负责人**: 开发团队
**审核状态**: 待审核
