# Task 3.4: 设置界面

## 任务概述

**任务编号**: Task 3.4
**任务名称**: 设置界面
**状态**: ✅ 已完成
**优先级**: 中
**预计时间**: 2 天
**依赖任务**: Task 3.3 (主题系统)

## 实现总结

### 已完成功能
- ✅ 完整的设置界面架构设计和实现
- ✅ 左侧分类导航，支持搜索功能
- ✅ 6个主要设置分类页面：通用、终端、外观、AI、快捷键、安全
- ✅ 通用表单字段组件：开关、选择框、输入框、滑块
- ✅ 与现有设置store的完整集成
- ✅ 设置导入导出功能
- ✅ 设置重置功能
- ✅ 响应式布局设计
- ✅ 完整的TypeScript类型支持

### 技术实现亮点
- 模块化组件设计，易于扩展新的设置项
- 统一的设计语言和用户体验
- 实时设置预览功能（字体、主题等）
- 完善的表单验证和错误处理
- 支持快捷键录制功能
- 危险命令管理界面

### 文件结构
```
src/components/settings/
├── SettingsPanel.tsx           # 主设置面板
├── SettingsNavigation.tsx      # 设置导航
├── SettingsSection.tsx         # 设置区块组件
├── index.ts                    # 组件导出
├── fields/                     # 表单字段组件
│   ├── ToggleField.tsx
│   ├── SelectField.tsx
│   ├── InputField.tsx
│   └── SliderField.tsx
└── sections/                   # 设置分类组件
    ├── GeneralSettings.tsx
    ├── TerminalSettings.tsx
    ├── AppearanceSettings.tsx
    ├── AISettings.tsx
    ├── ShortcutSettings.tsx
    └── SecuritySettings.tsx
```

## 任务描述

创建完整的应用设置和配置界面，为用户提供个性化配置选项。设置界面需要涵盖终端配置、主题设置、AI 功能配置、快捷键设置等各个方面，提供直观易用的配置体验。

## 功能需求

### 3.4.1 设置界面架构

#### 设置分类
- **通用设置**: 语言、启动配置、自动更新等
- **终端设置**: 字体、颜色、行为配置
- **主题设置**: 主题选择、自定义主题管理
- **AI 设置**: AI 模型配置、API 密钥管理
- **快捷键设置**: 自定义快捷键配置
- **安全设置**: 权限管理、危险命令设置
- **高级设置**: 实验性功能、调试选项

#### 界面布局
- **侧边栏导航**: 左侧分类导航栏
- **设置面板**: 右侧详细设置内容
- **搜索功能**: 快速搜索设置选项
- **重置功能**: 恢复默认设置选项

### 3.4.2 通用设置

#### 应用行为
- **启动设置**
  - 开机自启动
  - 启动时默认工作目录
  - 启动时打开的 Tab 数量
  - 恢复上次会话

- **语言设置**
  - 界面语言选择（中文/英文）
  - 日期时间格式
  - 数字格式设置

- **自动更新**
  - 自动检查更新
  - 更新提醒设置
  - Beta 版本参与

#### 窗口设置
- **默认窗口大小**: 设置启动时的窗口尺寸
- **窗口位置**: 记住窗口位置或居中显示
- **最小化行为**: 最小化到系统托盘
- **关闭行为**: 关闭窗口或退出应用

### 3.4.3 终端设置

#### 字体配置
- **字体家族**: 选择等宽字体
- **字体大小**: 字体大小滑块调节
- **行高**: 行间距调节
- **字体权重**: 字体粗细设置

#### 显示设置
- **光标样式**: 块状/下划线/竖线光标
- **光标闪烁**: 启用/禁用光标闪烁
- **显示行号**: 显示/隐藏行号
- **文本换行**: 自动换行设置

#### 行为设置
- **历史记录**: 命令历史保留数量
- **滚动行数**: 终端缓冲区大小
- **选择复制**: 选择文本自动复制
- **粘贴确认**: 粘贴多行内容时确认

### 3.4.4 主题设置

#### 主题选择
- **内置主题**: 预设主题列表选择
- **主题预览**: 实时预览主题效果
- **跟随系统**: 跟随系统主题设置
- **定时切换**: 根据时间自动切换主题

#### 自定义主题
- **主题编辑器**: 可视化主题编辑工具
- **颜色选择**: RGB/HSL 颜色选择器
- **主题导入**: 从文件导入主题
- **主题导出**: 导出自定义主题

### 3.4.5 AI 设置

#### 模型配置
- **默认模型**: 选择默认 AI 模型
- **本地模型**: 本地模型路径配置
- **云端模型**: API 密钥和端点配置
- **模型参数**: 温度、最大 tokens 等参数

#### AI 功能
- **@command 功能**: 启用/禁用自然语言转命令
- **@model 对话**: 启用/禁用 AI 对话模式
- **智能建议**: 命令自动补全和建议
- **安全检查**: AI 安全性检查设置

### 3.4.6 快捷键设置

#### 全局快捷键
- **显示/隐藏窗口**: 全局唤起快捷键
- **新建 Tab**: 创建新终端 Tab
- **关闭 Tab**: 关闭当前 Tab
- **切换 Tab**: Tab 之间快速切换

#### 终端快捷键
- **复制粘贴**: 复制粘贴快捷键自定义
- **清屏**: 清屏快捷键
- **历史搜索**: 命令历史搜索
- **全屏切换**: 终端全屏显示

#### 自定义快捷键
- **快捷键编辑器**: 可视化快捷键编辑
- **冲突检测**: 快捷键冲突提醒
- **重置默认**: 恢复默认快捷键设置

### 3.4.7 安全设置

#### 权限管理
- **文件系统访问**: 限制文件访问范围
- **网络访问**: 网络请求权限控制
- **系统调用**: 系统命令执行权限
- **敏感目录**: 敏感目录访问保护

#### 危险命令
- **命令黑名单**: 危险命令列表管理
- **确认机制**: 危险操作确认设置
- **日志记录**: 命令执行日志记录
- **撤销功能**: 支持操作撤销的命令

## 技术实现

### 3.4.1 设置数据结构

```typescript
// 设置配置类型定义
interface AppSettings {
  general: GeneralSettings;
  terminal: TerminalSettings;
  theme: ThemeSettings;
  ai: AISettings;
  shortcuts: ShortcutSettings;
  security: SecuritySettings;
  advanced: AdvancedSettings;
}

// 通用设置
interface GeneralSettings {
  language: 'zh-CN' | 'en-US';
  autoStart: boolean;
  defaultWorkingDirectory: string;
  restoreSession: boolean;
  autoUpdate: boolean;
  betaUpdates: boolean;
  windowSize: { width: number; height: number };
  windowPosition: 'center' | 'remember';
  minimizeToTray: boolean;
}

// 终端设置
interface TerminalSettings {
  fontFamily: string;
  fontSize: number;
  lineHeight: number;
  fontWeight: number;
  cursorStyle: 'block' | 'underline' | 'bar';
  cursorBlink: boolean;
  showLineNumbers: boolean;
  wordWrap: boolean;
  historySize: number;
  scrollbackLines: number;
  selectToCopy: boolean;
  confirmPaste: boolean;
}

// AI 设置
interface AISettings {
  defaultModel: string;
  localModelPath: string;
  apiKeys: Record<string, string>;
  modelParameters: {
    temperature: number;
    maxTokens: number;
    topP: number;
  };
  enableCommand: boolean;
  enableChat: boolean;
  smartSuggestions: boolean;
  safetyCheck: boolean;
}

// 快捷键设置
interface ShortcutSettings {
  global: Record<string, string>;
  terminal: Record<string, string>;
  custom: Array<{
    id: string;
    name: string;
    keys: string;
    action: string;
  }>;
}
```

### 3.4.2 设置状态管理

```typescript
// src/stores/settingsStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface SettingsState {
  // 设置数据
  settings: AppSettings;

  // 临时设置（未保存）
  tempSettings: Partial<AppSettings>;

  // 状态标识
  isModified: boolean;
  isLoading: boolean;

  // 操作方法
  updateSetting: <T extends keyof AppSettings>(
    category: T,
    key: keyof AppSettings[T],
    value: any
  ) => void;

  updateTempSetting: <T extends keyof AppSettings>(
    category: T,
    key: keyof AppSettings[T],
    value: any
  ) => void;

  saveSettings: () => Promise<void>;
  resetSettings: () => void;
  resetCategory: (category: keyof AppSettings) => void;
  loadSettings: () => Promise<void>;

  // 验证方法
  validateSettings: () => string[];
  hasUnsavedChanges: () => boolean;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      settings: getDefaultSettings(),
      tempSettings: {},
      isModified: false,
      isLoading: false,

      updateSetting: (category, key, value) => {
        set((state) => ({
          settings: {
            ...state.settings,
            [category]: {
              ...state.settings[category],
              [key]: value
            }
          },
          isModified: true
        }));
      },

      updateTempSetting: (category, key, value) => {
        set((state) => ({
          tempSettings: {
            ...state.tempSettings,
            [category]: {
              ...state.tempSettings[category],
              [key]: value
            }
          },
          isModified: true
        }));
      },

      saveSettings: async () => {
        const { settings, tempSettings } = get();
        const mergedSettings = mergeDeep(settings, tempSettings);

        // 验证设置
        const errors = validateAppSettings(mergedSettings);
        if (errors.length > 0) {
          throw new Error(`设置验证失败: ${errors.join(', ')}`);
        }

        // 保存到后端
        await saveSettingsToBackend(mergedSettings);

        set({
          settings: mergedSettings,
          tempSettings: {},
          isModified: false
        });
      },

      resetSettings: () => {
        set({
          settings: getDefaultSettings(),
          tempSettings: {},
          isModified: false
        });
      }
    }),
    {
      name: 'app-settings'
    }
  )
);
```

### 3.4.3 设置界面组件

```typescript
// src/components/settings/SettingsPanel.tsx
import React, { useState } from 'react';
import { useSettingsStore } from '@/stores/settingsStore';
import { SettingsNavigation } from './SettingsNavigation';
import { GeneralSettings } from './sections/GeneralSettings';
import { TerminalSettings } from './sections/TerminalSettings';
import { ThemeSettings } from './sections/ThemeSettings';
import { AISettings } from './sections/AISettings';
import { ShortcutSettings } from './sections/ShortcutSettings';
import { SecuritySettings } from './sections/SecuritySettings';

type SettingsSection =
  | 'general'
  | 'terminal'
  | 'theme'
  | 'ai'
  | 'shortcuts'
  | 'security'
  | 'advanced';

export function SettingsPanel() {
  const [activeSection, setActiveSection] = useState<SettingsSection>('general');
  const [searchQuery, setSearchQuery] = useState('');

  const {
    settings,
    isModified,
    saveSettings,
    resetSettings,
    hasUnsavedChanges
  } = useSettingsStore();

  const handleSave = async () => {
    try {
      await saveSettings();
      // 显示保存成功提示
    } catch (error) {
      // 显示错误提示
      console.error('保存设置失败:', error);
    }
  };

  const handleReset = () => {
    if (window.confirm('确定要重置所有设置到默认值吗？')) {
      resetSettings();
    }
  };

  const renderSettingsSection = () => {
    switch (activeSection) {
      case 'general':
        return <GeneralSettings />;
      case 'terminal':
        return <TerminalSettings />;
      case 'theme':
        return <ThemeSettings />;
      case 'ai':
        return <AISettings />;
      case 'shortcuts':
        return <ShortcutSettings />;
      case 'security':
        return <SecuritySettings />;
      default:
        return <GeneralSettings />;
    }
  };

  return (
    <div className="flex h-full bg-background">
      {/* 左侧导航 */}
      <div className="w-64 border-r border-border bg-surface">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4">设置</h2>

          {/* 搜索框 */}
          <input
            type="text"
            placeholder="搜索设置..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-3 py-2 text-sm border border-border rounded-md"
          />
        </div>

        <SettingsNavigation
          activeSection={activeSection}
          onSectionChange={setActiveSection}
          searchQuery={searchQuery}
        />
      </div>

      {/* 右侧设置内容 */}
      <div className="flex-1 flex flex-col">
        {/* 设置内容 */}
        <div className="flex-1 overflow-auto">
          {renderSettingsSection()}
        </div>

        {/* 底部操作栏 */}
        {isModified && (
          <div className="border-t border-border bg-surface p-4 flex items-center justify-between">
            <div className="text-sm text-warning">
              ⚠️ 有未保存的更改
            </div>

            <div className="flex gap-2">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 text-sm border border-border rounded-md hover:bg-hover"
              >
                取消
              </button>

              <button
                onClick={handleReset}
                className="px-4 py-2 text-sm border border-border rounded-md hover:bg-hover"
              >
                重置
              </button>

              <button
                onClick={handleSave}
                className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary/90"
              >
                保存设置
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
```

### 3.4.4 设置组件示例

```typescript
// src/components/settings/sections/GeneralSettings.tsx
import React from 'react';
import { useSettingsStore } from '@/stores/settingsStore';
import { SettingsSection } from '../SettingsSection';
import { ToggleField } from '../fields/ToggleField';
import { SelectField } from '../fields/SelectField';
import { InputField } from '../fields/InputField';

export function GeneralSettings() {
  const { settings, updateSetting } = useSettingsStore();

  return (
    <div className="p-6 space-y-6">
      <SettingsSection title="启动设置" description="配置应用启动行为">
        <ToggleField
          label="开机自启动"
          description="系统启动时自动启动 TAgent"
          value={settings.general.autoStart}
          onChange={(value) => updateSetting('general', 'autoStart', value)}
        />

        <InputField
          label="默认工作目录"
          description="终端启动时的默认目录"
          value={settings.general.defaultWorkingDirectory}
          onChange={(value) => updateSetting('general', 'defaultWorkingDirectory', value)}
          placeholder="/Users/<USER>"
        />

        <ToggleField
          label="恢复上次会话"
          description="启动时恢复上次关闭时的 Tab"
          value={settings.general.restoreSession}
          onChange={(value) => updateSetting('general', 'restoreSession', value)}
        />
      </SettingsSection>

      <SettingsSection title="语言设置" description="界面语言和本地化">
        <SelectField
          label="界面语言"
          description="选择应用界面显示语言"
          value={settings.general.language}
          onChange={(value) => updateSetting('general', 'language', value)}
          options={[
            { value: 'zh-CN', label: '简体中文' },
            { value: 'en-US', label: 'English' }
          ]}
        />
      </SettingsSection>

      <SettingsSection title="更新设置" description="应用更新相关配置">
        <ToggleField
          label="自动检查更新"
          description="定期检查应用更新"
          value={settings.general.autoUpdate}
          onChange={(value) => updateSetting('general', 'autoUpdate', value)}
        />

        <ToggleField
          label="参与 Beta 测试"
          description="接收 Beta 版本更新"
          value={settings.general.betaUpdates}
          onChange={(value) => updateSetting('general', 'betaUpdates', value)}
        />
      </SettingsSection>
    </div>
  );
}
```

## 验收标准

### 3.4.1 功能验收
- [ ] 完整的设置分类导航
- [ ] 所有设置项正常工作
- [ ] 设置实时预览和应用
- [ ] 设置持久化保存
- [ ] 搜索功能正常
- [ ] 重置功能正常

### 3.4.2 界面验收
- [ ] 响应式布局适配
- [ ] 主题样式统一
- [ ] 交互反馈完善
- [ ] 无障碍访问支持

### 3.4.3 数据验收
- [ ] 设置验证机制
- [ ] 错误处理完善
- [ ] 数据同步正确
- [ ] 默认值合理

## 相关文件

### 新增文件
- `src/components/settings/SettingsPanel.tsx` - 主设置面板
- `src/components/settings/SettingsNavigation.tsx` - 设置导航
- `src/components/settings/SettingsSection.tsx` - 设置区块组件
- `src/components/settings/fields/` - 设置字段组件
- `src/components/settings/sections/` - 各设置分类组件
- `src/stores/settingsStore.ts` - 设置状态管理

### 修改文件
- `src/components/layout/MainLayout.tsx` - 添加设置面板入口
- `src/App.tsx` - 集成设置功能

## 测试计划

### 3.4.1 单元测试
- 设置状态管理测试
- 设置组件渲染测试
- 设置验证逻辑测试

### 3.4.2 集成测试
- 设置保存和恢复测试
- 设置应用效果测试
- 搜索功能测试

### 3.4.3 用户测试
- 设置界面易用性测试
- 设置查找效率测试
- 设置修改体验测试

## 时间安排

### Day 1: 基础架构 (8 小时)
- 设置数据结构设计 (2 小时)
- 设置状态管理实现 (3 小时)
- 基础设置界面框架 (3 小时)

### Day 2: 功能实现 (8 小时)
- 各设置分类实现 (5 小时)
- 搜索和重置功能 (2 小时)
- 测试和优化 (1 小时)

## 风险评估

### 技术风险
- **中**: 设置项过多可能影响界面性能
- **低**: 设置验证逻辑复杂度

### 解决方案
- 使用虚拟滚动优化长列表
- 实现分层验证机制
- 添加设置导入导出功能

## 后续优化

### 功能扩展
- 设置同步到云端
- 设置配置文件版本管理
- 高级设置搜索和过滤
- 设置使用统计和建议

### 性能优化
- 设置懒加载
- 设置缓存机制
- 批量设置操作
- 设置变更通知优化
