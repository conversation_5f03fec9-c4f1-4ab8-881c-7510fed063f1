# Task 3.2: 终端显示组件

## 任务概述

**任务编号**: Task 3.2
**任务名称**: 终端显示组件
**状态**: ⏳ 待开始
**优先级**: 高
**预计时间**: 3 天
**依赖任务**: Task 3.1 (基础 UI 框架), Task 2.4 (输入输出处理)

## 任务描述

实现核心的终端文本显示和渲染组件，包括命令输入、输出显示、光标管理、文本选择等基础终端功能。这是用户界面的核心组件，需要提供流畅的终端体验。

## 功能需求

### 3.2.1 核心显示功能

#### 文本渲染
- **单体字符显示**: 支持 ASCII 和 Unicode 字符显示
- **颜色支持**: 支持 ANSI 颜色代码和 256 色模式
- **字体配置**: 支持等宽字体选择和字体大小调节
- **文本换行**: 正确处理长文本的换行显示
- **特殊字符**: 支持制表符、回车符等特殊字符渲染

#### 光标管理
- **光标显示**: 可见的光标指示器，支持不同光标样式
- **光标定位**: 精确的光标位置控制
- **光标闪烁**: 可配置的光标闪烁效果
- **光标跟随**: 输入时光标自动跟随

#### 滚动功能
- **自动滚动**: 新内容超出视窗时自动滚动到底部
- **手动滚动**: 支持鼠标滚轮和滚动条滚动
- **滚动回历史**: 支持向上滚动查看历史输出
- **快速定位**: 支持快速跳转到顶部/底部

### 3.2.2 交互功能

#### 文本选择
- **鼠标选择**: 支持鼠标拖拽选择文本
- **键盘选择**: 支持 Shift + 方向键选择
- **全选功能**: Ctrl/Cmd + A 全选当前可见内容
- **复制功能**: 支持选中文本的复制操作

#### 输入处理
- **实时输入**: 实时显示用户键盘输入
- **输入验证**: 处理特殊按键和组合键
- **IME 支持**: 支持中文等输入法
- **粘贴功能**: 支持 Ctrl/Cmd + V 粘贴

#### 右键菜单
- **复制**: 复制选中文本
- **粘贴**: 粘贴剪贴板内容
- **全选**: 选择所有文本
- **清屏**: 清除终端显示内容

### 3.2.3 显示优化

#### 性能优化
- **虚拟滚动**: 大量内容时使用虚拟滚动减少 DOM 节点
- **增量渲染**: 只渲染变化的部分，避免全量重绘
- **防抖处理**: 快速输入时的防抖优化
- **内存管理**: 限制历史记录长度，避免内存泄漏

#### 响应式设计
- **自适应布局**: 根据容器大小调整显示区域
- **字体缩放**: 支持字体大小的动态调整
- **窗口变化**: 处理窗口大小变化时的重新布局

## 技术实现

### 3.2.1 组件架构

```tsx
// 核心终端显示组件
interface TerminalDisplayProps {
  terminalId: string;
  fontSize: number;
  theme: TerminalTheme;
  onInput: (input: string) => void;
  onResize: (cols: number, rows: number) => void;
}

// 终端状态接口
interface TerminalState {
  buffer: TerminalBuffer;
  cursor: CursorState;
  selection: SelectionState;
  scrollOffset: number;
  isActive: boolean;
}

// 终端缓冲区
interface TerminalBuffer {
  lines: TerminalLine[];
  maxLines: number;
  currentLine: number;
}

// 终端行数据
interface TerminalLine {
  text: string;
  attributes: TextAttribute[];
  timestamp: number;
}

// 文本属性
interface TextAttribute {
  start: number;
  length: number;
  foreground?: string;
  background?: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
}
```

### 3.2.2 核心组件实现

#### TerminalDisplay 主组件
```tsx
// src/components/terminal/TerminalDisplay.tsx
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useTerminalStore } from '../../stores/terminalStore';
import { TerminalBuffer } from './TerminalBuffer';
import { TerminalCursor } from './TerminalCursor';
import { TerminalSelection } from './TerminalSelection';

export const TerminalDisplay: React.FC<TerminalDisplayProps> = ({
  terminalId,
  fontSize,
  theme,
  onInput,
  onResize
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { getTerminal, updateTerminal } = useTerminalStore();
  const terminal = getTerminal(terminalId);

  // 处理键盘输入
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 处理特殊按键
    if (event.ctrlKey || event.metaKey) {
      handleCtrlKey(event);
      return;
    }

    // 处理普通按键
    onInput(event.key);
  }, [onInput]);

  // 处理组合键
  const handleCtrlKey = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'c':
        if (hasSelection()) {
          copySelection();
        } else {
          onInput('\x03'); // Ctrl+C
        }
        break;
      case 'v':
        pasteFromClipboard();
        break;
      case 'a':
        selectAll();
        break;
    }
  };

  return (
    <div
      ref={containerRef}
      className="terminal-display"
      style={{
        fontSize: `${fontSize}px`,
        fontFamily: 'Consolas, Monaco, monospace',
        backgroundColor: theme.background,
        color: theme.foreground,
      }}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      <TerminalBuffer
        buffer={terminal.buffer}
        theme={theme}
        onTextSelect={handleTextSelect}
      />
      <TerminalCursor
        position={terminal.cursor}
        theme={theme}
        visible={terminal.isActive}
      />
      <TerminalSelection
        selection={terminal.selection}
        theme={theme}
      />
    </div>
  );
};
```

#### TerminalBuffer 缓冲区组件
```tsx
// src/components/terminal/TerminalBuffer.tsx
import React, { memo } from 'react';
import { TerminalLine } from './TerminalLine';

interface TerminalBufferProps {
  buffer: TerminalBuffer;
  theme: TerminalTheme;
  onTextSelect: (start: Position, end: Position) => void;
}

export const TerminalBuffer: React.FC<TerminalBufferProps> = memo(({
  buffer,
  theme,
  onTextSelect
}) => {
  return (
    <div className="terminal-buffer">
      {buffer.lines.map((line, index) => (
        <TerminalLine
          key={`${line.timestamp}-${index}`}
          line={line}
          lineNumber={index}
          theme={theme}
          onSelect={onTextSelect}
        />
      ))}
    </div>
  );
});
```

#### TerminalLine 行组件
```tsx
// src/components/terminal/TerminalLine.tsx
import React, { memo } from 'react';

interface TerminalLineProps {
  line: TerminalLine;
  lineNumber: number;
  theme: TerminalTheme;
  onSelect: (start: Position, end: Position) => void;
}

export const TerminalLine: React.FC<TerminalLineProps> = memo(({
  line,
  lineNumber,
  theme,
  onSelect
}) => {
  // 渲染带样式的文本
  const renderStyledText = () => {
    if (!line.attributes.length) {
      return <span>{line.text}</span>;
    }

    const elements = [];
    let currentIndex = 0;

    line.attributes.forEach((attr, index) => {
      // 渲染属性前的普通文本
      if (attr.start > currentIndex) {
        elements.push(
          <span key={`text-${index}`}>
            {line.text.slice(currentIndex, attr.start)}
          </span>
        );
      }

      // 渲染带样式的文本
      const styledText = line.text.slice(attr.start, attr.start + attr.length);
      const style = {
        color: attr.foreground || theme.foreground,
        backgroundColor: attr.background || 'transparent',
        fontWeight: attr.bold ? 'bold' : 'normal',
        fontStyle: attr.italic ? 'italic' : 'normal',
        textDecoration: attr.underline ? 'underline' : 'none',
      };

      elements.push(
        <span key={`styled-${index}`} style={style}>
          {styledText}
        </span>
      );

      currentIndex = attr.start + attr.length;
    });

    // 渲染剩余的普通文本
    if (currentIndex < line.text.length) {
      elements.push(
        <span key="text-end">
          {line.text.slice(currentIndex)}
        </span>
      );
    }

    return elements;
  };

  return (
    <div
      className="terminal-line"
      data-line={lineNumber}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      {renderStyledText()}
    </div>
  );
});
```

### 3.2.3 状态管理

#### Terminal Store 扩展
```typescript
// src/stores/terminalStore.ts
import { create } from 'zustand';

interface TerminalDisplayState {
  terminals: Map<string, TerminalState>;
  activeTerminalId: string | null;

  // 显示相关方法
  addLine: (terminalId: string, line: TerminalLine) => void;
  updateCursor: (terminalId: string, position: CursorPosition) => void;
  setSelection: (terminalId: string, selection: SelectionState) => void;
  clearSelection: (terminalId: string) => void;
  scrollToBottom: (terminalId: string) => void;

  // 输入处理
  handleInput: (terminalId: string, input: string) => void;
  handleSpecialKey: (terminalId: string, key: string) => void;
}

export const useTerminalStore = create<TerminalDisplayState>((set, get) => ({
  terminals: new Map(),
  activeTerminalId: null,

  addLine: (terminalId, line) => {
    set((state) => {
      const terminal = state.terminals.get(terminalId);
      if (terminal) {
        terminal.buffer.lines.push(line);

        // 限制历史记录长度
        if (terminal.buffer.lines.length > terminal.buffer.maxLines) {
          terminal.buffer.lines.shift();
        }

        // 更新当前行
        terminal.buffer.currentLine = terminal.buffer.lines.length - 1;

        state.terminals.set(terminalId, terminal);
      }
      return { terminals: new Map(state.terminals) };
    });
  },

  updateCursor: (terminalId, position) => {
    set((state) => {
      const terminal = state.terminals.get(terminalId);
      if (terminal) {
        terminal.cursor = { ...terminal.cursor, ...position };
        state.terminals.set(terminalId, terminal);
      }
      return { terminals: new Map(state.terminals) };
    });
  },

  // ... 其他方法实现
}));
```

### 3.2.4 样式设计

#### Tailwind CSS 配置
```css
/* src/styles/terminal.css */
.terminal-display {
  @apply w-full h-full overflow-hidden relative;
  @apply focus:outline-none;
  @apply select-none;
}

.terminal-buffer {
  @apply w-full h-full overflow-auto;
  @apply font-mono text-sm leading-tight;
}

.terminal-line {
  @apply whitespace-pre-wrap break-words;
  @apply min-h-[1.2em] px-2;
  @apply hover:bg-opacity-5 hover:bg-white;
}

.terminal-line.selected {
  @apply bg-blue-500 bg-opacity-20;
}

.terminal-cursor {
  @apply absolute w-2 h-5 bg-white;
  @apply animate-pulse;
}

.terminal-cursor.hidden {
  @apply opacity-0;
}

.terminal-selection {
  @apply absolute bg-blue-500 bg-opacity-30;
  @apply pointer-events-none;
}
```

#### 主题配置
```typescript
// src/types/theme.ts
export interface TerminalTheme {
  name: string;
  background: string;
  foreground: string;
  cursor: string;
  selection: string;

  // ANSI 颜色
  black: string;
  red: string;
  green: string;
  yellow: string;
  blue: string;
  magenta: string;
  cyan: string;
  white: string;

  // 亮色版本
  brightBlack: string;
  brightRed: string;
  brightGreen: string;
  brightYellow: string;
  brightBlue: string;
  brightMagenta: string;
  brightCyan: string;
  brightWhite: string;
}

export const darkTheme: TerminalTheme = {
  name: 'dark',
  background: '#1e1e1e',
  foreground: '#d4d4d4',
  cursor: '#ffffff',
  selection: '#264f78',

  black: '#000000',
  red: '#cd3131',
  green: '#0dbc79',
  yellow: '#e5e510',
  blue: '#2472c8',
  magenta: '#bc3fbc',
  cyan: '#11a8cd',
  white: '#e5e5e5',

  brightBlack: '#666666',
  brightRed: '#f14c4c',
  brightGreen: '#23d18b',
  brightYellow: '#f5f543',
  brightBlue: '#3b8eea',
  brightMagenta: '#d670d6',
  brightCyan: '#29b8db',
  brightWhite: '#ffffff',
};
```

## 集成方案

### 3.2.1 与后端集成

#### Tauri 命令调用
```typescript
// src/services/terminalService.ts
import { invoke } from '@tauri-apps/api/tauri';

export class TerminalService {
  // 发送输入到后端
  async sendInput(terminalId: string, input: string): Promise<void> {
    await invoke('terminal_input', {
      terminalId,
      input
    });
  }

  // 监听终端输出
  setupOutputListener(terminalId: string, callback: (output: string) => void) {
    // 使用 Tauri 事件系统监听输出
    listen('terminal_output', (event) => {
      if (event.payload.terminalId === terminalId) {
        callback(event.payload.output);
      }
    });
  }

  // 获取终端大小
  async getTerminalSize(terminalId: string): Promise<{ cols: number; rows: number }> {
    return await invoke('get_terminal_size', { terminalId });
  }

  // 调整终端大小
  async resizeTerminal(terminalId: string, cols: number, rows: number): Promise<void> {
    await invoke('resize_terminal', {
      terminalId,
      cols,
      rows
    });
  }
}
```

#### 输出解析器
```typescript
// src/utils/outputParser.ts
export class OutputParser {
  // 解析 ANSI 转义序列
  parseAnsiOutput(output: string): TerminalLine {
    const line: TerminalLine = {
      text: '',
      attributes: [],
      timestamp: Date.now()
    };

    let currentIndex = 0;
    let currentAttributes: Partial<TextAttribute> = {};

    // 解析 ANSI 代码
    const ansiRegex = /\x1b\[[0-9;]*m/g;
    let match;
    let lastIndex = 0;

    while ((match = ansiRegex.exec(output)) !== null) {
      // 添加前面的普通文本
      if (match.index > lastIndex) {
        const text = output.slice(lastIndex, match.index);
        line.text += text;

        if (Object.keys(currentAttributes).length > 0) {
          line.attributes.push({
            start: currentIndex,
            length: text.length,
            ...currentAttributes
          });
        }

        currentIndex += text.length;
      }

      // 解析 ANSI 代码
      const ansiCode = match[0];
      currentAttributes = this.parseAnsiCode(ansiCode, currentAttributes);

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余文本
    if (lastIndex < output.length) {
      const text = output.slice(lastIndex);
      line.text += text;

      if (Object.keys(currentAttributes).length > 0) {
        line.attributes.push({
          start: currentIndex,
          length: text.length,
          ...currentAttributes
        });
      }
    }

    return line;
  }

  private parseAnsiCode(code: string, current: Partial<TextAttribute>): Partial<TextAttribute> {
    // 解析具体的 ANSI 颜色和样式代码
    const nums = code.slice(2, -1).split(';').map(n => parseInt(n));
    const newAttributes = { ...current };

    for (const num of nums) {
      switch (num) {
        case 0: // 重置
          return {};
        case 1: // 粗体
          newAttributes.bold = true;
          break;
        case 3: // 斜体
          newAttributes.italic = true;
          break;
        case 4: // 下划线
          newAttributes.underline = true;
          break;
        case 30: case 31: case 32: case 33:
        case 34: case 35: case 36: case 37:
          newAttributes.foreground = this.getAnsiColor(num - 30);
          break;
        case 40: case 41: case 42: case 43:
        case 44: case 45: case 46: case 47:
          newAttributes.background = this.getAnsiColor(num - 40);
          break;
        // ... 更多 ANSI 代码处理
      }
    }

    return newAttributes;
  }

  private getAnsiColor(index: number): string {
    const colors = [
      '#000000', '#cd3131', '#0dbc79', '#e5e510',
      '#2472c8', '#bc3fbc', '#11a8cd', '#e5e5e5'
    ];
    return colors[index] || '#d4d4d4';
  }
}
```

### 3.2.2 主布局集成

#### 更新 MainLayout
```tsx
// src/components/layout/MainLayout.tsx
import React from 'react';
import { TerminalDisplay } from '../terminal/TerminalDisplay';
import { useTerminalStore } from '../../stores/terminalStore';
import { useSettingsStore } from '../../stores/settingsStore';

export const MainLayout: React.FC = () => {
  const { activeTerminalId, terminals } = useTerminalStore();
  const { theme, fontSize } = useSettingsStore();

  const activeTerminal = activeTerminalId ? terminals.get(activeTerminalId) : null;

  if (!activeTerminal) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">没有活动的终端</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-hidden">
        <TerminalDisplay
          terminalId={activeTerminalId!}
          fontSize={fontSize}
          theme={theme}
          onInput={handleTerminalInput}
          onResize={handleTerminalResize}
        />
      </div>
    </div>
  );
};
```

## 测试计划

### 3.2.1 单元测试

#### 组件测试
```typescript
// src/components/terminal/__tests__/TerminalDisplay.test.tsx
import { render, fireEvent, screen } from '@testing-library/react';
import { TerminalDisplay } from '../TerminalDisplay';

describe('TerminalDisplay', () => {
  const mockProps = {
    terminalId: 'test-terminal',
    fontSize: 14,
    theme: darkTheme,
    onInput: jest.fn(),
    onResize: jest.fn()
  };

  test('渲染基本终端界面', () => {
    render(<TerminalDisplay {...mockProps} />);
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  test('处理键盘输入', () => {
    render(<TerminalDisplay {...mockProps} />);
    const input = screen.getByRole('textbox');

    fireEvent.keyDown(input, { key: 'a' });
    expect(mockProps.onInput).toHaveBeenCalledWith('a');
  });

  test('处理组合键', () => {
    render(<TerminalDisplay {...mockProps} />);
    const input = screen.getByRole('textbox');

    fireEvent.keyDown(input, { key: 'c', ctrlKey: true });
    // 验证复制功能
  });

  test('文本选择功能', () => {
    // 测试鼠标选择文本
  });
});
```

#### 解析器测试
```typescript
// src/utils/__tests__/outputParser.test.ts
import { OutputParser } from '../outputParser';

describe('OutputParser', () => {
  const parser = new OutputParser();

  test('解析纯文本', () => {
    const result = parser.parseAnsiOutput('Hello World');
    expect(result.text).toBe('Hello World');
    expect(result.attributes).toHaveLength(0);
  });

  test('解析 ANSI 颜色代码', () => {
    const result = parser.parseAnsiOutput('\x1b[31mRed Text\x1b[0m');
    expect(result.text).toBe('Red Text');
    expect(result.attributes).toHaveLength(1);
    expect(result.attributes[0].foreground).toBe('#cd3131');
  });

  test('解析复杂 ANSI 序列', () => {
    const result = parser.parseAnsiOutput('\x1b[1;31mBold Red\x1b[0m Normal');
    expect(result.text).toBe('Bold Red Normal');
    expect(result.attributes).toHaveLength(1);
    expect(result.attributes[0].bold).toBe(true);
    expect(result.attributes[0].foreground).toBe('#cd3131');
  });
});
```

### 3.2.2 集成测试

#### 端到端测试
```typescript
// tests/terminal-display.e2e.ts
import { test, expect } from '@playwright/test';

test.describe('Terminal Display', () => {
  test('基本命令执行', async ({ page }) => {
    await page.goto('/');

    // 等待终端加载
    await page.waitForSelector('.terminal-display');

    // 输入命令
    await page.keyboard.type('echo "Hello World"');
    await page.keyboard.press('Enter');

    // 验证输出
    await expect(page.locator('.terminal-line')).toContainText('Hello World');
  });

  test('文本选择和复制', async ({ page }) => {
    await page.goto('/');

    // 选择文本
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(200, 100);
    await page.mouse.up();

    // 复制文本
    await page.keyboard.press('Control+c');

    // 验证选择状态
    await expect(page.locator('.terminal-selection')).toBeVisible();
  });

  test('滚动功能', async ({ page }) => {
    await page.goto('/');

    // 生成大量输出
    for (let i = 0; i < 100; i++) {
      await page.keyboard.type(`echo "Line ${i}"`);
      await page.keyboard.press('Enter');
    }

    // 验证滚动到底部
    await expect(page.locator('.terminal-line').last()).toContainText('Line 99');

    // 测试向上滚动
    await page.mouse.wheel(0, -500);
    await expect(page.locator('.terminal-line').first()).toBeVisible();
  });
});
```

### 3.2.3 性能测试

#### 渲染性能测试
```typescript
// tests/performance/terminal-performance.test.ts
import { performance } from 'perf_hooks';

describe('Terminal Performance', () => {
  test('大量文本渲染性能', () => {
    const start = performance.now();

    // 渲染 1000 行文本
    const lines = Array.from({ length: 1000 }, (_, i) => ({
      text: `Line ${i} with some content`,
      attributes: [],
      timestamp: Date.now()
    }));

    // 渲染组件（模拟）
    const end = performance.now();

    // 渲染时间应该小于 100ms
    expect(end - start).toBeLessThan(100);
  });

  test('ANSI 解析性能', () => {
    const parser = new OutputParser();
    const start = performance.now();

    // 解析复杂 ANSI 输出
    const complexOutput = '\x1b[1;31m'.repeat(100) + 'Text' + '\x1b[0m'.repeat(100);

    for (let i = 0; i < 1000; i++) {
      parser.parseAnsiOutput(complexOutput);
    }

    const end = performance.now();

    // 解析时间应该合理
    expect(end - start).toBeLessThan(500);
  });
});
```

## 验收标准

### 3.2.1 功能验收

- [ ] **基础显示功能**
  - [ ] 能够正确显示命令输入和输出
  - [ ] 支持基本的 ANSI 颜色和样式
  - [ ] 光标位置准确，闪烁正常
  - [ ] 文本换行和滚动正常

- [ ] **交互功能**
  - [ ] 键盘输入响应正确
  - [ ] 鼠标选择文本功能正常
  - [ ] 复制粘贴功能正常
  - [ ] 右键菜单功能完整

- [ ] **性能要求**
  - [ ] 1000 行文本渲染时间 < 100ms
  - [ ] 键盘输入响应延迟 < 50ms
  - [ ] 滚动操作流畅无卡顿
  - [ ] 内存占用合理（< 50MB）

### 3.2.2 UI/UX 验收

- [ ] **视觉效果**
  - [ ] 界面美观，符合设计规范
  - [ ] 暗色主题显示正常
  - [ ] 字体清晰易读
  - [ ] 颜色对比度符合无障碍要求

- [ ] **用户体验**
  - [ ] 操作响应迅速
  - [ ] 交互反馈明确
  - [ ] 错误处理友好
  - [ ] 符合用户习惯

### 3.2.3 兼容性验收

- [ ] **浏览器兼容**
  - [ ] Chrome 90+ 正常运行
  - [ ] Firefox 88+ 正常运行
  - [ ] Safari 14+ 正常运行

- [ ] **系统兼容**
  - [ ] macOS 显示正常
  - [ ] Windows 显示正常
  - [ ] Linux 显示正常

## 交付物

### 3.2.1 代码文件

- [ ] `src/components/terminal/TerminalDisplay.tsx` - 主显示组件
- [ ] `src/components/terminal/TerminalBuffer.tsx` - 缓冲区组件
- [ ] `src/components/terminal/TerminalLine.tsx` - 行组件
- [ ] `src/components/terminal/TerminalCursor.tsx` - 光标组件
- [ ] `src/components/terminal/TerminalSelection.tsx` - 选择组件
- [ ] `src/utils/outputParser.ts` - 输出解析器
- [ ] `src/services/terminalService.ts` - 终端服务
- [ ] `src/styles/terminal.css` - 样式文件

### 3.2.2 测试文件

- [ ] `src/components/terminal/__tests__/` - 组件测试
- [ ] `src/utils/__tests__/` - 工具测试
- [ ] `tests/terminal-display.e2e.ts` - 端到端测试
- [ ] `tests/performance/` - 性能测试

### 3.2.3 文档文件

- [ ] 组件使用文档
- [ ] API 接口文档
- [ ] 性能优化指南
- [ ] 故障排除指南

## 后续任务

完成此任务后，可以进行以下后续任务：

- **Task 3.3**: 主题系统 - 实现主题切换功能
- **Task 3.4**: 设置界面 - 创建应用配置界面
- **Task 5.1**: 多 Tab 管理 - 实现多终端 Tab 功能

## 风险和注意事项

1. **性能风险**: 大量文本渲染可能影响性能，需要实现虚拟滚动
2. **兼容性风险**: 不同系统的字体渲染可能有差异
3. **内存风险**: 历史记录过多可能导致内存泄漏
4. **用户体验风险**: 输入延迟可能影响用户体验

## 预估时间分解

- **Day 1**: 基础渲染和显示 (8小时)
- **Day 2**: 交互功能和事件处理 (8小时)
- **Day 3**: 性能优化和测试 (8小时)

**总计**: 24 小时 (3 个工作日)
