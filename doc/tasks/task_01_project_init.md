# Task 1.1: 项目初始化

## 任务概述
- **任务ID**: Task 1.1
- **任务名称**: 项目初始化
- **优先级**: 高
- **预计时间**: 1 天
- **负责人**: 待分配
- **状态**: ⏳ 待开始

## 任务描述
搭建 TAgent 项目的基础框架，使用 Tauri + React + Rust 技术栈，创建完整的项目结构和基础配置。

## 具体任务

### 1.1.1 创建 Tauri 项目 (2 小时)
- 使用 `create-tauri-app` 初始化项目
- 选择 React + TypeScript 模板
- 验证项目可以正常启动

**执行命令**:
```bash
npx create-tauri-app --template react-ts
cd TAgent
```

### 1.1.2 配置项目结构 (2 小时)
- 整理和优化目录结构
- 创建必要的子目录
- 设置项目配置文件

**目录结构**:
```
TAgent/
├── src-tauri/          # Rust 后端代码
│   ├── src/
│   │   ├── main.rs     # 应用入口
│   │   ├── lib.rs      # 库文件
│   │   ├── terminal/   # 终端相关模块
│   │   ├── ai/         # AI 功能模块
│   │   └── utils/      # 工具函数
│   ├── Cargo.toml      # Rust 依赖配置
│   └── tauri.conf.json # Tauri 配置
├── src/                # React 前端代码
│   ├── components/     # React 组件
│   ├── hooks/          # 自定义 Hooks
│   ├── stores/         # 状态管理
│   ├── types/          # TypeScript 类型定义
│   ├── utils/          # 工具函数
│   └── styles/         # 样式文件
├── public/             # 静态资源
├── doc/                # 项目文档
└── tests/              # 测试文件
```

### 1.1.3 安装基础依赖 (1 小时)
- 安装 React 生态依赖
- 安装 Rust 依赖
- 配置 TypeScript

**前端依赖**:
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@tauri-apps/api": "^1.5.0",
    "zustand": "^4.4.0",
    "tailwindcss": "^3.3.0",
    "@headlessui/react": "^1.7.0",
    "lucide-react": "^0.263.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "typescript": "^5.0.0",
    "vite": "^4.4.0"
  }
}
```

**Rust 依赖**:
```toml
[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
```

### 1.1.4 基础配置文件 (2 小时)
- 配置 Tailwind CSS
- 设置 TypeScript 配置
- 配置 Tauri 窗口属性
- 设置开发脚本

**tauri.conf.json 关键配置**:
```json
{
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devPath": "http://localhost:1420",
    "distDir": "../dist"
  },
  "tauri": {
    "windows": [
      {
        "fullscreen": false,
        "resizable": true,
        "title": "TAgent",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600,
        "decorations": true,
        "transparent": false
      }
    ]
  }
}
```

### 1.1.5 创建基础组件 (1 小时)
- 创建 App 主组件
- 创建基础布局组件
- 验证前后端通信

**App.tsx**:
```tsx
import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">TAgent</h1>
        <div className="bg-gray-800 rounded-lg p-4">
          <p>Terminal Agent - AI Enhanced Terminal</p>
        </div>
      </div>
    </div>
  );
}

export default App;
```

## 验收标准

### 功能验收
- [ ] 项目可以成功启动 (`npm run tauri dev`)
- [ ] 应用窗口正常显示基础界面
- [ ] 前后端通信正常
- [ ] 项目结构符合规范
- [ ] 所有配置文件正确

### 技术验收
- [ ] TypeScript 编译无错误
- [ ] Rust 编译无错误
- [ ] 代码符合格式规范
- [ ] 依赖安装完整

### 文档验收
- [ ] README.md 包含项目基本信息
- [ ] 项目结构文档完整
- [ ] 开发环境搭建说明清晰

## 风险与依赖

### 潜在风险
- **Tauri 版本兼容性**: 确保使用稳定版本
- **Node.js 版本**: 确认开发环境 Node.js 版本兼容
- **Rust 工具链**: 确认 Rust 环境正确安装

### 前置依赖
- Node.js 16.0+
- Rust 1.70+
- 操作系统支持 (macOS/Windows/Linux)

### 后续依赖
- Task 1.2: 开发环境配置
- Task 1.3: 项目架构设计

## 验证步骤

1. **项目启动验证**:
   ```bash
   npm install
   npm run tauri dev
   ```

2. **构建验证**:
   ```bash
   npm run tauri build
   ```

3. **代码检查**:
   ```bash
   npm run lint
   npm run type-check
   ```

## 完成标志
- [ ] 项目成功启动并显示基础界面
- [ ] 前后端通信测试通过
- [ ] 构建流程验证成功
- [ ] 代码提交到版本控制系统
- [ ] 文档更新完成

---

**创建时间**: 2024-01-XX  
**最后更新**: 2024-01-XX  
**下一个任务**: Task 1.2 开发环境配置 