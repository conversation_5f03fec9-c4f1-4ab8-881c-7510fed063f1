# Task 2.3: 命令执行引擎

## 📋 任务概述

**任务编号**: Task 2.3
**任务名称**: 命令执行引擎
**任务状态**: ✅ 已完成
**优先级**: 高
**预计时间**: 2 天
**依赖任务**: Task 2.2 (Shell 进程管理)

## 🎯 任务目标

实现智能的命令解析和执行机制，支持传统命令执行和 AI 增强功能，为用户提供安全、高效的命令行体验。

## 📝 详细需求

### 2.3.1 基础命令执行
- 解析用户输入的命令
- 支持命令历史记录
- 处理命令参数和选项
- 支持管道和重定向操作
- 环境变量替换和扩展

### 2.3.2 AI 增强功能
- **@command**: 自然语言转命令
- **@model**: AI 对话模式
- **@explain**: 命令解释功能
- 上下文感知的智能建议
- 命令优化和最佳实践提示

### 2.3.3 安全机制
- 危险命令识别和警告
- 执行前确认机制
- 命令预览和影响范围分析
- 操作日志和审计记录

### 2.3.4 执行管理
- 异步命令执行
- 进程状态监控
- 中断和取消机制
- 错误处理和恢复

## 🛠️ 技术实现

### 2.3.1 核心架构设计

#### 命令执行器结构
```rust
#[derive(Debug)]
pub struct CommandExecutor {
    shell_manager: Arc<Mutex<ShellManager>>,
    ai_service: Arc<dyn AIService>,
    security_checker: SecurityChecker,
    history_manager: CommandHistoryManager,
    context: ExecutionContext,
}

#[derive(Debug, Clone)]
pub struct ExecutionContext {
    pub current_directory: String,
    pub environment_vars: HashMap<String, String>,
    pub user_id: String,
    pub session_id: String,
}
```

#### 命令类型定义
```rust
#[derive(Debug, Clone)]
pub enum CommandType {
    // 普通Shell命令
    Shell(ShellCommand),
    // AI增强命令
    AICommand(AICommandType),
    // 内置命令
    Builtin(BuiltinCommand),
}

#[derive(Debug, Clone)]
pub enum AICommandType {
    // @command 自然语言转命令
    NaturalLanguage { prompt: String },
    // @model AI对话
    ModelChat { query: String },
    // @explain 命令解释
    CommandExplain { command: String },
}

#[derive(Debug, Clone)]
pub struct ShellCommand {
    pub executable: String,
    pub args: Vec<String>,
    pub working_dir: Option<String>,
    pub env_vars: HashMap<String, String>,
    pub stdin_input: Option<String>,
}
```

#### 命令解析器
```rust
pub struct CommandParser {
    ai_prefixes: HashMap<String, AICommandType>,
}

impl CommandParser {
    pub fn new() -> Self {
        let mut ai_prefixes = HashMap::new();
        ai_prefixes.insert("@command".to_string(), AICommandType::NaturalLanguage { prompt: String::new() });
        ai_prefixes.insert("@model".to_string(), AICommandType::ModelChat { query: String::new() });
        ai_prefixes.insert("@explain".to_string(), AICommandType::CommandExplain { command: String::new() });

        Self { ai_prefixes }
    }

    pub fn parse(&self, input: &str) -> Result<CommandType, ParseError> {
        let trimmed = input.trim();

        // 检查AI命令前缀
        if let Some(ai_command) = self.parse_ai_command(trimmed)? {
            return Ok(CommandType::AICommand(ai_command));
        }

        // 检查内置命令
        if let Some(builtin) = self.parse_builtin_command(trimmed)? {
            return Ok(CommandType::Builtin(builtin));
        }

        // 解析普通Shell命令
        self.parse_shell_command(trimmed)
            .map(CommandType::Shell)
    }

    fn parse_ai_command(&self, input: &str) -> Result<Option<AICommandType>, ParseError> {
        for (prefix, _) in &self.ai_prefixes {
            if input.starts_with(prefix) {
                let content = input[prefix.len()..].trim();
                return Ok(Some(match prefix.as_str() {
                    "@command" => AICommandType::NaturalLanguage {
                        prompt: content.to_string()
                    },
                    "@model" => AICommandType::ModelChat {
                        query: content.to_string()
                    },
                    "@explain" => AICommandType::CommandExplain {
                        command: content.to_string()
                    },
                    _ => return Err(ParseError::UnknownAICommand(prefix.clone())),
                }));
            }
        }
        Ok(None)
    }

    fn parse_shell_command(&self, input: &str) -> Result<ShellCommand, ParseError> {
        // 使用 shell-words 库解析命令和参数
        let words = shell_words::split(input)
            .map_err(|e| ParseError::InvalidSyntax(e.to_string()))?;

        if words.is_empty() {
            return Err(ParseError::EmptyCommand);
        }

        let executable = words[0].clone();
        let args = words[1..].to_vec();

        Ok(ShellCommand {
            executable,
            args,
            working_dir: None,
            env_vars: HashMap::new(),
            stdin_input: None,
        })
    }
}
```

### 2.3.2 AI 功能集成

#### AI 命令处理器
```rust
pub struct AICommandProcessor {
    nlp_service: Arc<dyn NLPService>,
    model_service: Arc<dyn ModelService>,
    command_explainer: CommandExplainer,
}

impl AICommandProcessor {
    pub async fn process_natural_language(&self, prompt: &str, context: &ExecutionContext) -> Result<ProcessedCommand, AIError> {
        // 1. 使用NLP服务解析自然语言
        let parsed_intent = self.nlp_service.parse_intent(prompt, context).await?;

        // 2. 生成对应的命令
        let generated_command = self.nlp_service.generate_command(parsed_intent, context).await?;

        // 3. 安全检查
        let safety_check = self.check_command_safety(&generated_command).await?;

        Ok(ProcessedCommand {
            original_prompt: prompt.to_string(),
            generated_command,
            safety_check,
            confidence_score: parsed_intent.confidence,
            explanation: parsed_intent.explanation,
        })
    }

    pub async fn process_model_chat(&self, query: &str, context: &ExecutionContext) -> Result<ChatResponse, AIError> {
        // AI对话处理
        let response = self.model_service.chat(query, context).await?;

        Ok(ChatResponse {
            query: query.to_string(),
            response: response.content,
            suggestions: response.suggestions,
            follow_up_questions: response.follow_up_questions,
        })
    }

    pub async fn explain_command(&self, command: &str) -> Result<CommandExplanation, AIError> {
        self.command_explainer.explain(command)
            .await
            .map_err(AIError::ExplanationFailed)
    }

    async fn check_command_safety(&self, command: &ShellCommand) -> Result<SafetyCheck, AIError> {
        // 实现安全检查逻辑
        Ok(SafetyCheck {
            is_safe: true,
            risk_level: RiskLevel::Low,
            warnings: vec![],
            recommendations: vec![],
        })
    }
}
```

#### 数据结构定义
```rust
#[derive(Debug, Clone)]
pub struct ProcessedCommand {
    pub original_prompt: String,
    pub generated_command: ShellCommand,
    pub safety_check: SafetyCheck,
    pub confidence_score: f32,
    pub explanation: String,
}

#[derive(Debug, Clone)]
pub struct ChatResponse {
    pub query: String,
    pub response: String,
    pub suggestions: Vec<String>,
    pub follow_up_questions: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct CommandExplanation {
    pub command: String,
    pub overall_description: String,
    pub component_explanations: Vec<ComponentExplanation>,
    pub examples: Vec<String>,
    pub common_pitfalls: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct ComponentExplanation {
    pub component: String,
    pub description: String,
    pub purpose: String,
}
```

### 2.3.3 安全检查机制

#### 安全检查器
```rust
pub struct SecurityChecker {
    dangerous_commands: HashSet<String>,
    risk_patterns: Vec<RiskPattern>,
    user_permissions: UserPermissions,
}

impl SecurityChecker {
    pub fn new() -> Self {
        let mut dangerous_commands = HashSet::new();
        dangerous_commands.insert("rm".to_string());
        dangerous_commands.insert("rmdir".to_string());
        dangerous_commands.insert("dd".to_string());
        dangerous_commands.insert("mkfs".to_string());
        dangerous_commands.insert("fdisk".to_string());
        dangerous_commands.insert("shutdown".to_string());
        dangerous_commands.insert("reboot".to_string());

        let risk_patterns = vec![
            RiskPattern {
                pattern: Regex::new(r"rm\s+.*-rf").unwrap(),
                risk_level: RiskLevel::High,
                description: "Recursive force deletion".to_string(),
            },
            RiskPattern {
                pattern: Regex::new(r"sudo\s+").unwrap(),
                risk_level: RiskLevel::Medium,
                description: "Elevated privileges required".to_string(),
            },
            RiskPattern {
                pattern: Regex::new(r">\s*/dev/").unwrap(),
                risk_level: RiskLevel::High,
                description: "Writing to device files".to_string(),
            },
        ];

        Self {
            dangerous_commands,
            risk_patterns,
            user_permissions: UserPermissions::default(),
        }
    }

    pub fn check_command_safety(&self, command: &ShellCommand) -> SafetyCheck {
        let mut warnings = Vec::new();
        let mut risk_level = RiskLevel::Low;

        // 检查危险命令
        if self.dangerous_commands.contains(&command.executable) {
            warnings.push(format!("'{}' is a potentially dangerous command", command.executable));
            risk_level = risk_level.max(RiskLevel::Medium);
        }

        // 检查风险模式
        let full_command = format!("{} {}", command.executable, command.args.join(" "));
        for pattern in &self.risk_patterns {
            if pattern.pattern.is_match(&full_command) {
                warnings.push(pattern.description.clone());
                risk_level = risk_level.max(pattern.risk_level);
            }
        }

        // 检查权限要求
        if command.executable == "sudo" || command.args.contains(&"sudo".to_string()) {
            warnings.push("This command requires elevated privileges".to_string());
            risk_level = risk_level.max(RiskLevel::Medium);
        }

        SafetyCheck {
            is_safe: risk_level < RiskLevel::High,
            risk_level,
            warnings,
            recommendations: self.generate_recommendations(&full_command, risk_level),
        }
    }

    fn generate_recommendations(&self, command: &str, risk_level: RiskLevel) -> Vec<String> {
        let mut recommendations = Vec::new();

        match risk_level {
            RiskLevel::High => {
                recommendations.push("Consider backing up important data before proceeding".to_string());
                recommendations.push("Double-check the command parameters".to_string());
                recommendations.push("Test in a safe environment first".to_string());
            },
            RiskLevel::Medium => {
                recommendations.push("Review the command carefully before execution".to_string());
                recommendations.push("Ensure you have necessary permissions".to_string());
            },
            _ => {}
        }

        recommendations
    }
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone)]
pub struct SafetyCheck {
    pub is_safe: bool,
    pub risk_level: RiskLevel,
    pub warnings: Vec<String>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct RiskPattern {
    pub pattern: Regex,
    pub risk_level: RiskLevel,
    pub description: String,
}
```

### 2.3.4 命令执行流程

#### 主执行器实现
```rust
impl CommandExecutor {
    pub async fn execute(&mut self, input: &str) -> Result<ExecutionResult, ExecutionError> {
        // 1. 解析命令
        let command_type = self.parser.parse(input)?;

        // 2. 根据命令类型分发处理
        match command_type {
            CommandType::Shell(shell_cmd) => {
                self.execute_shell_command(shell_cmd).await
            },
            CommandType::AICommand(ai_cmd) => {
                self.execute_ai_command(ai_cmd).await
            },
            CommandType::Builtin(builtin_cmd) => {
                self.execute_builtin_command(builtin_cmd).await
            },
        }
    }

    async fn execute_shell_command(&mut self, command: ShellCommand) -> Result<ExecutionResult, ExecutionError> {
        // 1. 安全检查
        let safety_check = self.security_checker.check_command_safety(&command);

        // 2. 如果存在风险，请求用户确认
        if !safety_check.is_safe {
            return Ok(ExecutionResult::RequiresConfirmation {
                command: command.clone(),
                safety_check,
            });
        }

        // 3. 添加到历史记录
        self.history_manager.add_command(&command).await?;

        // 4. 执行命令
        let execution_id = Uuid::new_v4().to_string();
        let start_time = Instant::now();

        let result = self.shell_manager
            .lock()
            .await
            .execute_command(&self.context.session_id, &command)
            .await;

        let duration = start_time.elapsed();

        // 5. 记录执行结果
        let execution_record = ExecutionRecord {
            id: execution_id,
            command: command.clone(),
            start_time: SystemTime::now(),
            duration,
            result: result.clone(),
        };

        self.history_manager.add_execution_record(execution_record).await?;

        // 6. 返回结果
        match result {
            Ok(output) => Ok(ExecutionResult::Success {
                command,
                output,
                duration,
            }),
            Err(e) => Ok(ExecutionResult::Error {
                command,
                error: e,
                duration,
            }),
        }
    }

    async fn execute_ai_command(&mut self, ai_command: AICommandType) -> Result<ExecutionResult, ExecutionError> {
        match ai_command {
            AICommandType::NaturalLanguage { prompt } => {
                let processed = self.ai_processor
                    .process_natural_language(&prompt, &self.context)
                    .await?;

                Ok(ExecutionResult::AIProcessed {
                    original_input: prompt,
                    processed_command: processed,
                })
            },
            AICommandType::ModelChat { query } => {
                let response = self.ai_processor
                    .process_model_chat(&query, &self.context)
                    .await?;

                Ok(ExecutionResult::ChatResponse(response))
            },
            AICommandType::CommandExplain { command } => {
                let explanation = self.ai_processor
                    .explain_command(&command)
                    .await?;

                Ok(ExecutionResult::CommandExplanation(explanation))
            },
        }
    }
}
```

#### 执行结果类型
```rust
#[derive(Debug, Clone)]
pub enum ExecutionResult {
    Success {
        command: ShellCommand,
        output: CommandOutput,
        duration: Duration,
    },
    Error {
        command: ShellCommand,
        error: ShellError,
        duration: Duration,
    },
    RequiresConfirmation {
        command: ShellCommand,
        safety_check: SafetyCheck,
    },
    AIProcessed {
        original_input: String,
        processed_command: ProcessedCommand,
    },
    ChatResponse(ChatResponse),
    CommandExplanation(CommandExplanation),
}

#[derive(Debug, Clone)]
pub struct CommandOutput {
    pub stdout: String,
    pub stderr: String,
    pub exit_code: i32,
}

#[derive(Debug, Clone)]
pub struct ExecutionRecord {
    pub id: String,
    pub command: ShellCommand,
    pub start_time: SystemTime,
    pub duration: Duration,
    pub result: Result<CommandOutput, ShellError>,
}
```

### 2.3.5 历史记录管理

#### 命令历史管理器
```rust
pub struct CommandHistoryManager {
    storage: Arc<dyn HistoryStorage>,
    max_history_size: usize,
    session_id: String,
}

impl CommandHistoryManager {
    pub fn new(storage: Arc<dyn HistoryStorage>, session_id: String) -> Self {
        Self {
            storage,
            max_history_size: 10000,
            session_id,
        }
    }

    pub async fn add_command(&mut self, command: &ShellCommand) -> Result<(), HistoryError> {
        let entry = HistoryEntry {
            id: Uuid::new_v4().to_string(),
            session_id: self.session_id.clone(),
            command: command.clone(),
            timestamp: SystemTime::now(),
        };

        self.storage.add_entry(entry).await?;
        self.cleanup_old_entries().await?;

        Ok(())
    }

    pub async fn search_history(&self, query: &str, limit: usize) -> Result<Vec<HistoryEntry>, HistoryError> {
        self.storage.search_entries(query, limit).await
    }

    pub async fn get_recent_commands(&self, limit: usize) -> Result<Vec<HistoryEntry>, HistoryError> {
        self.storage.get_recent_entries(&self.session_id, limit).await
    }

    async fn cleanup_old_entries(&mut self) -> Result<(), HistoryError> {
        let total_count = self.storage.count_entries(&self.session_id).await?;

        if total_count > self.max_history_size {
            let excess = total_count - self.max_history_size;
            self.storage.remove_oldest_entries(&self.session_id, excess).await?;
        }

        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct HistoryEntry {
    pub id: String,
    pub session_id: String,
    pub command: ShellCommand,
    pub timestamp: SystemTime,
}
```

## 🔧 集成实现

### 2.3.6 Tauri 命令接口

```rust
#[tauri::command]
pub async fn execute_command(
    app_state: tauri::State<'_, AppState>,
    terminal_id: String,
    input: String,
) -> Result<serde_json::Value, String> {
    let mut executor = app_state.command_executors
        .lock()
        .await
        .get_mut(&terminal_id)
        .ok_or("Terminal not found")?
        .clone();

    let result = executor.execute(&input)
        .await
        .map_err(|e| format!("Execution failed: {}", e))?;

    Ok(serde_json::to_value(result)
        .map_err(|e| format!("Serialization failed: {}", e))?)
}

#[tauri::command]
pub async fn confirm_dangerous_command(
    app_state: tauri::State<'_, AppState>,
    terminal_id: String,
    command_id: String,
    confirmed: bool,
) -> Result<serde_json::Value, String> {
    if !confirmed {
        return Ok(serde_json::json!({ "cancelled": true }));
    }

    // 执行被确认的危险命令
    let mut executor = app_state.command_executors
        .lock()
        .await
        .get_mut(&terminal_id)
        .ok_or("Terminal not found")?
        .clone();

    // 从待确认队列中获取命令并执行
    if let Some(pending_command) = executor.get_pending_command(&command_id) {
        let result = executor.execute_confirmed_command(pending_command)
            .await
            .map_err(|e| format!("Execution failed: {}", e))?;

        Ok(serde_json::to_value(result)
            .map_err(|e| format!("Serialization failed: {}", e))?)
    } else {
        Err("Command not found in pending queue".to_string())
    }
}

#[tauri::command]
pub async fn get_command_history(
    app_state: tauri::State<'_, AppState>,
    terminal_id: String,
    limit: Option<usize>,
) -> Result<Vec<serde_json::Value>, String> {
    let executor = app_state.command_executors
        .lock()
        .await
        .get(&terminal_id)
        .ok_or("Terminal not found")?
        .clone();

    let history = executor.get_command_history(limit.unwrap_or(100))
        .await
        .map_err(|e| format!("Failed to get history: {}", e))?;

    history.into_iter()
        .map(|entry| serde_json::to_value(entry)
            .map_err(|e| format!("Serialization failed: {}", e)))
        .collect()
}

#[tauri::command]
pub async fn search_command_history(
    app_state: tauri::State<'_, AppState>,
    terminal_id: String,
    query: String,
    limit: Option<usize>,
) -> Result<Vec<serde_json::Value>, String> {
    let executor = app_state.command_executors
        .lock()
        .await
        .get(&terminal_id)
        .ok_or("Terminal not found")?
        .clone();

    let results = executor.search_command_history(&query, limit.unwrap_or(50))
        .await
        .map_err(|e| format!("Search failed: {}", e))?;

    results.into_iter()
        .map(|entry| serde_json::to_value(entry)
            .map_err(|e| format!("Serialization failed: {}", e)))
        .collect()
}
```

### 2.3.7 错误处理

#### 错误类型定义
```rust
#[derive(Debug, thiserror::Error)]
pub enum ExecutionError {
    #[error("Parse error: {0}")]
    ParseError(#[from] ParseError),

    #[error("AI service error: {0}")]
    AIError(#[from] AIError),

    #[error("Shell error: {0}")]
    ShellError(#[from] ShellError),

    #[error("Security violation: {message}")]
    SecurityViolation { message: String },

    #[error("History error: {0}")]
    HistoryError(#[from] HistoryError),

    #[error("Context error: {message}")]
    ContextError { message: String },
}

#[derive(Debug, thiserror::Error)]
pub enum ParseError {
    #[error("Empty command")]
    EmptyCommand,

    #[error("Invalid syntax: {0}")]
    InvalidSyntax(String),

    #[error("Unknown AI command: {0}")]
    UnknownAICommand(String),

    #[error("Missing required argument: {0}")]
    MissingArgument(String),
}

#[derive(Debug, thiserror::Error)]
pub enum AIError {
    #[error("Service unavailable")]
    ServiceUnavailable,

    #[error("Model loading failed: {reason}")]
    ModelLoadFailed { reason: String },

    #[error("Processing failed: {reason}")]
    ProcessingFailed { reason: String },

    #[error("Explanation failed: {0}")]
    ExplanationFailed(String),
}
```

## ✅ 完成标准

### 2.3.1 功能验收标准
- [ ] 基础Shell命令正常执行
- [ ] @command 自然语言转命令功能可用
- [ ] @model AI对话功能正常
- [ ] @explain 命令解释准确
- [ ] 危险命令识别和确认机制工作正常
- [ ] 命令历史记录和搜索功能完整
- [ ] 错误处理完善，用户体验良好

### 2.3.2 性能标准
- [ ] 普通命令执行延迟 < 100ms
- [ ] AI命令处理时间 < 3s (本地模型)
- [ ] 历史记录搜索响应时间 < 200ms
- [ ] 内存占用增长控制在合理范围

### 2.3.3 安全标准
- [ ] 所有危险命令都能被正确识别
- [ ] 安全检查机制无漏洞
- [ ] 用户确认流程完整
- [ ] 操作日志记录完整

## 📋 子任务清单

### Day 1: 核心架构和基础功能
- [ ] 2.3.1 设计命令执行器架构 (2小时)
- [ ] 2.3.2 实现命令解析器 (3小时)
- [ ] 2.3.3 实现基础Shell命令执行 (3小时)

### Day 2: AI功能和安全机制
- [ ] 2.3.4 实现AI命令处理器 (3小时)
- [ ] 2.3.5 实现安全检查机制 (2小时)
- [ ] 2.3.6 实现命令历史管理 (2小时)
- [ ] 2.3.7 集成测试和调试 (1小时)

## 🔗 相关文件

### 需要创建的文件
- `src-tauri/src/terminal/commands.rs` - 命令执行器实现
- `src-tauri/src/terminal/shell_commands.rs` - Shell命令处理
- `src-tauri/src/terminal/security.rs` - 安全检查器
- `src-tauri/src/terminal/history.rs` - 历史记录管理

### 需要修改的文件
- `src-tauri/src/terminal/mod.rs` - 添加新模块导出
- `src-tauri/src/lib.rs` - 添加Tauri命令导出
- `src-tauri/src/main.rs` - 注册新的Tauri命令

## 🧪 测试计划

### 单元测试
- [ ] 命令解析器测试
- [ ] 安全检查器测试
- [ ] AI命令处理测试
- [ ] 历史记录管理测试

### 集成测试
- [ ] 端到端命令执行测试
- [ ] AI功能集成测试
- [ ] 安全机制集成测试

### 性能测试
- [ ] 命令执行性能测试
- [ ] 大量历史记录性能测试
- [ ] 并发执行测试

## 📚 技术参考

### 依赖库
- `shell-words` - Shell命令解析
- `regex` - 正则表达式匹配
- `uuid` - 唯一ID生成
- `thiserror` - 错误处理
- `serde` - 序列化/反序列化
- `tokio` - 异步运行时

### 相关文档
- [Shell Command Parsing](https://docs.rs/shell-words/)
- [Regex Pattern Matching](https://docs.rs/regex/)
- [Async Rust Programming](https://rust-lang.github.io/async-book/)
- [Tauri Command System](https://tauri.app/v1/guides/features/command)

---

**任务负责人**: AI Assistant
**当前状态**: 📋 已规划，准备开始实现
