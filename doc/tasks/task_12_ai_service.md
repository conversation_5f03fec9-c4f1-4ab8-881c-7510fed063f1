# Task 4.1: AI 服务架构

## 任务概述
- **任务ID**: Task 4.1
- **任务名称**: AI 服务架构
- **优先级**: 高
- **预计时间**: 2 天
- **负责人**: 待分配
- **状态**: ✅ 已完成

## 任务描述
设计 TAgent 的 AI 服务接口和抽象层，为后续的本地模型集成、云端模型接口和自然语言处理功能提供统一的架构基础。

## 依赖关系
- **前置任务**: Task 1.3 (项目架构设计)
- **后续任务**: Task 4.2 (本地模型集成), Task 4.3 (云端模型接口)

## 具体任务

### 4.1.1 AI 服务抽象设计 (4 小时)
- 定义 AI 服务的统一接口
- 设计模型提供者抽象层
- 制定服务生命周期管理

#### AI 服务架构图

```
┌─────────────────────────────────────────────────────────┐
│                    AI 服务架构                            │
├─────────────────────────────────────────────────────────┤
│  前端 AI 服务层                                          │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐│
│  │ AI Store    │ AI Service  │ AI Types    │ AI Utils    ││
│  │ - State     │ - Commands  │ - Models    │ - Parsers   ││
│  │ - Actions   │ - Events    │ - Requests  │ - Formatters││
│  │ - Config    │ - Cache     │ - Responses │ - Validators││
│  └─────────────┴─────────────┴─────────────┴─────────────┘│
├─────────────────────────────────────────────────────────┤
│  Tauri IPC 层                                            │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ AI Commands                                         │ │
│  │ - process_natural_language                          │ │
│  │ - explain_command                                   │ │
│  │ - ai_chat                                          │ │
│  │ - get_ai_models                                    │ │
│  │ - switch_ai_model                                  │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  Rust AI 服务层                                          │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐│
│  │ AI Service  │ Model Mgr   │ NLP Engine  │ AI Utils    ││
│  │ - Interface │ - Registry  │ - Parser    │ - Config    ││
│  │ - Manager   │ - Factory   │ - Processor │ - Cache     ││
│  │ - Router    │ - Lifecycle │ - Context   │ - Logger    ││
│  └─────────────┴─────────────┴─────────────┴─────────────┘│
├─────────────────────────────────────────────────────────┤
│  模型提供者层                                             │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐│
│  │ Local Model │ Cloud API   │ Hybrid Mode │ Mock Model  ││
│  │ - Ollama    │ - OpenAI    │ - Fallback  │ - Testing   ││
│  │ - Llama.cpp │ - Claude    │ - Load Bal  │ - Demo      ││
│  │ - GGML      │ - Gemini    │ - Cache     │ - Offline   ││
│  └─────────────┴─────────────┴─────────────┴─────────────┘│
└─────────────────────────────────────────────────────────┘
```

#### 核心接口定义

```rust
// AI 服务抽象接口
pub trait AIService: Send + Sync {
    async fn process_command(&self, input: &str, context: &AIContext) -> Result<AIResponse, AIError>;
    async fn explain_command(&self, command: &str) -> Result<String, AIError>;
    async fn chat(&self, message: &str, context: &ChatContext) -> Result<String, AIError>;
    async fn health_check(&self) -> Result<(), AIError>;
    fn get_capabilities(&self) -> AICapabilities;
}

// 模型提供者接口
pub trait ModelProvider: Send + Sync {
    async fn initialize(&mut self, config: ModelConfig) -> Result<(), AIError>;
    async fn generate(&self, prompt: &str, options: &GenerationOptions) -> Result<String, AIError>;
    async fn cleanup(&self) -> Result<(), AIError>;
    fn get_model_info(&self) -> ModelInfo;
    fn is_available(&self) -> bool;
}

// AI 服务管理器
pub struct AIServiceManager {
    providers: HashMap<String, Box<dyn ModelProvider>>,
    current_provider: Option<String>,
    config: AIConfig,
    cache: Arc<RwLock<AICache>>,
}
```

### 4.1.2 数据结构设计 (3 小时)
- 定义 AI 请求/响应数据结构
- 设计配置和状态管理
- 制定错误处理策略

#### 核心数据结构

```rust
// AI 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIConfig {
    pub default_provider: String,
    pub providers: HashMap<String, ProviderConfig>,
    pub fallback_enabled: bool,
    pub cache_enabled: bool,
    pub timeout_ms: u64,
    pub max_retries: u32,
}

// 提供者配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderConfig {
    pub provider_type: ProviderType,
    pub model_name: String,
    pub api_endpoint: Option<String>,
    pub api_key: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub custom_params: HashMap<String, serde_json::Value>,
}

// AI 上下文
#[derive(Debug, Clone)]
pub struct AIContext {
    pub session_id: String,
    pub current_directory: String,
    pub shell_type: String,
    pub environment: HashMap<String, String>,
    pub command_history: Vec<String>,
    pub user_preferences: UserPreferences,
}

// AI 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIResponse {
    pub response_type: ResponseType,
    pub content: String,
    pub confidence: f32,
    pub suggested_commands: Vec<SuggestedCommand>,
    pub requires_confirmation: bool,
    pub metadata: ResponseMetadata,
}

// 建议命令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuggestedCommand {
    pub command: String,
    pub description: String,
    pub risk_level: RiskLevel,
    pub estimated_duration: Option<String>,
}

// 错误类型
#[derive(Debug, thiserror::Error)]
pub enum AIError {
    #[error("Provider not available: {0}")]
    ProviderNotAvailable(String),
    #[error("Model initialization failed: {0}")]
    InitializationFailed(String),
    #[error("Generation failed: {0}")]
    GenerationFailed(String),
    #[error("Timeout: operation took too long")]
    Timeout,
    #[error("Configuration error: {0}")]
    ConfigError(String),
    #[error("Network error: {0}")]
    NetworkError(String),
}
```

### 4.1.3 服务实现框架 (5 小时)
- 实现 AI 服务管理器
- 创建模型提供者注册机制
- 实现基础的生命周期管理

#### AI 服务管理器实现

```rust
impl AIServiceManager {
    pub fn new(config: AIConfig) -> Self {
        Self {
            providers: HashMap::new(),
            current_provider: None,
            config,
            cache: Arc::new(RwLock::new(AICache::new())),
        }
    }

    pub async fn initialize(&mut self) -> Result<(), AIError> {
        // 初始化配置的提供者
        for (name, config) in &self.config.providers {
            let provider = self.create_provider(config).await?;
            self.providers.insert(name.clone(), provider);
        }

        // 设置默认提供者
        self.current_provider = Some(self.config.default_provider.clone());

        Ok(())
    }

    pub async fn process_natural_language(
        &self,
        input: &str,
        context: &AIContext,
    ) -> Result<AIResponse, AIError> {
        let provider = self.get_current_provider()?;

        // 构建提示词
        let prompt = self.build_command_prompt(input, context);

        // 尝试从缓存获取
        if self.config.cache_enabled {
            if let Some(cached) = self.get_cached_response(&prompt).await {
                return Ok(cached);
            }
        }

        // 调用模型生成
        let response = provider.generate(&prompt, &self.get_generation_options()).await?;

        // 解析响应
        let ai_response = self.parse_command_response(&response, input)?;

        // 缓存结果
        if self.config.cache_enabled {
            self.cache_response(&prompt, &ai_response).await;
        }

        Ok(ai_response)
    }

    pub async fn switch_provider(&mut self, provider_name: &str) -> Result<(), AIError> {
        if !self.providers.contains_key(provider_name) {
            return Err(AIError::ProviderNotAvailable(provider_name.to_string()));
        }

        self.current_provider = Some(provider_name.to_string());
        Ok(())
    }

    async fn create_provider(&self, config: &ProviderConfig) -> Result<Box<dyn ModelProvider>, AIError> {
        match config.provider_type {
            ProviderType::Local => {
                let provider = LocalModelProvider::new(config.clone()).await?;
                Ok(Box::new(provider))
            }
            ProviderType::OpenAI => {
                let provider = OpenAIProvider::new(config.clone()).await?;
                Ok(Box::new(provider))
            }
            ProviderType::Claude => {
                let provider = ClaudeProvider::new(config.clone()).await?;
                Ok(Box::new(provider))
            }
            ProviderType::Mock => {
                let provider = MockProvider::new(config.clone()).await?;
                Ok(Box::new(provider))
            }
        }
    }

    fn build_command_prompt(&self, input: &str, context: &AIContext) -> String {
        format!(
            r#"你是一个专业的命令行助手。根据用户的自然语言描述，生成对应的命令行命令。

当前环境信息：
- 操作系统: {}
- Shell: {}
- 当前目录: {}
- 最近命令: {:?}

用户请求: {}

请生成准确的命令，并提供简要说明。如果是危险操作，请明确标注风险等级。

输出格式：
```json
{{
    "command": "具体命令",
    "description": "命令说明",
    "risk_level": "low|medium|high",
    "requires_confirmation": true/false
}}
```"#,
            std::env::consts::OS,
            context.shell_type,
            context.current_directory,
            context.command_history.iter().rev().take(3).collect::<Vec<_>>(),
            input
        )
    }
}
```

### 4.1.4 配置和存储 (2 小时)
- 设计 AI 配置文件格式
- 实现配置的持久化存储
- 创建配置验证机制

#### 配置文件示例

```toml
# ~/.tagent/ai_config.toml

[ai]
default_provider = "local"
fallback_enabled = true
cache_enabled = true
timeout_ms = 30000
max_retries = 3

[providers.local]
provider_type = "Local"
model_name = "llama3.2:3b"
api_endpoint = "http://localhost:11434"
max_tokens = 2048
temperature = 0.7

[providers.openai]
provider_type = "OpenAI"
model_name = "gpt-3.5-turbo"
api_endpoint = "https://api.openai.com/v1"
api_key = "${OPENAI_API_KEY}"
max_tokens = 1024
temperature = 0.5

[providers.claude]
provider_type = "Claude"
model_name = "claude-3-haiku-20240307"
api_endpoint = "https://api.anthropic.com"
api_key = "${ANTHROPIC_API_KEY}"
max_tokens = 1024
temperature = 0.5

[cache]
max_size = 1000
ttl_hours = 24
storage_path = "~/.tagent/cache"

[security]
dangerous_commands = [
    "rm -rf",
    "sudo rm",
    "format",
    "mkfs",
    "dd if=",
]
confirmation_required = true
sandbox_mode = false
```

### 4.1.5 前端集成接口 (2 小时)
- 创建前端 AI Store
- 实现 Tauri 命令包装
- 设计状态管理

#### 前端 AI Store

```typescript
// src/stores/aiStore.ts
import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/tauri';

export interface AIModel {
  id: string;
  name: string;
  provider: string;
  isAvailable: boolean;
  capabilities: string[];
}

interface AIState {
  // 状态
  currentModel: AIModel | null;
  availableModels: AIModel[];
  isProcessing: boolean;
  lastResponse: AIResponse | null;
  chatHistory: ChatMessage[];

  // 配置
  config: AIConfig;

  // 操作
  processCommand: (input: string) => Promise<AIResponse>;
  explainCommand: (command: string) => Promise<string>;
  sendChatMessage: (message: string) => Promise<string>;
  switchModel: (modelId: string) => Promise<void>;
  loadAvailableModels: () => Promise<void>;
  updateConfig: (config: Partial<AIConfig>) => Promise<void>;
}

export const useAIStore = create<AIState>((set, get) => ({
  // 初始状态
  currentModel: null,
  availableModels: [],
  isProcessing: false,
  lastResponse: null,
  chatHistory: [],
  config: {
    defaultProvider: 'local',
    fallbackEnabled: true,
    cacheEnabled: true,
    timeoutMs: 30000,
  },

  // 处理自然语言命令
  processCommand: async (input: string) => {
    set({ isProcessing: true });
    try {
      const context = {
        sessionId: crypto.randomUUID(),
        currentDirectory: await invoke('get_current_directory'),
        shellType: await invoke('get_shell_type'),
        environment: await invoke('get_environment_variables'),
        commandHistory: [], // TODO: 从终端 store 获取
        userPreferences: {},
      };

      const response = await invoke<AIResponse>('ai_process_natural_language', {
        input,
        context,
      });

      set({ lastResponse: response, isProcessing: false });
      return response;
    } catch (error) {
      set({ isProcessing: false });
      throw error;
    }
  },

  // 解释命令
  explainCommand: async (command: string) => {
    return await invoke<string>('ai_explain_command', { command });
  },

  // AI 对话
  sendChatMessage: async (message: string) => {
    const response = await invoke<string>('ai_chat', {
      message,
      context: {
        history: get().chatHistory,
      },
    });

    set(state => ({
      chatHistory: [
        ...state.chatHistory,
        { role: 'user', content: message },
        { role: 'assistant', content: response },
      ],
    }));

    return response;
  },

  // 切换模型
  switchModel: async (modelId: string) => {
    await invoke('ai_switch_model', { modelId });
    const models = get().availableModels;
    const newModel = models.find(m => m.id === modelId);
    set({ currentModel: newModel || null });
  },

  // 加载可用模型
  loadAvailableModels: async () => {
    const models = await invoke<AIModel[]>('ai_get_available_models');
    set({ availableModels: models });
  },

  // 更新配置
  updateConfig: async (newConfig: Partial<AIConfig>) => {
    const updatedConfig = { ...get().config, ...newConfig };
    await invoke('ai_update_config', { config: updatedConfig });
    set({ config: updatedConfig });
  },
}));
```

#### Tauri 命令实现

```rust
// src-tauri/src/ai/commands.rs
use tauri::command;
use super::service::AIServiceManager;
use crate::ai::{AIContext, AIResponse, AIConfig};

#[command]
pub async fn ai_process_natural_language(
    input: String,
    context: AIContext,
    state: tauri::State<'_, AIServiceManager>,
) -> Result<AIResponse, String> {
    state
        .process_natural_language(&input, &context)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn ai_explain_command(
    command: String,
    state: tauri::State<'_, AIServiceManager>,
) -> Result<String, String> {
    state
        .explain_command(&command)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn ai_chat(
    message: String,
    context: serde_json::Value,
    state: tauri::State<'_, AIServiceManager>,
) -> Result<String, String> {
    // TODO: 实现聊天功能
    Ok("Chat功能将在下个任务中实现".to_string())
}

#[command]
pub async fn ai_get_available_models(
    state: tauri::State<'_, AIServiceManager>,
) -> Result<Vec<AIModel>, String> {
    state
        .get_available_models()
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn ai_switch_model(
    model_id: String,
    state: tauri::State<'_, AIServiceManager>,
) -> Result<(), String> {
    state
        .switch_provider(&model_id)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn ai_update_config(
    config: AIConfig,
    state: tauri::State<'_, AIServiceManager>,
) -> Result<(), String> {
    state
        .update_config(config)
        .await
        .map_err(|e| e.to_string())
}
```

## 验收标准

### 功能验收
- [ ] AI 服务抽象接口定义完成
- [ ] 模型提供者注册机制实现
- [ ] 基础配置管理功能完成
- [ ] 前端 AI Store 集成完成
- [ ] Tauri 命令接口实现完成

### 代码质量
- [ ] 代码通过所有 lint 检查
- [ ] 核心接口有完整的文档注释
- [ ] 错误处理机制完善
- [ ] 配置验证机制完成

### 测试要求
- [ ] 单元测试覆盖率 > 80%
- [ ] 模拟提供者测试通过
- [ ] 配置加载测试通过
- [ ] 错误处理测试通过

## 后续任务
- Task 4.2: 本地模型集成
- Task 4.3: 云端模型接口
- Task 4.4: 自然语言处理
- Task 4.5: AI 对话功能

## 技术债务
- 需要在后续任务中实现具体的模型提供者
- 缓存机制需要在性能优化阶段进一步完善
- 安全检查机制需要在安全模块中补充
