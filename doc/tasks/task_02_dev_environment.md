# Task 1.2: 开发环境配置

## 任务概述
- **任务ID**: Task 1.2
- **任务名称**: 开发环境配置
- **优先级**: 高
- **预计时间**: 1 天
- **负责人**: 待分配
- **状态**: ⏳ 待开始

## 任务描述
配置完整的开发环境，包括代码规范、CI/CD 流程、调试工具和开发者工具链，确保团队开发的一致性和效率。

## 具体任务

### 1.2.1 代码规范配置 (2 小时)
- 配置 ESLint 和 Prettier
- 设置 Rust 代码格式化
- 配置 commit 规范

**ESLint 配置** (`.eslintrc.js`):
```javascript
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react', 'react-hooks'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'warn'
  }
};
```

**Prettier 配置** (`.prettierrc`):
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

**Rustfmt 配置** (`rustfmt.toml`):
```toml
max_width = 100
hard_tabs = false
tab_spaces = 4
edition = "2021"
```

### 1.2.2 Git 工作流配置 (1 小时)
- 设置 Git hooks
- 配置 conventional commits
- 创建 `.gitignore` 文件

**Pre-commit Hook**:
```bash
#!/bin/sh
npm run lint
npm run type-check
cargo fmt --check
cargo clippy -- -D warnings
```

**Conventional Commits 规范**:
- `feat:` 新功能
- `fix:` 修复 bug
- `docs:` 文档更新
- `style:` 代码格式化
- `refactor:` 重构
- `test:` 测试
- `chore:` 构建/工具相关

### 1.2.3 VS Code 开发环境 (1 小时)
- 配置 VS Code 设置
- 推荐扩展列表
- 调试配置

**VS Code 设置** (`.vscode/settings.json`):
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "rust-analyzer.checkOnSave.command": "clippy",
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[rust]": {
    "editor.defaultFormatter": "rust-lang.rust-analyzer"
  }
}
```

**推荐扩展** (`.vscode/extensions.json`):
```json
{
  "recommendations": [
    "rust-lang.rust-analyzer",
    "tauri-apps.tauri-vscode",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### 1.2.4 调试配置 (2 小时)
- 配置 Tauri 调试
- 设置 Chrome DevTools
- 配置 Rust 调试器

**Launch 配置** (`.vscode/launch.json`):
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Tauri Development Debug",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}",
      "program": "${workspaceFolder}/node_modules/@tauri-apps/cli/bin/tauri.js",
      "args": ["dev"],
      "console": "integratedTerminal"
    },
    {
      "name": "Rust Debug",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/src-tauri/target/debug/TAgent",
      "cwd": "${workspaceFolder}/src-tauri"
    }
  ]
}
```

### 1.2.5 CI/CD 配置 (2 小时)
- 设置 GitHub Actions
- 配置自动测试
- 设置构建流程

**GitHub Actions** (`.github/workflows/ci.yml`):
```yaml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      
    - name: Install dependencies
      run: npm ci
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Run TypeScript check
      run: npm run type-check
      
    - name: Run Rust clippy
      run: cd src-tauri && cargo clippy -- -D warnings
      
    - name: Run tests
      run: npm test
      
    - name: Build application
      run: npm run tauri build
```

## 验收标准

### 开发工具验收
- [ ] ESLint 和 Prettier 正常工作
- [ ] Rust clippy 和 rustfmt 配置正确
- [ ] VS Code 扩展安装完成
- [ ] 调试配置可用

### 代码质量验收
- [ ] Pre-commit hooks 正常工作
- [ ] 代码格式自动修正
- [ ] 类型检查通过
- [ ] 无 linting 错误

### CI/CD 验收
- [ ] GitHub Actions 流程运行成功
- [ ] 自动化测试通过
- [ ] 构建流程无错误
- [ ] PR 检查机制生效

## 开发脚本

**package.json scripts**:
```json
{
  "scripts": {
    "dev": "tauri dev",
    "build": "tauri build",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write src",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "prepare": "husky install"
  }
}
```

**Cargo.toml 额外配置**:
```toml
[profile.dev]
debug = true

[profile.release]
strip = true
lto = true
codegen-units = 1
panic = "abort"
```

## 工具链版本

### 必需版本
- **Node.js**: 18.0+
- **npm**: 9.0+
- **Rust**: 1.70+
- **Cargo**: 1.70+

### 推荐工具
- **VS Code**: 最新版本
- **Git**: 2.30+
- **Chrome**: 最新版本 (调试用)

## 环境变量配置

**开发环境变量** (`.env.development`):
```bash
VITE_APP_NAME=TAgent
VITE_APP_VERSION=0.1.0
VITE_DEBUG_MODE=true
RUST_LOG=debug
```

**生产环境变量** (`.env.production`):
```bash
VITE_APP_NAME=TAgent
VITE_APP_VERSION=0.1.0
VITE_DEBUG_MODE=false
RUST_LOG=info
```

## 风险与依赖

### 潜在风险
- **工具版本不兼容**: 确保所有工具版本匹配
- **操作系统差异**: 配置需要考虑跨平台兼容性
- **权限问题**: 某些 hooks 可能需要执行权限

### 前置依赖
- Task 1.1: 项目初始化完成
- 开发环境基础工具安装

### 后续影响
- 影响所有后续开发任务
- 代码质量保证基础

## 验证步骤

1. **代码规范验证**:
   ```bash
   npm run lint
   npm run format
   cargo fmt --check
   cargo clippy
   ```

2. **调试环境验证**:
   - 启动 VS Code 调试
   - 验证断点功能
   - 检查 DevTools 连接

3. **CI/CD 验证**:
   - 创建测试 PR
   - 验证 Actions 运行
   - 检查构建产物

## 完成标志
- [ ] 所有代码规范工具配置完成
- [ ] VS Code 开发环境就绪
- [ ] 调试配置验证成功
- [ ] CI/CD 流程运行正常
- [ ] 团队开发规范文档完成

---

**创建时间**: 2024-01-XX  
**最后更新**: 2024-01-XX  
**前置任务**: Task 1.1 项目初始化  
**下一个任务**: Task 1.3 项目架构设计 